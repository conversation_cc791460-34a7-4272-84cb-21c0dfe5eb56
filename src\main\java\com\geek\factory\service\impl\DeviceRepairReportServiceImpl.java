package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.dto.DeviceRepairReportDTO;
import com.geek.factory.dto.DeviceRepairReportQueryDTO;
import com.geek.factory.entity.DeviceRepairReport;
import com.geek.factory.mapper.DeviceRepairReportMapper;
import com.geek.factory.service.DeviceRepairReportService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备维修上报服务实现类
 */
@Service
public class DeviceRepairReportServiceImpl extends ServiceImpl<DeviceRepairReportMapper, DeviceRepairReport> implements DeviceRepairReportService {

    /**
     * 获取Mapper实例，供控制器使用
     */
    public DeviceRepairReportMapper getMapper() {
        return this.baseMapper;
    }

    @Override
    public IPage<DeviceRepairReportDTO> getPagedReports(int page, int size) {
        Page<DeviceRepairReport> pageParam = new Page<>(page, size);
        IPage<DeviceRepairReport> result = baseMapper.selectPageWithConditions(pageParam, null, null, null, null, null);

        // 转换为DTO分页结果
        Page<DeviceRepairReportDTO> dtoPage = new Page<>(page, size);
        dtoPage.setTotal(result.getTotal());
        dtoPage.setCurrent(result.getCurrent());
        dtoPage.setSize(result.getSize());

        List<DeviceRepairReportDTO> dtoList = result.getRecords().stream().map(report -> {
            DeviceRepairReportDTO dto = new DeviceRepairReportDTO();
            dto.setId(report.getId());
            dto.setDeviceName(report.getDeviceName());
            dto.setDeviceType(report.getDeviceType());
            dto.setProductLineId(report.getProductLineId());
            dto.setProductLineName(report.getProductLineName());
            dto.setDescription(report.getDescription());
            dto.setStatus(report.getStatus());
            dto.setSubmitter(report.getSubmitter());
            dto.setSubmitTime(report.getSubmitTime());
            dto.setProcessInstanceId(report.getProcessInstanceId());
            return dto;
        }).collect(java.util.stream.Collectors.toList());

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public DeviceRepairReportDTO getReportByIdWithLineName(Long id) {
        DeviceRepairReport report = baseMapper.selectDetailById(id);
        if (report == null) {
            return null;
        }

        // 转换为DTO
        DeviceRepairReportDTO dto = new DeviceRepairReportDTO();
        dto.setId(report.getId());
        dto.setDeviceName(report.getDeviceName());
        dto.setDeviceType(report.getDeviceType());
        dto.setProductLineId(report.getProductLineId());
        dto.setDescription(report.getDescription());
        dto.setStatus(report.getStatus());
        dto.setSubmitter(report.getSubmitter());
        dto.setSubmitTime(report.getSubmitTime());
        dto.setProcessInstanceId(report.getProcessInstanceId());

        return dto;
    }

    // 实现其他接口方法的占位符
    @Override
    public IPage<DeviceRepairReportDTO> getPageList(DeviceRepairReportQueryDTO queryDTO) {
        Page<DeviceRepairReport> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<DeviceRepairReport> result = baseMapper.selectPageWithConditions(
                page,
                queryDTO.getDeviceName(),
                queryDTO.getDeviceType(),
                queryDTO.getProductLineId(),
                queryDTO.getStatus(),
                queryDTO.getSubmitter()
        );

        // 转换为DTO分页结果
        Page<DeviceRepairReportDTO> dtoPage = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        dtoPage.setTotal(result.getTotal());
        dtoPage.setCurrent(result.getCurrent());
        dtoPage.setSize(result.getSize());

        List<DeviceRepairReportDTO> dtoList = result.getRecords().stream().map(report -> {
            DeviceRepairReportDTO dto = new DeviceRepairReportDTO();
            dto.setId(report.getId());
            dto.setDeviceName(report.getDeviceName());
            dto.setDeviceType(report.getDeviceType());
            dto.setProductLineId(report.getProductLineId());
            dto.setProductLineName(report.getProductLineName());
            dto.setDescription(report.getDescription());
            dto.setStatus(report.getStatus());
            dto.setSubmitter(report.getSubmitter());
            dto.setSubmitTime(report.getSubmitTime());
            dto.setProcessInstanceId(report.getProcessInstanceId());
            return dto;
        }).collect(java.util.stream.Collectors.toList());

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public boolean createReport(DeviceRepairReport report) {
        return this.save(report);
    }

    @Override
    public boolean updateReport(DeviceRepairReport report) {
        return this.updateById(report);
    }

    @Override
    public DeviceRepairReportDTO getReportDetail(Long id) {
        return getReportByIdWithLineName(id);
    }

    @Override
    public List<DeviceRepairReportDTO> getReportsByProductLineId(Long productLineId) {
        // TODO: 实现根据产线ID查询
        return null;
    }

    @Override
    public List<DeviceRepairReportDTO> getReportsByStatus(String status) {
        // TODO: 实现根据状态查询
        return null;
    }

    @Override
    public List<DeviceRepairReportDTO> getReportsByDeviceName(String deviceName) {
        // TODO: 实现根据设备名称查询
        return null;
    }

    @Override
    public List<DeviceRepairReportDTO> getReportsBySubmitter(String submitter) {
        // TODO: 实现根据提交人查询
        return null;
    }

    @Override
    public List<Object> getStatusStatistics() {
        return baseMapper.countByStatus();
    }

    @Override
    public boolean deleteReport(Long id) {
        return this.removeById(id);
    }

    @Override
    public boolean deleteReports(List<Long> ids) {
        return this.removeByIds(ids);
    }

    @Override
    public boolean updateReportStatus(Long id, String status) {
        DeviceRepairReport report = new DeviceRepairReport();
        report.setId(id);
        report.setStatus(status);
        return this.updateById(report);
    }

    @Override
    public List<DeviceRepairReportDTO> getAllReports() {
        // TODO: 实现获取所有记录
        return null;
    }
}
