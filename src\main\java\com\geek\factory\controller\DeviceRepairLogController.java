package com.geek.factory.controller;

import com.geek.factory.constant.SuccessConstant;
import com.geek.factory.dto.DeviceRepairLogDTO;
import com.geek.factory.entity.DeviceRepairLog;
import com.geek.factory.result.Result;
import com.geek.factory.service.DeviceRepairLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description 设备维修记录日志控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/log")
@Api(tags = "设备维修记录日志管理")
@CrossOrigin
public class DeviceRepairLogController {

    @Autowired
    private DeviceRepairLogService deviceRepairLogService;

    /**
     * 获取维修记录日志列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取维修记录日志列表")
    public Result getList() {
        log.info("获取维修记录日志列表");
        try {
            List<DeviceRepairLogDTO> list = deviceRepairLogService.getAllWithLineName();
            log.info("获取维修记录日志列表成功，共{}条记录", list.size());
            return new Result(SuccessConstant.SUCCESS, "查询成功", list);
        } catch (Exception e) {
            log.error("获取维修记录日志列表失败", e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 新增维修记录日志
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增维修记录日志")
    public Result add(@RequestBody DeviceRepairLogDTO dto) {
        log.info("新增维修记录日志，参数：{}", dto);
        try {
            DeviceRepairLog entity = new DeviceRepairLog();
            entity.setRepairId(dto.getRepairId());
            entity.setProductLineId(dto.getProductLineId());
            entity.setRemark(dto.getRemark());
            entity.setOperator(dto.getOperator());
            entity.setStatus(dto.getStatus());
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            boolean success = deviceRepairLogService.save(entity);
            if (success) {
                log.info("新增维修记录日志成功");
                return new Result(SuccessConstant.SUCCESS, "新增成功", null);
            } else {
                log.warn("新增维修记录日志失败");
                return new Result(SuccessConstant.FAILED, "新增失败", null);
            }
        } catch (Exception e) {
            log.error("新增维修记录日志失败", e);
            return new Result(SuccessConstant.FAILED, "新增失败", null);
        }
    }

    /**
     * 更新维修记录日志
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新维修记录日志")
    public Result update(@RequestBody DeviceRepairLogDTO dto) {
        log.info("更新维修记录日志，参数：{}", dto);
        try {
            DeviceRepairLog entity = deviceRepairLogService.getById(dto.getId());
            if (entity == null) {
                log.warn("维修记录日志不存在，ID：{}", dto.getId());
                return new Result(SuccessConstant.FAILED, "记录不存在", null);
            }

            entity.setRepairId(dto.getRepairId());
            entity.setProductLineId(dto.getProductLineId());
            entity.setRemark(dto.getRemark());
            entity.setOperator(dto.getOperator());
            entity.setStatus(dto.getStatus());
            entity.setUpdateTime(LocalDateTime.now());

            boolean success = deviceRepairLogService.updateById(entity);
            if (success) {
                log.info("更新维修记录日志成功，ID：{}", dto.getId());
                return new Result(SuccessConstant.SUCCESS, "更新成功", null);
            } else {
                log.warn("更新维修记录日志失败");
                return new Result(SuccessConstant.FAILED, "更新失败", null);
            }
        } catch (Exception e) {
            log.error("更新维修记录日志失败", e);
            return new Result(SuccessConstant.FAILED, "更新失败", null);
        }
    }

    /**
     * 删除维修记录日志
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除维修记录日志")
    public Result delete(@PathVariable Long id) {
        log.info("删除维修记录日志，ID：{}", id);
        try {
            boolean success = deviceRepairLogService.removeById(id);
            if (success) {
                log.info("删除维修记录日志成功，ID：{}", id);
                return new Result(SuccessConstant.SUCCESS, "删除成功", null);
            } else {
                log.warn("删除维修记录日志失败，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "删除失败", null);
            }
        } catch (Exception e) {
            log.error("删除维修记录日志失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "删除失败", null);
        }
    }
}
