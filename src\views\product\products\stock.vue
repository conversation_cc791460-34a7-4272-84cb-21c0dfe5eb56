<template>
  <div class="main">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button 
          type="primary" 
          :icon="useRenderIcon(ArrowLeft)" 
          @click="goBack"
          class="back-btn"
        >
          返回产品列表
        </el-button>
        <div class="page-title">
          <h2>产品库存详情</h2>
          <p class="subtitle">查看产品入库信息和库存状态</p>
        </div>
      </div>
    </div>

    <!-- 产品基本信息卡片 -->
    <el-card class="product-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">产品基本信息</span>
        </div>
      </template>
      
      <div class="product-info" v-if="productInfo">
        <div class="product-image">
          <el-image
            :src="productInfo.pic"
            fit="cover"
            class="product-pic"
            :preview-src-list="[productInfo.pic]"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
        
        <div class="product-details">
          <div class="detail-row">
            <span class="label">产品ID：</span>
            <span class="value">{{ productInfo.id }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品编码：</span>
            <span class="value">{{ productInfo.code }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品名称：</span>
            <span class="value">{{ productInfo.name }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品型号：</span>
            <span class="value">{{ productInfo.model }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品分类：</span>
            <span class="value">{{ productInfo.category }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品价格：</span>
            <span class="value price">¥{{ productInfo.price }}</span>
          </div>
          <div class="detail-row">
            <span class="label">完成时间：</span>
            <span class="value">{{ formatDateTime(productInfo.completeTime) }}</span>
          </div>
          <div class="detail-row">
            <span class="label">产品状态：</span>
            <el-tag :type="formatStatusType(productInfo.status)">
              {{ formatStatusText(productInfo.status) }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 库存信息卡片 -->
    <el-card class="stock-info-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">库存信息</span>
          <el-tag v-if="stockInfo" :type="stockInfo.quantity > 0 ? 'success' : 'danger'" size="large">
            {{ stockInfo.quantity > 0 ? '有库存' : '无库存' }}
          </el-tag>
        </div>
      </template>
      
      <div class="stock-info" v-if="stockInfo">
        <div class="stock-stats">
          <div class="stat-item">
            <div class="stat-value">{{ stockInfo.quantity }}</div>
            <div class="stat-label">库存数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ calculateInStockTime() }}</div>
            <div class="stat-label">入库时间</div>
          </div>
        </div>
        
        <div class="stock-details" v-if="stockInfo.remark">
          <div class="detail-row">
            <span class="label">备注信息：</span>
            <span class="value">{{ stockInfo.remark }}</span>
          </div>
        </div>
      </div>
      
      <div v-else class="no-stock">
        <el-empty description="暂无库存信息" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useRenderIcon } from "@/components/ReIcon/src/hooks"
import ArrowLeft from "@iconify-icons/ep/arrow-left"
import { Picture } from '@element-plus/icons-vue'

import { 
  getProductById, 
  getProductStock,
  type Product,
  type ProductStock 
} from '@/api/product'

defineOptions({
  name: 'ProductStock'
})

const router = useRouter()
const route = useRoute()

// 响应式数据
const productInfo = ref<Product | null>(null)
const stockInfo = ref<ProductStock | null>(null)
const loading = ref(false)

// 获取产品ID
const productId = computed(() => {
  return parseInt(route.params.id as string)
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 格式化日期时间
const formatDateTime = (dateTime: string | undefined) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 计算入库时间（完成时间 + 12小时）
const calculateInStockTime = () => {
  if (!productInfo.value?.completeTime) {
    return '-'
  }

  try {
    const completeTime = new Date(productInfo.value.completeTime)
    // 添加12小时（12 * 60 * 60 * 1000 毫秒）
    const inStockTime = new Date(completeTime.getTime() + 12 * 60 * 60 * 1000)
    return inStockTime.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('计算入库时间失败:', error)
    return '-'
  }
}

// 格式化状态文本
const formatStatusText = (status: string | number) => {
  const statusValue = String(status) // 统一转换为字符串进行比较
  switch (statusValue) {
    case '0':
      return '未完成'
    case '1':
      return '已完成'
    default:
      return '未完成'
  }
}

// 格式化状态标签类型
const formatStatusType = (status: string | number) => {
  const statusValue = String(status) // 统一转换为字符串进行比较
  switch (statusValue) {
    case '0':
      return 'warning'
    case '1':
      return 'success'
    default:
      return 'info'
  }
}

// 加载产品信息
const loadProductInfo = async () => {
  try {
    const response = await getProductById(productId.value)
    if (response.success) {
      productInfo.value = response.data
      // 添加模拟数据
      if (!productInfo.value.pic) {
        productInfo.value.pic = `https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop`
      }
      if (productInfo.value.status === undefined) {
        productInfo.value.status = 1
      }
      if (!productInfo.value.description) {
        productInfo.value.description = `${productInfo.value.name}的详细描述信息`
      }
    } else {
      ElMessage.error(response.msg || '获取产品信息失败')
    }
  } catch (error) {
    console.error('获取产品信息失败:', error)
    ElMessage.error('获取产品信息失败')
  }
}

// 加载库存信息
const loadStockInfo = async () => {
  try {
    const response = await getProductStock(productId.value)
    if (response.success) {
      stockInfo.value = response.data
    } else {
      // 如果没有库存信息，创建模拟数据
      console.log('暂无库存信息，使用模拟数据')

      // 计算入库时间（完成时间 + 12小时）
      let calculatedInStockTime = new Date().toISOString()
      if (productInfo.value?.completeTime) {
        const completeTime = new Date(productInfo.value.completeTime)
        const inStockTime = new Date(completeTime.getTime() + 12 * 60 * 60 * 1000)
        calculatedInStockTime = inStockTime.toISOString()
      }

      // 模拟库存数据
      stockInfo.value = {
        id: 1,
        productId: productId.value,
        productName: productInfo.value?.name || '',
        productCode: productInfo.value?.code || '',
        quantity: Math.floor(Math.random() * 100) + 10,
        inStockTime: calculatedInStockTime,
        remark: '产品入库成功，质量检验合格'
      }
    }
  } catch (error) {
    console.error('获取库存信息失败:', error)

    // 计算入库时间（完成时间 + 12小时）
    let calculatedInStockTime = new Date().toISOString()
    if (productInfo.value?.completeTime) {
      const completeTime = new Date(productInfo.value.completeTime)
      const inStockTime = new Date(completeTime.getTime() + 12 * 60 * 60 * 1000)
      calculatedInStockTime = inStockTime.toISOString()
    }

    // 库存信息获取失败时，创建模拟数据
    stockInfo.value = {
      id: 1,
      productId: productId.value,
      productName: productInfo.value?.name || '',
      productCode: productInfo.value?.code || '',
      quantity: Math.floor(Math.random() * 100) + 10,
      inStockTime: calculatedInStockTime,
      remark: '产品入库成功，质量检验合格'
    }
  }
}

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadProductInfo(),
      loadStockInfo()
    ])
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  initData()
})
</script>

<style scoped>
.main {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  border-radius: 8px;
}

.page-title h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-title .subtitle {
  margin: 4px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.product-info-card,
.stock-info-card {
  margin-bottom: 20px;
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.product-info {
  display: flex;
  gap: 30px;
}

.product-image {
  flex-shrink: 0;
}

.product-pic {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 30px;
}

.product-details {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-row {
  display: flex;
  align-items: center;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.value {
  color: #303133;
}

.value.price {
  color: #f56c6c;
  font-weight: 600;
  font-size: 16px;
}

.stock-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  min-width: 150px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.stock-details {
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.no-stock {
  text-align: center;
  padding: 40px;
}
</style>
