package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.geek.factory.constant.MessageConstant;
import com.geek.factory.entity.Product;
import com.geek.factory.result.Result;
import com.geek.factory.service.ProductService;
import com.geek.factory.vo.ProductQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 产品管理控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/product/management")
@Api(tags = "产品管理")
@CrossOrigin
@Validated
public class ProductController {

    @Autowired
    private ProductService productService;

    /**
     * 分页查询产品列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询产品列表", notes = "支持按名称、型号、分类、价格区间查询")
    public Result getProductPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody ProductQueryVO queryVO) {
        log.info("分页查询产品列表，查询条件：{}", queryVO);
        try {
            IPage<Product> page = productService.getProductPage(queryVO);
            log.info("分页查询产品列表成功，共查询到{}条记录", page.getTotal());
            return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, page);
        } catch (Exception e) {
            log.error("分页查询产品列表失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    /**
     * 根据ID查询产品详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询产品详情", notes = "根据产品ID查询详细信息")
    public Result getProductById(
            @ApiParam(value = "产品ID", required = true, example = "1") @PathVariable Integer id) {
        log.info("根据ID查询产品详情，ID：{}", id);
        try {
            if (id == null || id <= 0) {
                return new Result(false, MessageConstant.PARAMETER_ERROR, null);
            }

            Product product = productService.getProductById(id);
            if (product != null) {
                log.info("查询产品详情成功，产品名称：{}", product.getName());
                return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, product);
            } else {
                log.warn("产品不存在，ID：{}", id);
                return new Result(false, MessageConstant.PRODUCT_NOT_FOUND, null);
            }
        } catch (Exception e) {
            log.error("根据ID查询产品详情失败，ID：{}", id, e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    /**
     * 新增产品
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增产品", notes = "新增产品信息，所有必填字段不能为空")
    public Result addProduct(
            @ApiParam(value = "产品信息", required = true) @RequestBody Product product) {
        log.info("新增产品，产品信息：{}", product);
        try {
            boolean success = productService.addProduct(product);
            if (success) {
                log.info("新增产品成功，产品名称：{}", product.getName());
                return new Result(true, MessageConstant.ADD_PRODUCT_SUCCESS, null);
            } else {
                log.warn("新增产品失败，产品信息：{}", product);
                return new Result(false, MessageConstant.ADD_PRODUCT_FAILED, null);
            }
        } catch (Exception e) {
            log.error("新增产品失败，产品信息：{}", product, e);
            return new Result(false, MessageConstant.ADD_PRODUCT_FAILED, null);
        }
    }

    /**
     * 修改产品
     */
    @PutMapping
    @ApiOperation(value = "修改产品", notes = "修改产品信息，产品ID不能为空")
    public Result<Void> updateProduct(
            @ApiParam(value = "产品信息", required = true) @RequestBody Product product) {
        log.info("修改产品，产品信息：{}", product);
        try {
            boolean success = productService.updateProduct(product);
            if (success) {
                log.info("修改产品成功，产品ID：{}", product.getId());
                return Result.success();
            } else {
                log.warn("修改产品失败，产品信息：{}", product);
                return Result.error(MessageConstant.PRODUCT_UPDATE_FAILED);
            }
        } catch (Exception e) {
            log.error("修改产品失败，产品信息：{}", product, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除产品", notes = "根据产品ID删除产品")
    public Result<Void> deleteProduct(
            @ApiParam(value = "产品ID", required = true, example = "1") @PathVariable Integer id) {
        log.info("删除产品，ID：{}", id);
        try {
            if (id == null || id <= 0) {
                return Result.error(MessageConstant.PARAMETER_ERROR);
            }

            boolean success = productService.deleteProduct(id);
            if (success) {
                log.info("删除产品成功，ID：{}", id);
                return Result.success();
            } else {
                log.warn("删除产品失败，ID：{}", id);
                return Result.error(MessageConstant.PRODUCT_DELETE_FAILED);
            }
        } catch (Exception e) {
            log.error("删除产品失败，ID：{}", id, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 批量删除产品
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除产品", notes = "根据产品ID列表批量删除产品")
    public Result<Void> deleteProductBatch(
            @ApiParam(value = "产品ID列表", required = true) @RequestBody List<Integer> ids) {
        log.info("批量删除产品，IDs：{}", ids);
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.error(MessageConstant.PARAMETER_ERROR);
            }

            boolean success = productService.deleteProductBatch(ids);
            if (success) {
                log.info("批量删除产品成功，删除数量：{}", ids.size());
                return Result.success();
            } else {
                log.warn("批量删除产品失败，IDs：{}", ids);
                return Result.error(MessageConstant.PRODUCT_DELETE_FAILED);
            }
        } catch (Exception e) {
            log.error("批量删除产品失败，IDs：{}", ids, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 获取所有产品列表（不分页）
     */
    @GetMapping("/all")
    @ApiOperation(value = "获取所有产品列表", notes = "获取所有产品列表，不分页")
    public Result<List<Product>> getAllProducts() {
        log.info("获取所有产品列表");
        try {
            List<Product> products = productService.getAllProducts();
            log.info("获取所有产品列表成功，共{}条记录", products.size());
            return Result.success(products);
        } catch (Exception e) {
            log.error("获取所有产品列表失败", e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 根据分类查询产品列表
     */
    @GetMapping("/category/{category}")
    @ApiOperation(value = "根据分类查询产品列表", notes = "根据产品分类查询产品列表")
    public Result<List<Product>> getProductsByCategory(
            @ApiParam(value = "产品分类", required = true, example = "主板类") @PathVariable String category) {
        log.info("根据分类查询产品列表，分类：{}", category);
        try {
            List<Product> products = productService.getProductsByCategory(category);
            log.info("根据分类查询产品列表成功，分类：{}，共{}条记录", category, products.size());
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据分类查询产品列表失败，分类：{}", category, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 根据价格区间查询产品
     */
    @GetMapping("/price")
    @ApiOperation(value = "根据价格区间查询产品", notes = "根据价格区间查询产品列表")
    public Result<List<Product>> getProductsByPriceRange(
            @ApiParam(value = "最小价格", required = true, example = "100.00") @RequestParam BigDecimal minPrice,
            @ApiParam(value = "最大价格", required = true, example = "1000.00") @RequestParam BigDecimal maxPrice) {
        log.info("根据价格区间查询产品，价格区间：{} - {}", minPrice, maxPrice);
        try {
            List<Product> products = productService.getProductsByPriceRange(minPrice, maxPrice);
            log.info("根据价格区间查询产品成功，价格区间：{} - {}，共{}条记录", minPrice, maxPrice, products.size());
            return Result.success(products);
        } catch (Exception e) {
            log.error("根据价格区间查询产品失败，价格区间：{} - {}", minPrice, maxPrice, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 获取产品分类统计
     */
    @GetMapping("/statistics/category")
    @ApiOperation(value = "获取产品分类统计", notes = "获取各分类下的产品数量统计")
    public Result<List<Map<String, Object>>> getProductCategoryStatistics() {
        log.info("获取产品分类统计");
        try {
            List<Map<String, Object>> statistics = productService.getProductCategoryStatistics();
            log.info("获取产品分类统计成功，共{}个分类", statistics.size());
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取产品分类统计失败", e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }
}
