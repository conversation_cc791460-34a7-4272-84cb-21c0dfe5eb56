-- 更新原料库存数据脚本
-- 为现有的原料添加库存数量

-- 首先检查是否已经有quantity字段，如果没有则添加
ALTER TABLE `materials` ADD COLUMN IF NOT EXISTS `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '库存数量';
ALTER TABLE `materials` ADD COLUMN IF NOT EXISTS `code` varchar(50) DEFAULT NULL COMMENT '原料编号';

-- 添加索引
ALTER TABLE `materials` ADD KEY IF NOT EXISTS `idx_code` (`code`);

-- 更新现有原料的库存数量和编号
UPDATE `materials` SET `code` = 'MAT001', `quantity` = 100.00 WHERE `name` = '硅晶圆';
UPDATE `materials` SET `code` = 'MAT002', `quantity` = 50.00 WHERE `name` = '铜箔';
UPDATE `materials` SET `code` = 'MAT003', `quantity` = 80.00 WHERE `name` = '环氧树脂';
UPDATE `materials` SET `code` = 'MAT004', `quantity` = 30.00 WHERE `name` = '焊锡丝';
UPDATE `materials` SET `code` = 'MAT005', `quantity` = 200.00 WHERE `name` = 'PCB基板';
UPDATE `materials` SET `code` = 'MAT006', `quantity` = 1000.00 WHERE `name` = '电阻器';
UPDATE `materials` SET `code` = 'MAT007', `quantity` = 800.00 WHERE `name` = '电容器';
UPDATE `materials` SET `code` = 'MAT008', `quantity` = 150.00 WHERE `name` = '集成电路';
UPDATE `materials` SET `code` = 'MAT009', `quantity` = 500.00 WHERE `name` = '导线';
UPDATE `materials` SET `code` = 'MAT010', `quantity` = 300.00 WHERE `name` = '塑料外壳';
UPDATE `materials` SET `code` = 'MAT011', `quantity` = 120.00 WHERE `name` = '金属外壳';
UPDATE `materials` SET `code` = 'MAT012', `quantity` = 2000.00 WHERE `name` = '螺丝';
UPDATE `materials` SET `code` = 'MAT013', `quantity` = 1000.00 WHERE `name` = '胶水';
UPDATE `materials` SET `code` = 'MAT014', `quantity` = 5000.00 WHERE `name` = '标签纸';
UPDATE `materials` SET `code` = 'MAT015', `quantity` = 500.00 WHERE `name` = '包装盒';

-- 添加一些额外的测试原料（如果用户在前端看到的原料）
INSERT IGNORE INTO `materials` (`code`, `name`, `type`, `unit`, `price`, `quantity`, `pic`) VALUES
('MAT016', '陶瓷电容 10μF 50V', '电子元件', '个', 0.25, 1000.00, '/images/materials/ceramic_capacitor.jpg'),
('MAT017', '铝电解电容 100μF 25V', '电子元件', '个', 0.35, 800.00, '/images/materials/aluminum_capacitor.jpg'),
('MAT018', '贴片电阻 1kΩ', '电子元件', '个', 0.02, 5000.00, '/images/materials/smd_resistor.jpg'),
('MAT019', '发光二极管 红色', '电子元件', '个', 0.15, 2000.00, '/images/materials/red_led.jpg'),
('MAT020', '晶体振荡器 16MHz', '电子元件', '个', 1.50, 500.00, '/images/materials/crystal_oscillator.jpg');

-- 创建生产计划原料关联表
CREATE TABLE IF NOT EXISTS `production_plan_material` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '生产计划ID',
  `material_id` bigint NOT NULL COMMENT '原料ID',
  `required_quantity` decimal(10,2) NOT NULL COMMENT '需要数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_material_id` (`material_id`),
  CONSTRAINT `fk_plan_material_plan` FOREIGN KEY (`plan_id`) REFERENCES `production_plan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_plan_material_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产计划原料关联表';

-- 验证更新结果
SELECT `id`, `code`, `name`, `type`, `unit`, `price`, `quantity` FROM `materials` ORDER BY `id`;
