/**
 * 网关配置工具函数
 * 用于管理不同环境下的网关地址配置
 */

// 获取环境变量
const { 
  VITE_GATEWAY_URL, 
  VITE_GATEWAY_WS_URL, 
  VITE_USE_GATEWAY 
} = import.meta.env;

/**
 * 获取HTTP网关地址
 * @returns 网关HTTP地址
 */
export const getGatewayUrl = (): string => {
  if (VITE_USE_GATEWAY === 'true' && VITE_GATEWAY_URL) {
    return VITE_GATEWAY_URL;
  }
  // 默认直连后端
  return 'http://localhost:8080';
};

/**
 * 获取WebSocket网关地址
 * @returns 网关WebSocket地址
 */
export const getGatewayWsUrl = (): string => {
  if (VITE_USE_GATEWAY === 'true' && VITE_GATEWAY_WS_URL) {
    return VITE_GATEWAY_WS_URL;
  }
  // 默认直连后端
  return 'ws://localhost:8080';
};

/**
 * 是否启用网关
 * @returns 是否启用网关
 */
export const isGatewayEnabled = (): boolean => {
  return VITE_USE_GATEWAY === 'true';
};

/**
 * 构建API请求URL
 * @param path API路径
 * @returns 完整的API URL
 */
export const buildApiUrl = (path: string): string => {
  const baseUrl = getGatewayUrl();
  // 确保路径以/api开头（用于网关路由）
  const apiPath = path.startsWith('/api') ? path : `/api${path}`;
  return `${baseUrl}${apiPath}`;
};

/**
 * 构建WebSocket URL
 * @param path WebSocket路径
 * @param params 查询参数
 * @returns 完整的WebSocket URL
 */
export const buildWebSocketUrl = (path: string, params?: Record<string, string | number>): string => {
  const baseUrl = getGatewayWsUrl();
  // 确保路径以/ws开头（用于网关路由）
  const wsPath = path.startsWith('/ws') ? path : `/ws${path}`;
  
  let url = `${baseUrl}${wsPath}`;
  
  // 添加查询参数
  if (params && Object.keys(params).length > 0) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    url += `?${queryString}`;
  }
  
  return url;
};

/**
 * 构建文件服务URL
 * @param path 文件路径
 * @returns 完整的文件服务URL
 */
export const buildFileUrl = (path: string): string => {
  const baseUrl = getGatewayUrl();
  // 确保路径以/file开头（用于网关路由）
  const filePath = path.startsWith('/file') ? path : `/file${path}`;
  return `${baseUrl}${filePath}`;
};

/**
 * 网关配置信息
 */
export const gatewayConfig = {
  httpUrl: getGatewayUrl(),
  wsUrl: getGatewayWsUrl(),
  enabled: isGatewayEnabled(),
  routes: {
    api: '/api/**',      // 主要API服务路由
    file: '/file/**',    // 文件服务路由
    ws: '/ws/**'         // WebSocket服务路由
  }
};

// 在开发环境下输出网关配置信息
if (import.meta.env.DEV) {
  console.log('🌐 网关配置信息:', gatewayConfig);
}
