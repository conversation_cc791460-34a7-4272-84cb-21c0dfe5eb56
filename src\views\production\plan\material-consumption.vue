<template>
  <div class="material-consumption-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="goBack" content="原料消耗详情">
        <template #icon>
          <el-icon><ArrowLeft /></el-icon>
        </template>
      </el-page-header>
    </div>

    <!-- 计划信息显示区域 -->
    <div class="plan-info-container">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>生产计划信息</span>
          </div>
        </template>
        <div class="plan-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="计划编号">
              <el-tag type="primary">{{ currentPlanCode }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="原料种类">
              <el-tag type="success">{{ tableData.length }} 种</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        empty-text="暂无原料消耗数据"
      >
        <el-table-column
          prop="planCode"
          label="计划编号"
          width="180"
          align="center"
        />
        <el-table-column
          prop="materialName"
          label="原料名称"
          min-width="150"
          align="center"
        />
        <el-table-column
          prop="requiredQuantity"
          label="数量"
          width="120"
          align="center"
        >
          <template #default="{ row }">
            <span class="quantity-text">{{ row.requiredQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="unit"
          label="单位"
          width="100"
          align="center"
        />
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="paginationConfig.current"
        v-model:page-size="paginationConfig.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="paginationConfig.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft } from "@element-plus/icons-vue";
import { getPlanMaterialsByCode } from "@/api/production";

// 路由
const router = useRouter();
const route = useRoute();

// 当前计划编号
const currentPlanCode = ref<string>("");

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);

// 分页配置
const paginationConfig = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 获取原料消耗数据
const getMaterialConsumptionData = async () => {
  if (!currentPlanCode.value) {
    ElMessage.warning("计划编号不能为空");
    return;
  }

  loading.value = true;

  try {
    console.log(`🔍 查询计划编号 ${currentPlanCode.value} 的原料消耗数据`);
    const response = await getPlanMaterialsByCode(currentPlanCode.value);

    console.log('API响应:', response);

    if (response && response.success && response.data && Array.isArray(response.data)) {
      // 为每条记录添加计划编号
      tableData.value = response.data.map((item: any) => ({
        planCode: currentPlanCode.value,
        materialName: item.materialName,
        requiredQuantity: item.requiredQuantity,
        unit: item.unit,
        remark: item.remark
      }));

      paginationConfig.total = tableData.value.length;
      ElMessage.success(`成功加载 ${tableData.value.length} 条原料消耗记录`);
    } else {
      tableData.value = [];
      paginationConfig.total = 0;
      ElMessage.warning(`计划编号 ${currentPlanCode.value} 暂无原料消耗记录`);
    }
  } catch (error) {
    console.error('获取原料消耗数据失败:', error);
    tableData.value = [];
    paginationConfig.total = 0;
    ElMessage.error(`获取数据失败: ${error?.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (size: number) => {
  paginationConfig.size = size;
  paginationConfig.current = 1;
};

const handleCurrentChange = (page: number) => {
  paginationConfig.current = page;
};

// 页面加载时，从路由参数获取计划编号并自动加载数据
onMounted(() => {
  // 从路由参数获取计划编号
  const planCodeFromRoute = route.params.planCode as string;

  if (planCodeFromRoute) {
    currentPlanCode.value = planCodeFromRoute;
    console.log(`📋 从路由获取计划编号: ${currentPlanCode.value}`);
    getMaterialConsumptionData();
  } else {
    ElMessage.error("未获取到计划编号，请从生产计划列表页面访问");
  }
});
</script>

<style scoped>
.material-consumption-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quantity-text {
  font-weight: 600;
  color: #409eff;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table--border th) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border td) {
  border-right: 1px solid #ebeef5;
}
</style>
