import { $t } from "@/plugins/i18n";
import { article } from "@/router/enums";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/article",
  name: "Article",
  component: Layout,
  redirect: "/article/list",
  meta: {
    icon: "ri:article-line",
    title: $t("文章管理"),
    rank: article
  },
  children: [
    {
      path: "/article/list",
      name: "articleList",
      component: () => import("@/views/article/list.vue"),
      meta: {
        title: $t("文章列表"),
        showParent: true
      }
    },
    {
      path: "/article/create",
      name: "articleAdd",
      component: () => import("@/views/article/add.vue"),
      meta: {
        title: $t("上传文章"),
        showParent: true
      }
    },
    {
      path: "/article/edit/:id",
      name: "edit",
      component: () => import("@/views/article/edit.vue"),
      meta: {
        title: $t("编辑文章"),
        showLink: false,
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
