{"name": "智慧工厂", "version": "5.7.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build && generate-version-file", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/vue-pure-admin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/vue-pure-admin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@howdyjs/mouse-menu": "^2.1.3", "@infectoone/vue-ganttastic": "^2.3.2", "@logicflow/core": "^1.2.27", "@logicflow/extension": "^1.2.27", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.1.2", "@pureadmin/utils": "^2.4.7", "@vue-flow/background": "^1.3.0", "@vue-flow/core": "^1.38.2", "@vueuse/core": "^10.11.0", "@vueuse/motion": "^2.2.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.7.2", "china-area-data": "^5.0.1", "cropperjs": "^1.6.2", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "echarts": "^5.5.1", "el-table-infinite-scroll": "^3.0.5", "element-plus": "^2.7.6", "intro.js": "^7.2.0", "js-cookie": "^3.0.5", "jsbarcode": "^3.11.6", "localforage": "^1.10.0", "mint-filter": "^4.0.3", "mitt": "^3.0.1", "mqtt": "4.3.7", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.7", "pinyin-pro": "^3.23.0", "plus-pro-components": "^0.1.11", "qrcode": "^1.5.3", "qs": "^6.12.2", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.2", "swiper": "^11.1.4", "typeit": "^8.8.3", "v-contextmenu": "^3.2.0", "v3-infinite-loading": "^1.3.1", "version-rocket": "^1.7.1", "vue": "^3.4.31", "vue-i18n": "^9.13.1", "vue-json-pretty": "^2.4.0", "vue-pdf-embed": "^2.0.4", "vue-router": "^4.4.0", "vue-tippy": "^6.4.4", "vue-types": "^5.1.2", "vue-virtual-scroller": "2.0.0-beta.8", "vue-waterfall-plugin-next": "^2.4.3", "vue3-danmaku": "^1.6.0", "vue3-puzzle-vcode": "^1.1.7", "vuedraggable": "^4.1.0", "vxe-table": "4.6.17", "wavesurfer.js": "^7.8.0", "xgplayer": "^3.0.18", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.6.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^4.0.0", "@pureadmin/theme": "^3.2.0", "@types/dagre": "^0.7.52", "@types/gradient-string": "^1.1.6", "@types/intro.js": "^5.1.5", "@types/js-cookie": "^3.0.6", "@types/node": "^20.14.9", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "autoprefixer": "^10.4.19", "boxen": "^7.1.1", "cssnano": "^7.0.3", "dagre": "^0.8.5", "eslint": "^9.6.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.27.0", "gradient-string": "^2.0.2", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8.4.39", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.2", "rimraf": "^5.0.7", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.6", "stylelint": "^16.6.1", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.0", "svgo": "^3.3.2", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "vite": "^5.3.2", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-checker": "^0.7.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-inspector": "^5.1.2", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.0.24"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}}