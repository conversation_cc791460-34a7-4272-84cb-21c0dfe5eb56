package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备维修上报实体类
 */
@Data
@TableName("device_repair_report")
public class DeviceRepairReport {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String deviceName;

    private String deviceType;

    private Long productLineId;

    @TableField(exist = false)
    private String productLineName;

    private String description;

    private String status;

    private String submitter;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime submitTime;

    private String processInstanceId;
}
