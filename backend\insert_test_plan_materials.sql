-- 插入生产计划原料关联测试数据
-- 注意：这里使用的生产计划ID和原料ID需要在对应表中存在

-- 首先查看现有的生产计划
SELECT id, plan_code, product_name, status FROM production_plan LIMIT 5;

-- 查看现有的原料
SELECT id, code, name, unit, quantity FROM materials LIMIT 10;

-- 为生产计划ID=1的计划添加原料消耗记录（如果该计划存在）
INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`) VALUES
-- 假设生产计划ID=1存在，添加一些原料消耗
(1, 1, 50.00, '片', '硅晶圆用于主板制造', NOW()),
(1, 2, 25.50, 'kg', '铜箔用于电路连接', NOW()),
(1, 3, 15.20, 'kg', '环氧树脂用于绝缘', NOW()),
(1, 7, 300.00, '个', '电容器用于电路', NOW()),
(1, 6, 500.00, '个', '电阻器用于电路', NOW());

-- 为其他生产计划添加原料消耗记录
INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`) VALUES
-- 生产计划ID=2的原料消耗
(2, 4, 8.80, 'kg', '焊锡丝用于焊接', NOW()),
(2, 5, 100.00, '片', 'PCB基板', NOW()),
(2, 8, 20.00, '个', '集成电路芯片', NOW()),
(2, 9, 50.00, 'm', '导线连接', NOW());

-- 动态插入：为最新的生产计划添加原料消耗
-- 获取最新的生产计划ID并插入原料消耗
SET @latest_plan_id = (SELECT id FROM production_plan ORDER BY id DESC LIMIT 1);

-- 为日志中显示的特定生产计划ID添加测试数据
INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`) VALUES
(1941332966093598700, 16, 20.00, '个', '陶瓷电容 10μF 50V', NOW()),
(1941332966093598700, 17, 15.00, '个', '铝电解电容 100μF 25V', NOW()),
(1941332966093598700, 18, 100.00, '个', '贴片电阻 1kΩ', NOW()),
(1941332966093598700, 1, 50.00, '片', '硅晶圆用于主板制造', NOW()),
(1941332966093598700, 7, 200.00, '个', '电容器用于电路', NOW());

-- 为最新的生产计划添加原料消耗（如果不是上面的ID）
INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`)
SELECT @latest_plan_id, 16, 20.00, '个', '陶瓷电容 10μF 50V', NOW()
WHERE @latest_plan_id IS NOT NULL AND @latest_plan_id != 1941332966093598700;

INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`)
SELECT @latest_plan_id, 17, 15.00, '个', '铝电解电容 100μF 25V', NOW()
WHERE @latest_plan_id IS NOT NULL AND @latest_plan_id != 1941332966093598700;

INSERT IGNORE INTO `production_plan_material` (`plan_id`, `material_id`, `required_quantity`, `unit`, `remark`, `create_time`)
SELECT @latest_plan_id, 18, 100.00, '个', '贴片电阻 1kΩ', NOW()
WHERE @latest_plan_id IS NOT NULL AND @latest_plan_id != 1941332966093598700;

-- 验证插入结果
SELECT
    ppm.id,
    ppm.plan_id,
    pp.plan_code,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
ORDER BY ppm.plan_id, ppm.material_id;

-- 查看特定生产计划的原料消耗（用于测试API）
SELECT
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = @latest_plan_id;
