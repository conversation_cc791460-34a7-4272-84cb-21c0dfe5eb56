import { http } from "@/utils/http";

export interface Product {
  id?: number;
  productCode: string;
  productName: string;
  productive?: number;
  model?: string;
  pic?: string;
}

interface PageData {
  records: Product[];
  total: number;
  size: number;
  current: number;
}

interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

export const getProductList = (params: any) =>
  http.request<Result<PageData>>("post", "/api/product/list", { data: params });

export const createProduct = (data: Product) =>
  http.request<Result>("post", "/api/product/create", { data });

export const updateProduct = (data: Product) =>
  http.request<Result>("put", "/api/product/update", { data });

export const deleteProduct = (id: number) =>
  http.request<Result>("delete", `/api/product/delete/${id}`);

// 为了兼容现有的组件导入，添加别名
export const create = createProduct;
export const update = updateProduct;
export const getDetail = (id: number) =>
  http.request<Result<Product>>("get", `/api/product/detail/${id}`);
export const fetchList = getProductList;
export const deleteById = deleteProduct;
