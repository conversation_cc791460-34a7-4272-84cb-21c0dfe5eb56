package com.geek.factory.constant;

/**
 * @Description 消息常量类
 */
public class MessageConstant {

    // 原料管理相关消息
    public static final String QUERY_MATERIAL_SUCCESS = "查询原料成功";
    public static final String QUERY_MATERIAL_FAILED = "查询原料失败";
    public static final String ADD_MATERIAL_SUCCESS = "新增原料成功";
    public static final String ADD_MATERIAL_FAILED = "新增原料失败";
    public static final String UPDATE_MATERIAL_SUCCESS = "更新原料成功";
    public static final String UPDATE_MATERIAL_FAILED = "更新原料失败";
    public static final String DELETE_MATERIAL_SUCCESS = "删除原料成功";
    public static final String DELETE_MATERIAL_FAILED = "删除原料失败";
    public static final String MATERIAL_NOT_FOUND = "原料不存在";

    // 产品管理相关消息
    public static final String QUERY_PRODUCT_SUCCESS = "查询产品成功";
    public static final String QUERY_PRODUCT_FAILED = "查询产品失败";
    public static final String ADD_PRODUCT_SUCCESS = "新增产品成功";
    public static final String ADD_PRODUCT_FAILED = "新增产品失败";
    public static final String UPDATE_PRODUCT_SUCCESS = "更新产品成功";
    public static final String UPDATE_PRODUCT_FAILED = "更新产品失败";
    public static final String DELETE_PRODUCT_SUCCESS = "删除产品成功";
    public static final String DELETE_PRODUCT_FAILED = "删除产品失败";
    public static final String PRODUCT_NOT_FOUND = "产品不存在";

    // 产品原料关联相关消息
    public static final String QUERY_PRODUCT_MATERIAL_SUCCESS = "查询产品原料关联成功";
    public static final String QUERY_PRODUCT_MATERIAL_FAILED = "查询产品原料关联失败";
    public static final String ADD_PRODUCT_MATERIAL_SUCCESS = "新增产品原料关联成功";
    public static final String ADD_PRODUCT_MATERIAL_FAILED = "新增产品原料关联失败";
    public static final String UPDATE_PRODUCT_MATERIAL_SUCCESS = "更新产品原料关联成功";
    public static final String UPDATE_PRODUCT_MATERIAL_FAILED = "更新产品原料关联失败";
    public static final String DELETE_PRODUCT_MATERIAL_SUCCESS = "删除产品原料关联成功";
    public static final String DELETE_PRODUCT_MATERIAL_FAILED = "删除产品原料关联失败";

    // 通用消息
    public static final String OPERATION_SUCCESS = "操作成功";
    public static final String OPERATION_FAILED = "操作失败";
    public static final String PARAMETER_ERROR = "参数错误";
    public static final String SYSTEM_ERROR = "系统错误";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String DATA_ALREADY_EXISTS = "数据已存在";
}
