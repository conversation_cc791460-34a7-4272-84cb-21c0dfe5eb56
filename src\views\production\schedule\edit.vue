<template>
  <div class="production-schedule-edit">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>{{ isViewMode ? '查看排班' : '编辑排班' }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="schedule-form"
        :disabled="isViewMode"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排班编号" prop="schedule_code">
              <el-input
                v-model="formData.schedule_code"
                placeholder="请输入排班编号"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="排班名称" prop="schedule_name">
              <el-input
                v-model="formData.schedule_name"
                placeholder="请输入排班名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产计划" prop="plan_id">
              <el-select
                v-model="formData.plan_id"
                placeholder="请选择生产计划"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchPlans"
                :loading="planLoading"
              >
                <el-option
                  v-for="plan in planOptions"
                  :key="plan.id"
                  :label="plan.plan_name"
                  :value="plan.id"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="生产线" prop="production_line">
              <el-input
                v-model="formData.production_line"
                placeholder="请输入生产线"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="start_date">
              <el-date-picker
                v-model="formData.start_date"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                v-model="formData.end_date"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班次" prop="shift">
              <el-select
                v-model="formData.shift"
                placeholder="请选择班次"
                style="width: 100%"
              >
                <el-option label="早班" value="早班" />
                <el-option label="中班" value="中班" />
                <el-option label="晚班" value="晚班" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="排班状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择排班状态"
                style="width: 100%"
              >
                <el-option label="未开始" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="已完成" :value="2" />
                <el-option label="已暂停" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="manager">
              <el-input
                v-model="formData.manager"
                placeholder="请输入负责人姓名"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系电话" prop="contact_phone">
              <el-input
                v-model="formData.contact_phone"
                placeholder="请输入联系电话"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="工作内容" prop="work_content">
          <el-input
            v-model="formData.work_content"
            type="textarea"
            :rows="4"
            placeholder="请输入工作内容"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="formData.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item v-if="!isViewMode">
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>

        <el-form-item v-else>
          <el-button @click="handleCancel">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { getProductionScheduleById, updateProductionSchedule, type ProductionSchedule } from "@/api/production";
import { getProductionPlanList, type ProductionPlan } from "@/api/production";

const router = useRouter();
const route = useRoute();
const formRef = ref();
const loading = ref(false);
const planLoading = ref(false);
const planOptions = ref<ProductionPlan[]>([]);

// 判断是否为查看模式
const isViewMode = computed(() => route.query.mode === 'view');

// 表单数据
const formData = reactive<ProductionSchedule>({
  schedule_code: "",
  schedule_name: "",
  plan_id: 0,
  production_line: "",
  start_date: "",
  end_date: "",
  shift: "",
  status: 0,
  manager: "",
  contact_phone: "",
  work_content: "",
  remarks: ""
});

// 表单验证规则
const rules = {
  schedule_code: [
    { required: true, message: "请输入排班编号", trigger: "blur" }
  ],
  schedule_name: [
    { required: true, message: "请输入排班名称", trigger: "blur" }
  ],
  plan_id: [
    { required: true, message: "请选择生产计划", trigger: "change" }
  ],
  production_line: [
    { required: true, message: "请输入生产线", trigger: "blur" }
  ],
  start_date: [
    { required: true, message: "请选择开始日期", trigger: "change" }
  ],
  end_date: [
    { required: true, message: "请选择结束日期", trigger: "change" }
  ],
  shift: [
    { required: true, message: "请选择班次", trigger: "change" }
  ],
  status: [
    { required: true, message: "请选择排班状态", trigger: "change" }
  ],
  manager: [
    { required: true, message: "请输入负责人姓名", trigger: "blur" }
  ],
  contact_phone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
  ],
  work_content: [
    { required: true, message: "请输入工作内容", trigger: "blur" }
  ]
};

// 搜索生产计划
const searchPlans = async (query: string) => {
  if (query) {
    planLoading.value = true;
    try {
      const response = await getProductionPlanList({
        page: 1,
        pageSize: 20,
        plan_name: query
      });
      planOptions.value = response.data.list;
    } catch (error) {
      console.error("搜索生产计划失败:", error);
    } finally {
      planLoading.value = false;
    }
  }
};

// 获取排班详情
const fetchScheduleData = async () => {
  const id = route.params.id as string;
  if (!id) return;

  loading.value = true;
  try {
    const response = await getProductionScheduleById(parseInt(id));
    Object.assign(formData, response.data);
    
    // 加载关联的生产计划信息
    if (formData.plan_id) {
      await searchPlans("");
    }
  } catch (error) {
    console.error("获取排班详情失败:", error);
    ElMessage.error("获取排班详情失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    const id = route.params.id as string;
    await updateProductionSchedule(parseInt(id), formData);
    ElMessage.success("更新成功");
    router.push("/production/schedule/list");
  } catch (error) {
    console.error("更新失败:", error);
    ElMessage.error("更新失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  router.back();
};

onMounted(() => {
  fetchScheduleData();
});
</script>

<style scoped>
.production-schedule-edit {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-form {
  max-width: 800px;
}
</style> 