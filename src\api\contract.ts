// import { http } from "@/utils/http";

// /** 合同类型定义 */
// export type Contract = {
//   id: number;
//   number: string;
//   orderType: string;
//   amount: number; // BigDecimal 对应 number
//   orderStatus: number;
//   payStatus: number;
//   isInvoiced: boolean;
//   orderDate: string; // LocalDateTime 通常序列化为字符串
// };

// /** 合同分页返回结构 */
// export type ContractListResult = {
//   success: boolean;
//   data: {
//     list: Contract[];
//     total: number;
//     pageSize?: number;
//     currentPage?: number;
//   };
// };

// /** 通用接口返回 */
// export type BaseResult = {
//   success: boolean;
//   message?: string;
//   data?: any;
// };

// /**
//  * 获取合同分页列表
//  * @param params 查询参数
//  */
// export const fetchContractList = (params: {
//   size?: number;
//   current?: number;
//   [key: string]: any;
// }) => {
//   return http.request<ContractListResult>("post", "/api/contract/list", {
//     params
//   });
// };

// /**
//  * 获取合同详情
//  * @param id 合同ID
//  */
// export const getContractDetail = (id: number) => {
//   return http.request<BaseResult>("get", `/api/contract/${id}`);
// };

// /**
//  * 添加合同
//  * @param data 合同数据
//  */
// export const createContract = (data: Omit<Contract, "id">) => {
//   return http.request<BaseResult>("post", "/api/contract", { data });
// };

// /**
//  * 更新合同
//  * @param data 合同数据
//  */
// export const updateContract = (data: Partial<Contract>) => {
//   return http.request<BaseResult>("put", "/api/contract", { data });
// };

// /**
//  * 删除合同
//  * @param id 合同ID
//  */
// export const deleteContractById = (id: number) => {
//   return http.request<BaseResult>("delete", `/api/contract/${id}`);
// };

// /**
//  * 批量删除合同
//  * @param ids 多个ID逗号分隔
//  */
// export const batchDeleteContract = (ids: string) => {
//   return http.request<BaseResult>("delete", `/api/contract/${ids}`);
// };
