-- 修复生产计划表ID生成问题
-- 不备份，直接修复

USE factory;

-- 1. 检查当前表结构和数据
DESCRIBE production_plan;

-- 2. 查看当前数据统计
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 3. 显示最近的几条记录
SELECT 
    id, 
    plan_code, 
    status, 
    create_time 
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 10;

-- 4. 修复表结构（确保ID字段是AUTO_INCREMENT）
ALTER TABLE production_plan MODIFY COLUMN id bigint NOT NULL AUTO_INCREMENT;

-- 5. 重置AUTO_INCREMENT起始值为15
ALTER TABLE production_plan AUTO_INCREMENT = 15;

-- 6. 验证结果
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 7. 显示AUTO_INCREMENT设置
SHOW CREATE TABLE production_plan;

SELECT '✅ 生产计划表ID修复完成！下一个ID将从15开始' as status;
