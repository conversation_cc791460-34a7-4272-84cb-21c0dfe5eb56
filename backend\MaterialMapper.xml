<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geek.factory.mapper.MaterialMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geek.factory.entity.Material">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="pic" property="pic" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, unit, price, pic
    </sql>

    <!-- 分页查询原料列表 -->
    <select id="selectMaterialPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM materials
        <where>
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.type != null and query.type != ''">
                AND type = #{query.type}
            </if>
            <if test="query.unit != null and query.unit != ''">
                AND unit = #{query.unit}
            </if>
            <if test="query.minPrice != null">
                AND price >= #{query.minPrice}
            </if>
            <if test="query.maxPrice != null">
                AND price &lt;= #{query.maxPrice}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据类型查询原料列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM materials
        WHERE type = #{type}
        ORDER BY id DESC
    </select>

    <!-- 根据价格区间查询原料 -->
    <select id="selectByPriceRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM materials
        WHERE price BETWEEN #{minPrice} AND #{maxPrice}
        ORDER BY price ASC
    </select>

    <!-- 统计各类型原料数量 -->
    <select id="selectTypeStatistics" resultType="java.util.Map">
        SELECT 
            type,
            COUNT(*) as count,
            AVG(price) as avgPrice,
            MIN(price) as minPrice,
            MAX(price) as maxPrice
        FROM materials
        GROUP BY type
        ORDER BY count DESC
    </select>

</mapper>
