<template>
  <div class="material-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            v-if="fromPage"
            type="default"
            @click="goBack"
            class="back-btn"
          >
            <component :is="useRenderIcon(ArrowLeft)" />
            返回{{ fromPageName }}
          </el-button>
          <div class="title-section">
            <h1 class="page-title">原料管理</h1>
            <p class="page-subtitle">管理生产所需的各种原料信息</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="primary" @click="handleAdd" class="add-btn">
            <template #icon>
              <component :is="useRenderIcon(Plus)" />
            </template>
            新增原料
          </el-button>
        </div>
      </div>

      <!-- 计划信息提示 -->
      <div v-if="highlightPlan" class="plan-info-card">
        <el-alert
          :title="`当前查看计划: ${highlightPlan} 的原料信息`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card" shadow="never">
        <template #header>
          <div class="search-header">
            <el-icon class="search-icon"><Search /></el-icon>
            <span class="search-title">筛选条件</span>
          </div>
        </template>

        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="原料名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入原料名称"
              clearable
              prefix-icon="Search"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="原料类型">
            <el-select
              v-model="searchForm.type"
              placeholder="请选择原料类型"
              clearable
              style="width: 180px"
            >
              <el-option label="半导体材料" value="半导体材料" />
              <el-option label="导电材料" value="导电材料" />
              <el-option label="绝缘材料" value="绝缘材料" />
              <el-option label="焊接材料" value="焊接材料" />
              <el-option label="电路板材料" value="电路板材料" />
              <el-option label="电子元件" value="电子元件" />
              <el-option label="芯片" value="芯片" />
              <el-option label="连接材料" value="连接材料" />
              <el-option label="封装材料" value="封装材料" />
              <el-option label="键合材料" value="键合材料" />
              <el-option label="导电浆料" value="导电浆料" />
              <el-option label="焊接辅料" value="焊接辅料" />
              <el-option label="化学试剂" value="化学试剂" />
              <el-option label="发光器件" value="发光器件" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" class="search-btn">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset" class="reset-btn">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card" shadow="hover">
            <el-statistic title="原料总数" :value="tableData.length" suffix="种">
              <template #prefix>
                <el-icon class="stats-icon" style="color: #409eff"><Box /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="hover">
            <el-statistic title="电子元件" :value="normalStockCount" suffix="种">
              <template #prefix>
                <el-icon class="stats-icon" style="color: #67c23a"><CircleCheck /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="hover">
            <el-statistic title="半导体材料" :value="warningStockCount" suffix="种">
              <template #prefix>
                <el-icon class="stats-icon" style="color: #e6a23c"><Warning /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card" shadow="hover">
            <el-statistic title="高价原料" :value="shortageStockCount" suffix="种">
              <template #prefix>
                <el-icon class="stats-icon" style="color: #f56c6c"><CircleClose /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="table-header">
            <span class="table-title">原料列表</span>
            <div class="table-actions">
              <el-button size="small" @click="exportData">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              <el-button size="small" @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="tableData"
          style="width: 100%"
          class="modern-table"
          v-loading="loading"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
            borderBottom: '2px solid #e5e7eb'
          }"
          :row-style="{ height: '60px' }"
          stripe
        >
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small" round>{{ row.id }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="code" label="原料编号" width="120" align="center">
            <template #default="{ row }">
              <div class="material-code">
                <el-tag type="primary" size="small">{{ row.code || 'MAT-' + row.id }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="name" label="原料名称" min-width="180">
            <template #default="{ row }">
              <div class="material-name-cell">
                <el-avatar
                  :size="40"
                  :src="row.pic"
                  class="material-avatar"
                >
                  <el-icon><Box /></el-icon>
                </el-avatar>
                <div class="material-info">
                  <div class="material-name">{{ row.name }}</div>
                  <div class="material-unit">{{ row.unit }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="type" label="原料类型" width="140" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getMaterialTagType(row.type)"
                size="small"
                round
              >
                <el-icon style="margin-right: 4px">
                  <component :is="getMaterialIcon(row.type)" />
                </el-icon>
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="unit" label="计量单位" width="100" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small" plain>{{ row.unit }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="price" label="单价" width="120" align="center">
            <template #default="{ row }">
              <div class="price-cell">
                <span class="price-symbol">¥</span>
                <span class="price-value">{{ row.price.toFixed(2) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="quantity" label="库存数量" width="120" align="center">
            <template #default="{ row }">
              <div class="quantity-cell">
                <el-tag
                  :type="getQuantityTagType(row.quantity || 0)"
                  size="small"
                >
                  {{ row.quantity || 0 }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="pic" label="图片" width="100" align="center">
            <template #default="{ row }">
              <div class="image-cell">
                <el-image
                  v-if="row.pic"
                  :src="row.pic"
                  class="material-image"
                  fit="cover"
                  :preview-src-list="[row.pic]"
                  :preview-teleported="true"
                  loading="lazy"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
                <div v-else class="no-image">
                  <el-icon><Picture /></el-icon>
                  <span>无图片</span>
                </div>
              </div>
            </template>
          </el-table-column>
      <el-table-column label="操作" width="200" fixed="right" align="center">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-tooltip content="编辑原料信息" placement="top">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(row)"
                class="action-btn edit-btn"
                round
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </el-tooltip>

            <el-tooltip content="查看关联产品" placement="top">
              <el-button
                size="small"
                type="success"
                @click="viewProducts(row)"
                class="action-btn product-btn"
                round
              >
                <el-icon><Connection /></el-icon>
                关联产品
              </el-button>
            </el-tooltip>

            <el-tooltip content="删除原料" placement="top">
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(row)"
                class="action-btn delete-btn"
                round
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="pagination-section">
          <el-pagination
            background
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="modern-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="dialog-form"
      >
        <el-form-item label="原料编号" prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入原料编号，如：MAT-001"
            clearable
          />
        </el-form-item>
        <el-form-item label="原料名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入原料名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="原料类型" prop="type">
          <el-input
            v-model="formData.type"
            placeholder="请输入原料类型"
            clearable
          />
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input
            v-model="formData.unit"
            placeholder="请输入计量单位"
            clearable
          />
        </el-form-item>
        <el-form-item label="单价" prop="price">
          <el-input-number
            v-model="formData.price"
            :min="0"
            :precision="2"
            placeholder="请输入单价"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="库存数量" prop="quantity">
          <el-input-number
            v-model="formData.quantity"
            :min="0"
            placeholder="请输入库存数量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="图片链接" prop="pic">
          <el-input
            v-model="formData.pic"
            placeholder="请输入图片链接"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import ArrowLeft from "@iconify-icons/ep/arrow-left";
import Plus from "@iconify-icons/ep/plus";
import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import Box from "@iconify-icons/ep/box";
import CircleCheck from "@iconify-icons/ep/circle-check";
import Warning from "@iconify-icons/ep/warning";
import CircleClose from "@iconify-icons/ep/circle-close";
import Download from "@iconify-icons/ep/download";
import Edit from "@iconify-icons/ep/edit";
import Connection from "@iconify-icons/ep/connection";
import Delete from "@iconify-icons/ep/delete";
import Picture from "@iconify-icons/ep/picture";
import Cpu from "@iconify-icons/ep/cpu";
import Lightning from "@iconify-icons/ep/lightning";
import Shield from "@iconify-icons/ep/star";
import Tools from "@iconify-icons/ep/tools";
import Grid from "@iconify-icons/ep/grid";
import Link from "@iconify-icons/ep/link";
import Package from "@iconify-icons/ep/box";
import {
  getMaterialPage,
  addMaterial,
  updateMaterial,
  deleteMaterial,
  deleteMaterialBatch,
  type Material,
  type MaterialQueryVO
} from '@/api/material'

defineOptions({
  name: 'MaterialList'
})

// 路由
const router = useRouter()
const route = useRoute()

// 从路由参数中获取信息
const fromPage = computed(() => route.query.from)
const fromPageName = computed(() => {
  if (route.query.from === 'production-plan') {
    return '生产计划'
  }
  return ''
})
const highlightPlan = computed(() => route.query.planName)

// 返回上一页
const goBack = () => {
  if (route.query.from === 'production-plan') {
    router.push('/production/plan/list')
  } else {
    router.go(-1)
  }
}

// 搜索表单
const searchForm = reactive<MaterialQueryVO>({
  name: '',
  type: '',
  current: 1,
  size: 10
})

// 统计计算属性
const normalStockCount = computed(() => {
  return tableData.value.filter(item => item.type === '电子元件').length
})

const warningStockCount = computed(() => {
  return tableData.value.filter(item => item.type === '半导体材料').length
})

const shortageStockCount = computed(() => {
  return tableData.value.filter(item => item.price > 100).length
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const tableData = ref<Material[]>([])
const loading = ref(false)

// 对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<Material>({
  code: '',
  name: '',
  type: '',
  unit: '',
  price: 0,
  quantity: 0,
  pic: ''
})

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入原料编号', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入原料名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请输入原料类型', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入计量单位', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入单价', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入库存数量', trigger: 'blur' }
  ]
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.name = ''
  searchForm.type = ''
  pagination.currentPage = 1
  loadData()
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能待实现')
}

// 刷新数据
const refreshData = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    unit: '',
    price: 0,
    pic: ''
  })
  formRef.value?.clearValidate()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增原料'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: Material) => {
  dialogTitle.value = '编辑原料'
  isEdit.value = true
  Object.assign(formData, { ...row })
  dialogVisible.value = true
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    let response
    if (isEdit.value) {
      response = await updateMaterial(formData)
    } else {
      response = await addMaterial(formData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '新增成功')
      handleDialogClose()
      loadData() // 重新加载数据
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}



// 查看关联产品
const viewProducts = (row: any) => {
  ElMessage.info(`查看 ${row.name} 的关联产品`)
  // TODO: 显示关联产品对话框
}



// 删除
const handleDelete = (row: Material) => {
  ElMessageBox.confirm('确定要删除这个原料吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteMaterial(row.id!)
      if (response.success) {
        ElMessage.success('删除成功')
        loadData() // 重新加载数据
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除原料失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params: MaterialQueryVO = {
      name: searchForm.name,
      type: searchForm.type,
      current: pagination.currentPage,
      size: pagination.pageSize
    }

    const response = await getMaterialPage(params)

    if (response.success) {
      // 为没有图片的原料添加默认图片
      const records = (response.data.records || []).map((item: Material, index: number) => {
        if (!item.pic) {
          // 使用电子元件相关的示例图片
          const defaultImages = [
            'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop',
            'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=200&h=200&fit=crop',
            'https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=200&h=200&fit=crop',
            'https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?w=200&h=200&fit=crop',
            'https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=200&h=200&fit=crop',
            'https://images.unsplash.com/photo-1581092921461-eab62e97a780?w=200&h=200&fit=crop'
          ]

          item.pic = defaultImages[index % defaultImages.length]
        }
        return item
      })

      tableData.value = records
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '查询失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载原料数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 获取原料类型图标
const getMaterialIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    '半导体材料': Cpu,
    '导电材料': Lightning,
    '绝缘材料': Shield,
    '焊接材料': Tools,
    '电路板材料': Grid,
    '电子元件': Cpu,
    '芯片': Cpu,
    '连接材料': Link,
    '封装材料': Package,
    '键合材料': Connection,
    '导电浆料': Lightning,
    '焊接辅料': Tools,
    '化学试剂': Warning,
    '发光器件': CircleCheck
  }
  return iconMap[type] || Box
}

// 获取原料类型标签类型
const getMaterialTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    '半导体材料': 'primary',
    '导电材料': 'warning',
    '绝缘材料': 'info',
    '焊接材料': 'success',
    '电路板材料': 'primary',
    '电子元件': 'success',
    '芯片': 'primary',
    '连接材料': 'info',
    '封装材料': 'warning',
    '键合材料': 'success',
    '导电浆料': 'warning',
    '焊接辅料': 'success',
    '化学试剂': 'danger',
    '发光器件': 'success'
  }
  return typeMap[type] || 'info'
}

// 获取库存数量标签类型
const getQuantityTagType = (quantity: number) => {
  if (quantity <= 0) {
    return 'danger'    // 无库存 - 红色
  } else if (quantity <= 50) {
    return 'warning'   // 低库存 - 橙色
  } else if (quantity <= 100) {
    return 'info'      // 中等库存 - 蓝色
  } else {
    return 'success'   // 充足库存 - 绿色
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.material-list {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #6b7280;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.add-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.plan-info-card {
  margin-top: 16px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 24px;
}

.search-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
}

.search-icon {
  color: #6366f1;
}

.search-title {
  font-size: 16px;
}

.search-form {
  margin: 0;
}

.search-btn, .reset-btn {
  border-radius: 6px;
  font-weight: 500;
}

.search-btn {
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 24px;
}

.stats-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  font-size: 20px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.modern-table :deep(.el-table__header) {
  background: #f8fafc;
}

.modern-table :deep(.el-table__header th) {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

.modern-table :deep(.el-table__row:hover) {
  background: #f0f9ff;
}

.modern-table :deep(.el-table .el-table__cell) {
  border-bottom: 1px solid #f1f5f9;
  padding: 16px 12px;
}

.modern-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #fafbfc;
}

/* 原料名称单元格 */
.material-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.material-avatar {
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.material-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.material-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.material-unit {
  font-size: 12px;
  color: #6b7280;
}

/* 原料编号单元格 */
.material-code {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 价格单元格 */
.price-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-weight: 600;
  color: #059669;
}

/* 库存数量单元格 */
.quantity-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.price-symbol {
  font-size: 12px;
}

.price-value {
  font-size: 16px;
}

/* 图片单元格 */
.image-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.material-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.material-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
  font-size: 12px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
  background: #fef0f0;
  padding: 8px;
  border-radius: 4px;
  width: 50px;
  height: 50px;
  justify-content: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.action-btn {
  border-radius: 16px;
  font-size: 11px;
  padding: 6px 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 70px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  width: 100%;
  max-width: 85px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.edit-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.product-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.product-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.action-btn .el-icon {
  font-size: 14px;
}

/* 分页样式 */
.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.modern-pagination {
  border-radius: 8px;
}

.modern-pagination :deep(.el-pagination__total) {
  color: #6b7280;
  font-weight: 500;
}

.modern-pagination :deep(.btn-next),
.modern-pagination :deep(.btn-prev) {
  border-radius: 6px;
}

.modern-pagination :deep(.el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .material-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .material-list {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-section .el-col {
    margin-bottom: 16px;
  }

  .table-section {
    overflow-x: auto;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
    width: 100%;
  }

  .action-btn {
    width: 100%;
    font-size: 11px;
    padding: 6px 12px;
  }
}

/* 动画效果 */
.el-card {
  transition: all 0.3s ease;
}

.el-button {
  transition: all 0.2s ease;
}

.el-table {
  transition: all 0.3s ease;
}

/* 自定义滚动条 */
:deep(.el-table__body-wrapper)::-webkit-scrollbar {
  height: 8px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
