package com.geek.factory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 产品查询VO
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@ApiModel(value = "ProductQueryVO", description = "产品查询参数")
public class ProductQueryVO {

    @ApiModelProperty(value = "产品名称", example = "智能手机")
    private String name;

    @ApiModelProperty(value = "产品型号", example = "MB-SM001")
    private String model;

    @ApiModelProperty(value = "产品分类", example = "主板类")
    private String category;

    @ApiModelProperty(value = "最小价格", example = "100.00")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最大价格", example = "1000.00")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100;
        }
    }
}
