package com.geek.factory.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description 统一结果集
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("统一结果集")
public class Result implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作是否成功", example = "true")
    private Boolean success;

    @ApiModelProperty(value = "返回消息", example = "操作成功")
    private String msg;

    @ApiModelProperty(value = "返回数据")
    private Object data;

    /**
     * 成功返回结果
     * @param data 返回数据
     * @return Result
     */
    public static Result success(Object data) {
        return new Result(true, "操作成功", data);
    }

    /**
     * 成功返回结果
     * @param msg 返回消息
     * @param data 返回数据
     * @return Result
     */
    public static Result success(String msg, Object data) {
        return new Result(true, msg, data);
    }

    /**
     * 成功返回结果（无数据）
     * @param msg 返回消息
     * @return Result
     */
    public static Result success(String msg) {
        return new Result(true, msg, null);
    }

    /**
     * 失败返回结果
     * @param msg 返回消息
     * @return Result
     */
    public static Result error(String msg) {
        return new Result(false, msg, null);
    }

    /**
     * 失败返回结果
     * @param msg 返回消息
     * @param data 返回数据
     * @return Result
     */
    public static Result error(String msg, Object data) {
        return new Result(false, msg, data);
    }
}
