package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.Product;
import com.geek.factory.vo.ProductQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 产品Mapper接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品列表
     * @param page 分页对象
     * @param query 查询条件
     * @return 产品分页列表
     */
    @Select("<script>" +
            "SELECT id, name, model, category, price, description, pic, create_time, update_time " +
            "FROM products WHERE 1=1 " +
            "<if test='query.name != null and query.name != \"\"'>" +
            "AND name LIKE CONCAT('%', #{query.name}, '%') " +
            "</if>" +
            "<if test='query.model != null and query.model != \"\"'>" +
            "AND model LIKE CONCAT('%', #{query.model}, '%') " +
            "</if>" +
            "<if test='query.category != null and query.category != \"\"'>" +
            "AND category = #{query.category} " +
            "</if>" +
            "<if test='query.minPrice != null'>" +
            "AND price >= #{query.minPrice} " +
            "</if>" +
            "<if test='query.maxPrice != null'>" +
            "AND price <= #{query.maxPrice} " +
            "</if>" +
            "ORDER BY create_time DESC, id DESC" +
            "</script>")
    IPage<Product> selectProductPage(Page<Product> page, @Param("query") ProductQueryVO query);

    /**
     * 根据分类查询产品列表
     * @param category 产品分类
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE category = #{category} ORDER BY create_time DESC")
    List<Product> selectByCategory(@Param("category") String category);

    /**
     * 根据价格区间查询产品
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 产品列表
     */
    @Select("SELECT * FROM products WHERE price BETWEEN #{minPrice} AND #{maxPrice} ORDER BY price ASC")
    List<Product> selectByPriceRange(@Param("minPrice") java.math.BigDecimal minPrice,
                                   @Param("maxPrice") java.math.BigDecimal maxPrice);

    /**
     * 查询产品分类统计
     * @return 分类统计列表
     */
    @Select("SELECT category, COUNT(*) as count FROM products GROUP BY category ORDER BY count DESC")
    List<java.util.Map<String, Object>> selectCategoryStatistics();
}
