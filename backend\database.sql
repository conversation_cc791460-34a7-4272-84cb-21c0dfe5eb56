-- 工厂管理系统数据库建表脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `factory` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `factory`;

-- 原料表
DROP TABLE IF EXISTS `materials`;
CREATE TABLE `materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) DEFAULT NULL COMMENT '原料编号',
  `name` varchar(100) NOT NULL COMMENT '原料名称',
  `type` varchar(50) NOT NULL COMMENT '原料类型',
  `unit` varchar(20) NOT NULL COMMENT '计量单位',
  `price` decimal(10,2) NOT NULL COMMENT '单价',
  `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '库存数量',
  `pic` varchar(255) DEFAULT NULL COMMENT '图片路径',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_type` (`type`),
  KEY `idx_price` (`price`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='原料表';

-- 产品表
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '产品名称',
  `model` varchar(50) NOT NULL COMMENT '产品型号',
  `category` varchar(50) NOT NULL COMMENT '产品分类',
  `price` decimal(10,2) NOT NULL COMMENT '产品单价',
  `description` text COMMENT '产品描述',
  `pic` varchar(255) DEFAULT NULL COMMENT '产品图片',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_model` (`model`),
  KEY `idx_category` (`category`),
  KEY `idx_price` (`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 产品原料关联表
DROP TABLE IF EXISTS `product_materials`;
CREATE TABLE `product_materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `material_id` int(11) NOT NULL COMMENT '原料ID',
  `quantity` decimal(10,3) NOT NULL COMMENT '用量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_material` (`product_id`,`material_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_material_id` (`material_id`),
  CONSTRAINT `fk_pm_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_pm_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品原料关联表';

-- 插入测试数据

-- 原料测试数据（包含库存数量）
INSERT INTO `materials` (`code`, `name`, `type`, `unit`, `price`, `quantity`, `pic`) VALUES
('MAT001', '硅晶圆', '半导体材料', '片', 125.50, 100.00, '/images/materials/silicon_wafer.jpg'),
('MAT002', '铜箔', '导电材料', 'kg', 68.80, 50.00, '/images/materials/copper_foil.jpg'),
('MAT003', '环氧树脂', '绝缘材料', 'kg', 45.20, 80.00, '/images/materials/epoxy_resin.jpg'),
('MAT004', '焊锡丝', '焊接材料', 'kg', 85.60, 30.00, '/images/materials/solder_wire.jpg'),
('MAT005', 'PCB基板', '电路板材料', '片', 12.30, 200.00, '/images/materials/pcb_substrate.jpg'),
('MAT006', '电阻器', '电子元件', '个', 0.05, 1000.00, '/images/materials/resistor.jpg'),
('MAT007', '电容器', '电子元件', '个', 0.12, 800.00, '/images/materials/capacitor.jpg'),
('MAT008', '集成电路', '芯片', '个', 15.80, 150.00, '/images/materials/ic_chip.jpg'),
('MAT009', '导线', '连接材料', 'm', 2.50, 500.00, '/images/materials/wire.jpg'),
('MAT010', '塑料外壳', '封装材料', '个', 3.20, 300.00, '/images/materials/plastic_case.jpg'),
('MAT011', '金属外壳', '封装材料', '个', 8.50, 120.00, '/images/materials/metal_case.jpg'),
('MAT012', '螺丝', '紧固件', '个', 0.08, 2000.00, '/images/materials/screw.jpg'),
('MAT013', '胶水', '粘合剂', 'ml', 0.15, 1000.00, '/images/materials/glue.jpg'),
('MAT014', '标签纸', '标识材料', '张', 0.02, 5000.00, '/images/materials/label.jpg'),
('MAT015', '包装盒', '包装材料', '个', 1.20, 500.00, '/images/materials/package_box.jpg');

-- 生产计划原料关联表
CREATE TABLE IF NOT EXISTS `production_plan_material` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '生产计划ID',
  `material_id` bigint NOT NULL COMMENT '原料ID',
  `required_quantity` decimal(10,2) NOT NULL COMMENT '需要数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_material_id` (`material_id`),
  CONSTRAINT `fk_plan_material_plan` FOREIGN KEY (`plan_id`) REFERENCES `production_plan` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_plan_material_material` FOREIGN KEY (`material_id`) REFERENCES `materials` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产计划原料关联表';

-- 产品测试数据
INSERT INTO `products` (`name`, `model`, `category`, `price`, `description`, `pic`) VALUES
('智能手机主板', 'MB-SM001', '主板类', 280.50, '高性能智能手机主板，支持5G网络', '/images/products/smartphone_board.jpg'),
('LED显示屏', 'LED-D032', '显示器件', 450.80, '32寸高清LED显示屏', '/images/products/led_display.jpg'),
('电源适配器', 'PSU-65W', '电源类', 85.60, '65W笔记本电源适配器', '/images/products/power_adapter.jpg'),
('蓝牙音响', 'BT-SP001', '音频设备', 120.30, '便携式蓝牙音响', '/images/products/bluetooth_speaker.jpg'),
('USB数据线', 'USB-C001', '连接线', 25.50, 'Type-C数据传输线', '/images/products/usb_cable.jpg'),
('无线充电器', 'WC-QI001', '充电设备', 95.20, '15W无线快充充电器', '/images/products/wireless_charger.jpg'),
('智能手表', 'SW-001', '可穿戴设备', 680.00, '多功能智能手表', '/images/products/smart_watch.jpg'),
('车载充电器', 'CC-001', '车载设备', 45.80, '双USB车载充电器', '/images/products/car_charger.jpg'),
('平板电脑主板', 'MB-TB001', '主板类', 320.00, '10寸平板电脑主板', '/images/products/tablet_board.jpg'),
('笔记本键盘', 'KB-NB001', '输入设备', 65.00, '背光笔记本键盘', '/images/products/notebook_keyboard.jpg');

-- 产品原料关联测试数据
INSERT INTO `product_materials` (`product_id`, `material_id`, `quantity`, `unit`) VALUES
-- 智能手机主板需要的原料
(1, 1, 1.000, '片'),    -- 硅晶圆
(1, 2, 0.050, 'kg'),    -- 铜箔
(1, 5, 1.000, '片'),    -- PCB基板
(1, 6, 50.000, '个'),   -- 电阻器
(1, 7, 30.000, '个'),   -- 电容器
(1, 8, 5.000, '个'),    -- 集成电路
-- LED显示屏需要的原料
(2, 1, 2.000, '片'),    -- 硅晶圆
(2, 2, 0.200, 'kg'),    -- 铜箔
(2, 11, 1.000, '个'),   -- 金属外壳
(2, 9, 5.000, 'm'),     -- 导线
-- 电源适配器需要的原料
(3, 2, 0.100, 'kg'),    -- 铜箔
(3, 3, 0.050, 'kg'),    -- 环氧树脂
(3, 10, 1.000, '个'),   -- 塑料外壳
(3, 9, 2.000, 'm');     -- 导线
