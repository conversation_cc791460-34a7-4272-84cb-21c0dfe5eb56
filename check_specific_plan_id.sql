-- 检查特定生产计划ID的原料消耗记录

-- 检查生产计划 194183190042343010 是否有原料消耗记录
SELECT '=== 检查计划 194183190042343010 的原料消耗 ===' as info;
SELECT 
    ppm.plan_id,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 194183190042343010;

-- 如果上面没有数据，检查这个计划是否存在
SELECT '=== 检查计划 194183190042343010 是否存在 ===' as info;
SELECT id, plan_code, product_id, quantity 
FROM production_plan 
WHERE id = 194183190042343010;

-- 查看有原料消耗记录的计划ID（前10个）
SELECT '=== 有原料消耗记录的计划ID ===' as info;
SELECT DISTINCT plan_id 
FROM production_plan_material 
ORDER BY plan_id 
LIMIT 10;

-- 为计划 194183190042343010 添加原料消耗记录（如果该计划存在且没有原料记录）
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT 194183190042343010, m.id, 
  CASE 
    WHEN m.id % 5 = 1 THEN 50.00
    WHEN m.id % 5 = 2 THEN 25.50  
    WHEN m.id % 5 = 3 THEN 15.20
    WHEN m.id % 5 = 4 THEN 100.00
    ELSE 30.00
  END as required_quantity,
  COALESCE(m.unit, '个') as unit,
  CONCAT(m.name, ' - 测试数据') as remark,
  NOW()
FROM materials m 
WHERE EXISTS (SELECT 1 FROM production_plan WHERE id = 194183190042343010)
AND NOT EXISTS (SELECT 1 FROM production_plan_material WHERE plan_id = 194183190042343010)
LIMIT 5;

-- 再次检查是否插入成功
SELECT '=== 再次检查计划 194183190042343010 ===' as info;
SELECT 
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 194183190042343010
ORDER BY ppm.material_id;
