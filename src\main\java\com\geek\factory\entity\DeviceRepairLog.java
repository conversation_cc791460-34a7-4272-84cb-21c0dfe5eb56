package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 设备维修记录日志实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@TableName("device_repair_log")
@ApiModel(value = "DeviceRepairLog", description = "设备维修记录日志实体")
public class DeviceRepairLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    @ApiModelProperty(value = "记录ID")
    private Long id;

    @TableField("repair_id")
    @ApiModelProperty(value = "关联的维修记录ID")
    private Long repairId;

    @TableField("product_line_id")
    @ApiModelProperty(value = "产线ID")
    private Long productLineId;

    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField("operator")
    @ApiModelProperty(value = "操作员")
    private String operator;

    @TableField("status")
    @ApiModelProperty(value = "状态")
    private String status;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
