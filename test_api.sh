#!/bin/bash

# 测试生产计划原料消耗API的脚本

echo "🧪 测试生产计划原料消耗API"
echo "================================"

# 设置API基础URL
BASE_URL="http://localhost:8080"

# 测试用的生产计划ID（从日志中获取）
PLAN_ID="1941332966093598700"

echo "📋 测试计划ID: $PLAN_ID"
echo ""

# 测试API调用
echo "🔍 调用API: GET $BASE_URL/api/plan/materials/$PLAN_ID"
echo ""

# 使用curl测试API
response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzUxMiIsInppcCI6IkdaSVAifQ.H4sIAAAAAAAAAKtWKi5NUrJScnN0DvEPitQNDXYNUtJRSq0oULIyNDc1NLMwNzAy0lEqLU4t8kwBikGYfom5qUBdVcVKtQAlUzgiQgAAAA.zIjiye0_RvFMvwOscxnTpjM3Wt_m6lJVVeJ1HPW4ahDhe_gLKlkDFPnfki0vHNuKz3pa9T0YRqu14vjMVk0zDw" \
  "$BASE_URL/api/plan/materials/$PLAN_ID")

# 分离响应体和HTTP状态码
http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "📊 响应结果:"
echo "HTTP状态码: $http_code"
echo "响应内容:"
echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"

echo ""
echo "🔧 如果返回空数据，请执行以下步骤:"
echo "1. 确保数据库中存在 production_plan_materials 表"
echo "2. 执行测试数据插入脚本:"
echo "   mysql -u root -p factory < backend/insert_test_plan_materials.sql"
echo "3. 重新测试API"

echo ""
echo "📝 数据库查询验证:"
echo "SELECT COUNT(*) FROM production_plan_materials WHERE plan_id = $PLAN_ID;"
