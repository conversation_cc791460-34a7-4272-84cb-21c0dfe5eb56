# 智慧工厂项目网关配置说明

## 概述

本项目已配置Spring Cloud Gateway网关，实现前后端请求的统一路由和跨域处理。

## 网关配置

### 后端网关配置 (application.properties)

```properties
# 端口号
server.port=80

# 微服务名
spring.application.name=server_gateway

# Nacos服务注册中心
spring.cloud.nacos.discovery.server-addr=47.117.102.110

# Gateway 反向代理
spring.cloud.gateway.discovery.locator.enabled=true

# ------------- 智慧工厂模块路由配置 -------------
# 主要API服务路由
spring.cloud.gateway.routes[2].id=service-factory
spring.cloud.gateway.routes[2].uri=lb://service-factory
spring.cloud.gateway.routes[2].predicates[0]=Path=/api/**

# 文件服务路由
spring.cloud.gateway.routes[3].id=service-factory-file
spring.cloud.gateway.routes[3].uri=lb://service-factory-file
spring.cloud.gateway.routes[3].predicates[0]=Path=/file/**

# WebSocket服务路由
spring.cloud.gateway.routes[4].id=service-factory-websocket
spring.cloud.gateway.routes[4].uri=lb://service-factory-websocket
spring.cloud.gateway.routes[4].predicates[0]=Path=/ws/**

# Gateway 跨域配置
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-origin-patterns=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.cloud.gateway.globalcors.cors-configurations.[/**].allowed-headers=*
spring.cloud.gateway.globalcors.cors-configurations.[/**].allow-credentials=true
spring.cloud.gateway.globalcors.cors-configurations.[/**].max-age=3600
```

### 前端配置

#### 1. 环境变量配置

**开发环境 (.env.development)**
```env
# 网关配置
VITE_GATEWAY_URL = "http://localhost:80"
VITE_GATEWAY_WS_URL = "ws://localhost:80"
VITE_USE_GATEWAY = true
```

**生产环境 (.env.production)**
```env
# 网关配置
VITE_GATEWAY_URL = "http://your-gateway-domain:80"
VITE_GATEWAY_WS_URL = "ws://your-gateway-domain:80"
VITE_USE_GATEWAY = true
```

#### 2. Vite代理配置 (vite.config.ts)

```typescript
proxy: {
  // 通过网关代理API请求
  "/api": {
    target: "http://localhost:80", // 网关端口
    changeOrigin: true, // 允许跨域
    // 不重写路径，保持/api前缀，让网关进行路由
  },
  // WebSocket代理
  "/ws": {
    target: "ws://localhost:80", // 网关端口，WebSocket协议
    ws: true, // 启用WebSocket代理
    changeOrigin: true // 允许跨域
  },
  // 文件服务代理
  "/file": {
    target: "http://localhost:80", // 网关端口
    changeOrigin: true // 允许跨域
  }
}
```

#### 3. 网关工具函数 (src/utils/gateway.ts)

提供了以下工具函数：
- `getGatewayUrl()` - 获取HTTP网关地址
- `getGatewayWsUrl()` - 获取WebSocket网关地址
- `buildWebSocketUrl()` - 构建WebSocket URL
- `buildApiUrl()` - 构建API URL
- `buildFileUrl()` - 构建文件服务URL

## 路由规则

| 前端路径 | 网关路由 | 后端服务 | 说明 |
|---------|---------|---------|------|
| `/api/**` | `service-factory` | 主要业务API | 生产计划、产品、原料等API |
| `/file/**` | `service-factory-file` | 文件服务 | 图片上传、文件下载等 |
| `/ws/**` | `service-factory-websocket` | WebSocket服务 | 实时消息推送 |

## 微服务注册

确保以下微服务在Nacos中正确注册：

1. **service-factory** - 主要的工厂管理服务
2. **service-factory-file** - 文件服务（如果单独部署）
3. **service-factory-websocket** - WebSocket服务（如果单独部署）

## 使用说明

### 1. API请求

前端API请求会自动通过网关路由：
```javascript
// 原有的API调用方式不变
import { http } from "@/utils/http";

// 这个请求会通过网关路由到 service-factory 服务
http.request("post", "/api/plan/list", { data: params });
```

### 2. WebSocket连接

WebSocket连接已更新为通过网关：
```javascript
import { buildWebSocketUrl } from "@/utils/gateway";

// 构建WebSocket URL
const wsUrl = buildWebSocketUrl('/reminder', { planId: '123' });
// 结果: ws://localhost:80/ws/reminder?planId=123

const socket = new WebSocket(wsUrl);
```

### 3. 文件上传

文件上传服务通过网关路由：
```javascript
// 上传配置
const uploadAction = '/api/file/upload'; // 会通过网关路由到文件服务
```

## 优势

1. **统一入口** - 所有请求通过网关统一处理
2. **跨域解决** - 网关层面解决跨域问题
3. **负载均衡** - 支持多实例负载均衡
4. **路由灵活** - 可以灵活配置路由规则
5. **监控统一** - 便于统一监控和日志管理

## 注意事项

1. 确保网关服务正常运行在80端口
2. 确保所有微服务在Nacos中正确注册
3. 生产环境需要修改网关域名配置
4. WebSocket连接需要网关支持WebSocket协议
