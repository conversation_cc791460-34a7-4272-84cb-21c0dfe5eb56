# 生产管理模块

## 模块概述

生产管理模块是一个完整的企业生产管理系统，包含生产计划、生产明细和排班管理三个核心子模块。

## 功能特性

### 1. 生产计划管理
- **计划创建**: 支持创建新的生产计划，包含计划编号、名称、产品信息等
- **计划编辑**: 可以修改现有生产计划的所有信息
- **计划查看**: 提供只读模式查看计划详情
- **计划删除**: 支持删除不需要的生产计划
- **状态管理**: 支持未开始、进行中、已完成、已暂停等状态
- **优先级管理**: 支持低、中、高三个优先级等级
- **进度跟踪**: 实时显示计划完成率

### 2. 生产明细管理
- **明细记录**: 记录具体的生产执行情况
- **批次管理**: 支持批次号管理，便于质量追溯
- **数量统计**: 包含计划数量、完成数量、不良数量
- **质量状态**: 支持待检验、合格、不合格三种质量状态
- **班次管理**: 支持早班、中班、晚班班次记录
- **操作员记录**: 记录具体操作员信息

### 3. 排班管理
- **员工排班**: 为员工安排具体的工作班次
- **时间管理**: 支持开始时间、结束时间、工作时长管理
- **加班管理**: 支持加班时长记录
- **状态跟踪**: 支持未开始、进行中、已完成、请假等状态
- **部门管理**: 按部门进行排班管理

## 技术特性

- **响应式设计**: 适配不同屏幕尺寸
- **国际化支持**: 支持中英文切换
- **表单验证**: 完整的表单验证机制
- **分页查询**: 支持大数据量的分页显示
- **搜索过滤**: 多条件搜索和过滤功能
- **状态管理**: 使用Vue 3 Composition API
- **类型安全**: 完整的TypeScript类型定义

## 文件结构

```
src/views/production/
├── README.md                 # 模块说明文档
├── plan/                     # 生产计划模块
│   ├── list.vue             # 计划列表页面
│   ├── add.vue              # 新增计划页面
│   └── edit.vue             # 编辑计划页面
├── detail/                   # 生产明细模块
│   ├── list.vue             # 明细列表页面
│   ├── add.vue              # 新增明细页面
│   └── edit.vue             # 编辑明细页面
└── schedule/                 # 排班管理模块
    ├── list.vue             # 排班列表页面
    ├── add.vue              # 新增排班页面
    └── edit.vue             # 编辑排班页面
```

## API接口

### 生产计划API
- `GET /api/production/plan` - 获取计划列表
- `GET /api/production/plan/:id` - 获取计划详情
- `POST /api/production/plan` - 创建计划
- `PUT /api/production/plan/:id` - 更新计划
- `DELETE /api/production/plan/:id` - 删除计划

### 生产明细API
- `GET /api/production/detail` - 获取明细列表
- `GET /api/production/detail/:id` - 获取明细详情
- `POST /api/production/detail` - 创建明细
- `PUT /api/production/detail/:id` - 更新明细
- `DELETE /api/production/detail/:id` - 删除明细

### 排班管理API
- `GET /api/production/schedule` - 获取排班列表
- `GET /api/production/schedule/:id` - 获取排班详情
- `POST /api/production/schedule` - 创建排班
- `PUT /api/production/schedule/:id` - 更新排班
- `DELETE /api/production/schedule/:id` - 删除排班

## 使用说明

1. **路由配置**: 模块已配置在 `/production` 路径下
2. **权限控制**: 需要配置相应的菜单权限
3. **国际化**: 支持中英文切换，翻译文件已配置
4. **数据格式**: 所有API接口都遵循RESTful规范

## 开发说明

- 使用Vue 3 + TypeScript开发
- 使用Element Plus作为UI组件库
- 使用Vue Router进行路由管理
- 使用Pinia进行状态管理
- 支持热重载和开发调试

## 注意事项

1. 确保后端API接口已正确实现
2. 注意数据验证和错误处理
3. 考虑性能优化，特别是大数据量场景
4. 注意用户体验，提供友好的交互反馈 