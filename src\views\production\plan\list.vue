<template>
  <div class="production-plan-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon class="title-icon"><Document /></el-icon>
          生产计划管理
        </h2>
        <p class="page-subtitle">管理和监控生产计划的执行情况</p>
      </div>
      <div class="header-right">
        <el-button type="primary" size="large" @click="openAdd">
          <template #icon>
            <component :is="useRenderIcon(Plus)" />
          </template>
          新增生产计划
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card shadow="never" class="search-card">
        <div class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-input
                v-model="searchForm.planCode"
                placeholder="请输入计划编号"
                clearable
                prefix-icon="Search"
              />
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择状态"
                clearable
                style="width: 100%"
              >
                <el-option label="未开始" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="已完成" :value="2" />
                <el-option label="已暂停" :value="3" />
              </el-select>
            </el-col>
            <el-col :span="12">
              <div class="search-buttons">
                <el-button type="primary" @click="handleSearch">
                  <template #icon>
                    <component :is="useRenderIcon(Search)" />
                  </template>
                  查询
                </el-button>
                <el-button @click="resetSearch">
                  <template #icon>
                    <component :is="useRenderIcon(Refresh)" />
                  </template>
                  重置
                </el-button>
                <el-button type="warning" @click="handleFixPlanCodes" :loading="fixLoading">
                  <template #icon>
                    <component :is="useRenderIcon(Edit)" />
                  </template>
                  修复计划编号
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card shadow="never" class="table-card">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column prop="planCode" label="计划编号" width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="plan-info">
                <div class="plan-code">{{ row.planCode || 'PLAN-' + row.id }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="productName" label="产品信息" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="product-info">
                <div class="product-name">{{ row.productName || '未知产品' }}</div>
                <div class="production-line">{{ row.productLineName || '未知产线' }}</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="quantity" label="生产数量" width="120" align="center">
            <template #default="{ row }">
              <div class="quantity-info">
                <el-tag type="primary" size="large">{{ row.quantity || 0 }} 件</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="planStartTime" label="开始日期" width="160" align="center">
            <template #default="{ row }">
              <div class="date-info">
                <el-icon class="date-icon"><Calendar /></el-icon>
                {{ formatDateTime(row.planStartTime) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="completeTime" label="完成日期" width="160" align="center">
            <template #default="{ row }">
              <div class="date-info">
                <el-icon class="date-icon"><Calendar /></el-icon>
                <span v-if="row.completeTime">{{ formatDateTime(row.completeTime) }}</span>
                <span v-else class="empty-date">-</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="large">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>



          <el-table-column label="操作" width="240" fixed="right" align="center">
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="openEdit(row)"
                  class="action-btn edit-btn"
                  round
                >
                  <template #icon>
                    <component :is="useRenderIcon(Edit)" />
                  </template>
                  编辑
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="viewMaterialConsume(row)"
                  class="action-btn material-btn"
                  round
                >
                  <template #icon>
                    <component :is="useRenderIcon(Box)" />
                  </template>
                  原料消耗
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row.id)"
                  class="action-btn delete-btn"
                  round
                >
                  <template #icon>
                    <component :is="useRenderIcon(Delete)" />
                  </template>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationConfig.total"
        :page-size="paginationConfig.size"
        :current-page="paginationConfig.current"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增/编辑 对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="70%"
      :close-on-click-modal="false"
      class="production-plan-dialog"
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划代码" prop="planCode">
              <el-input v-model="formData.planCode" placeholder="正在生成计划编号..." disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品ID">
              <el-input v-model="formData.productId" placeholder="选择产品后自动填充" disabled style="background-color: #f5f7fa;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品选择" prop="productId">
              <el-select
                v-model="formData.productId"
                placeholder="请选择产品"
                @change="handleProductChange"
                style="width: 100%"
              >
                <el-option
                  v-for="product in productOptions"
                  :key="product.id"
                  :label="`${product.name} (ID: ${product.id})`"
                  :value="product.id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: 500;">{{ product.name }}</span>
                    <span style="color: #8492a6; font-size: 12px;">ID: {{ product.id }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称">
              <el-input v-model="formData.productName" placeholder="选择产品后自动填充" disabled style="background-color: #f5f7fa;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="产线ID">
              <el-input v-model="formData.productLineId" placeholder="选择产线后自动填充" disabled style="background-color: #f5f7fa;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产线选择" prop="productLineId">
              <el-select
                v-model="formData.productLineId"
                placeholder="请选择产线"
                @change="handleProductLineChange"
                style="width: 100%"
              >
                <el-option
                  v-for="line in productionLineOptions"
                  :key="line.id"
                  :label="`${line.name} (${line.code})`"
                  :value="line.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产线名称">
              <el-input v-model="formData.productLineName" placeholder="选择产线后自动填充" disabled style="background-color: #f5f7fa;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="计划数量" prop="planQuantity">
              <el-input-number v-model="formData.planQuantity" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                @change="handleStartTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划完成时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="datetime"
                placeholder="选择完成时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled-date="disabledEndDate"
                :disabled-hours="disabledEndHours"
                :disabled-minutes="disabledEndMinutes"
                :disabled-seconds="disabledEndSeconds"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="未开始" :value="0">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <el-tag type="info" size="small">未开始</el-tag>
                    <span style="color: #666; font-size: 12px;">计划尚未开始执行</span>
                  </div>
                </el-option>
                <el-option label="进行中" :value="1">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <el-tag type="success" size="small">进行中</el-tag>
                    <span style="color: #666; font-size: 12px;">计划正在执行中</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 原料配置 -->
        <el-form-item label="原料配置">
          <div class="materials-section">
            <div class="materials-header">
              <el-button
                type="primary"
                size="small"
                @click="openMaterialDialog"
              >
                添加原料
              </el-button>
            </div>

            <el-table
              :data="formData.materials"
              border
              size="small"
              style="width: 100%"
              v-if="formData.materials && formData.materials.length > 0"
              class="materials-table"
            >
              <el-table-column prop="materialName" label="原料名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="requiredQuantity" label="数量" width="100">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.requiredQuantity"
                    :min="0"
                    :precision="2"
                    size="small"
                    :controls="false"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="单位" width="60" />
              <el-table-column label="操作" width="70" fixed="right">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeMaterial($index)"
                    :icon="Delete"
                    circle
                  />
                </template>
              </el-table-column>
            </el-table>

            <el-empty
              v-if="!formData.materials || formData.materials.length === 0"
              description="暂无原料配置"
              :image-size="60"
            />
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 原料选择对话框 -->
    <el-dialog
      v-model="showMaterialDialog"
      title="选择原料"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-table
        :data="availableMaterials"
        border
        style="width: 100%"
        @selection-change="handleMaterialSelection"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="原料名称" />
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="price" label="单价" />
        <el-table-column label="库存状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStockStatusType(row.quantity)"
              size="small"
            >
              {{ row.quantity || 0 }} {{ row.unit }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="需求数量" width="150">
          <template #default="{ row }">
            <el-input-number
              v-model="row.tempQuantity"
              :min="0"
              :precision="2"
              size="small"
              style="width: 100%"
              placeholder="请输入数量"
              @change="updateTempQuantity(row)"
            />
          </template>
        </el-table-column>

      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMaterialDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmMaterialSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { http } from "@/utils/http";
import { buildWebSocketUrl } from "@/utils/gateway";

import Document from "@iconify-icons/ep/document";
import Plus from "@iconify-icons/ep/plus";
import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import Calendar from "@iconify-icons/ep/calendar";
import Edit from "@iconify-icons/ep/edit";
import Box from "@iconify-icons/ep/box";
import Delete from "@iconify-icons/ep/delete";

import {
  getProductionPlanList,
  getProductionPlanById,
  createProductionPlan,
  updateProductionPlan,
  deleteProductionPlan,
  getNextPlanCode,
  fixPlanCodes,
  type CreateProductionPlanDTO
} from "@/api/production";
import { getAllMaterials, validateMaterialStock, type Material, type MaterialWithTemp, type MaterialUsageDTO } from "@/api/material";

// 路由
const router = useRouter();

// 搜索
const searchForm = reactive({
  planCode: "",
  productId: undefined as number | undefined,
  productLineId: undefined as number | undefined,
  status: undefined as number | undefined
});

// 分页
const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const fixLoading = ref(false);

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref<FormInstance>();
const isEdit = ref(false);

// 产品相关
const productOptions = ref([]);

// 产线相关
const productionLineOptions = ref([]);

// 原料相关状态
const showMaterialDialog = ref(false);
const availableMaterials = ref<MaterialWithTemp[]>([]);
const selectedMaterials = ref<MaterialWithTemp[]>([]);

// 表单数据
const initialFormData = {
  id: null,
  planCode: "",
  productId: null,
  productName: "",
  productLineId: null,
  productLineName: "",
  planQuantity: 0,
  actualQuantity: 0,
  startDate: "",
  endDate: "",
  status: 0,
  materials: [] as MaterialUsageDTO[]
};
const formData = ref({ ...initialFormData });

// 表单校验
const formRules = reactive<FormRules>({
  planCode: [{ required: true, message: "计划代码为必填项", trigger: "blur" }],
  productId: [{ required: true, message: "请选择产品", trigger: "change" }],
  productLineId: [{ required: true, message: "请选择产线", trigger: "change" }],
  planQuantity: [{ required: true, message: "计划数量为必填项", trigger: "blur" }],
  startDate: [
    { required: true, message: "计划开始时间为必填项", trigger: "blur" }
  ],
  endDate: [
    { required: true, message: "计划完成时间为必填项", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value && formData.value.startDate) {
          if (new Date(value) <= new Date(formData.value.startDate)) {
            callback(new Error("完成时间必须晚于开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
});

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      current: paginationConfig.current,
      size: paginationConfig.size,
      ...searchForm
    };
    console.log('=== 获取列表请求参数 ===');
    console.log('请求参数:', params);

    const res = await getProductionPlanList(params);
    console.log('=== 生产计划API响应 ===');
    console.log('完整响应:', res);

    if (res && res.success && res.data) {
      console.log('=== 分页数据详情 ===');
      console.log('records数量:', res.data.records?.length || 0);
      console.log('total总数:', res.data.total);
      console.log('current当前页:', res.data.current);
      console.log('size页大小:', res.data.size);

      // 检查是否有重复的ID
      const records = res.data.records || [];
      const ids = records.map(item => item.id);
      const uniqueIds = [...new Set(ids)];
      console.log('记录ID数组:', ids);
      console.log('去重后ID数组:', uniqueIds);
      console.log('是否有重复ID:', ids.length !== uniqueIds.length);

      tableData.value = records;
      paginationConfig.total = res.data.total || 0;
      paginationConfig.current = res.data.current || 1;
      paginationConfig.size = res.data.size || 10;

      // 调试：检查完成时间字段
      if (tableData.value.length > 0) {
        console.log('第一条记录的完成时间(completeTime):', tableData.value[0].completeTime);
        console.log('第一条记录的完整数据:', JSON.stringify(tableData.value[0], null, 2));
      }
    } else {
      console.log('API响应失败或无数据');
      tableData.value = [];
      paginationConfig.total = 0;
    }
  } catch (error) {
    console.error("获取列表失败:", error);
    ElMessage.error("获取列表失败");
    tableData.value = [];
    paginationConfig.total = 0;
  } finally {
    loading.value = false;
  }
};

// 获取产品列表
const getProductOptions = async () => {
  try {
    // 调用产品API获取所有产品
    const response = await http.request({
      method: 'get',
      url: '/api/product/all'
    });

    if (response.success && response.data) {
      // /api/product/all 直接返回产品数组
      if (Array.isArray(response.data)) {
        productOptions.value = response.data;
      } else {
        productOptions.value = [];
      }
      console.log('产品选项加载成功:', productOptions.value);
    } else {
      console.warn('产品API返回异常:', response);
      // 使用备用数据
      productOptions.value = [
        { id: 1, name: "iPhone 15", model: "A2884" },
        { id: 2, name: "华为MateBook X Pro", model: "2023" },
        { id: 3, name: "小米平板6 Pro", model: "P6P" },
        { id: 4, name: "Apple Watch S8", model: "A2770" },
        { id: 5, name: "索尼WH-1000XM5", model: "XM5" }
      ];
    }
  } catch (error) {
    console.error('获取产品列表失败:', error);
    // 使用备用数据
    productOptions.value = [
      { id: 1, name: "iPhone 15", model: "A2884" },
      { id: 2, name: "华为MateBook X Pro", model: "2023" },
      { id: 3, name: "小米平板6 Pro", model: "P6P" },
      { id: 4, name: "Apple Watch S8", model: "A2770" },
      { id: 5, name: "索尼WH-1000XM5", model: "XM5" }
    ];
  }
};

// 分页变化
const handleCurrentChange = (page: number) => {
  paginationConfig.current = page;
  getList();
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  paginationConfig.size = size;
  paginationConfig.current = 1;
  getList();
};

// 删除
const handleDelete = (id: string) => {
  console.log('=== 删除操作开始 ===');
  console.log('准备删除生产计划，ID:', id);
  console.log('ID类型:', typeof id);
  console.log('ID字符串值:', String(id));

  ElMessageBox.confirm("确认删除该生产计划吗？", "提示", { type: "warning" })
    .then(async () => {
      console.log('用户确认删除');
      try {
        console.log('开始调用删除API，ID:', id);
        console.log('调用 deleteProductionPlan 函数...');

        // 确保 ID 是字符串格式
        const idStr = String(id);
        console.log('转换后的ID字符串:', idStr);

        const result = await deleteProductionPlan(idStr);
        console.log('删除API响应:', result);

        if (result && result.success) {
          ElMessage.success("删除成功");
          console.log('删除成功，刷新列表');
          getList();
        } else {
          console.error('删除失败，服务器返回:', result);
          ElMessage.error(result?.msg || "删除失败");
        }
      } catch (error) {
        console.error('删除请求异常:', error);
        console.error('错误详情:', JSON.stringify(error, null, 2));
        ElMessage.error("删除失败：" + (error.message || "未知错误"));
      }
    })
    .catch(() => {
      console.log('用户取消删除');
    });
};

// 重置搜索
const resetSearch = () => {
  searchForm.planCode = "";
  searchForm.productId = undefined;
  searchForm.productLineId = undefined;
  searchForm.status = undefined;
  paginationConfig.current = 1; // 重置到第一页
  getList();
};

// 搜索
const handleSearch = () => {
  paginationConfig.current = 1; // 重置到第一页
  getList();
};

// 修复计划编号
const handleFixPlanCodes = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将为所有没有计划编号的生产计划生成编号，是否继续？',
      '确认修复',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    fixLoading.value = true;
    const response = await fixPlanCodes();

    if (response.success) {
      ElMessage.success(`修复成功，共修复 ${response.data} 条记录`);
      // 刷新列表
      await getList();
    } else {
      ElMessage.error(response.msg || '修复失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修复计划编号失败:', error);
      ElMessage.error('修复失败，请稍后重试');
    }
  } finally {
    fixLoading.value = false;
  }
};

// 日期格式化函数
const formatDateTime = (dateTime: string | null | undefined) => {
  if (!dateTime || dateTime === null || dateTime === undefined || dateTime === '') {
    return '-';
  }

  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) {
      return '-';
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return '-';
  }
};

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    "未开始": "info",
    "生产中": "success",
    "已完成": "success",
    "已取消": "danger",
    "已暂停": "warning"
  };
  return statusMap[status] || "info";
};

const getStatusText = (status: string | number) => {
  // 将后端状态值转换为友好的显示文本
  const statusValue = String(status);

  if (statusValue === "0") return "未开始";
  if (statusValue === "1") return "进行中";
  if (statusValue === "2") return "已完成";
  if (statusValue === "3") return "已暂停";
  if (statusValue === "completed") return "已完成";

  // 如果已经是中文状态，直接返回
  if (["未开始", "进行中", "已完成", "已暂停"].includes(statusValue)) {
    return statusValue;
  }

  return status || "未知";
};



// 打开新增对话框
const openAdd = async () => {
  isEdit.value = false;
  dialogTitle.value = "新增生产计划";

  // 重置表单数据
  formData.value = {
    ...initialFormData,
    planCode: "正在生成..." // 先显示加载状态
  };

  dialogVisible.value = true;

  // 获取计划编号
  try {
    const response = await getNextPlanCode();
    formData.value.planCode = response.data;
    console.log("获取到计划编号:", response.data);
  } catch (error) {
    console.error("获取计划编号失败:", error);
    // 生成备用编号
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, "");
    formData.value.planCode = `PLAN-${today}-01`;
  }
};

// 打开编辑对话框
const openEdit = async (row: any) => {
  isEdit.value = true;
  dialogTitle.value = "编辑生产计划";

  // 复制基本数据并处理字段映射
  formData.value = {
    id: row.id, // 保存ID用于更新
    planCode: row.planCode || "",
    productId: row.productId,
    productName: row.productName || "",
    productLineId: row.productLineId,
    productLineName: row.productLineName || "",
    planQuantity: row.quantity || 0, // 注意：后端是quantity，前端表单是planQuantity
    status: row.status || "未开始",
    // 确保materials数组存在
    materials: [],
    // 处理日期字段格式，确保与日期选择器兼容
    startDate: row.planStartTime || "",
    endDate: row.completeTime || ""
  };

  console.log('编辑模式初始化数据:', JSON.stringify(formData.value, null, 2));

  // 获取该生产计划的原料配置
  try {
    const res = await getProductionPlanById(row.id);
    if (res && res.success && res.data && res.data.materials) {
      formData.value.materials = res.data.materials;
      console.log('编辑模式加载的原料数据:', formData.value.materials);
    }
  } catch (error) {
    console.error('获取生产计划原料配置失败:', error);
    ElMessage.warning('获取原料配置失败，请重试');
  }

  dialogVisible.value = true;
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 验证原料库存
const validateStock = async () => {
  if (!formData.value.materials || formData.value.materials.length === 0) {
    ElMessage.warning('请先添加原料');
    return false;
  }

  try {
    console.log('提交时验证库存，原料数据:', formData.value.materials);
    const res = await validateMaterialStock(formData.value.materials);
    console.log('提交时库存验证响应:', res);

    if (!res.success) {
      ElMessage.error(res.msg || '库存验证失败');
      return false;
    }
    return true;
  } catch (error) {
    console.error('提交时库存验证失败:', error);
    // 临时跳过验证，直接返回true
    ElMessage.warning('库存验证服务暂时不可用，将跳过验证');
    return true;
  }
};

// 提交表单
const submitForm = async () => {
  const valid = await new Promise(resolve => {
    formRef.value?.validate(resolve);
  });

  if (!valid) return;

  // 验证库存
  const stockValid = await validateStock();
  if (!stockValid) return;

  try {
    // 数据验证
    if (!formData.value.materials || formData.value.materials.length === 0) {
      ElMessage.warning('请至少添加一个原料配置');
      return;
    }

    if (!formData.value.planQuantity || formData.value.planQuantity <= 0) {
      ElMessage.warning('计划数量必须大于0');
      return;
    }

    // 格式化日期为后端期望的格式 (YYYY-MM-DD HH:mm:ss)
    const formatDateTimeForSubmit = (dateStr: string, time: string) => {
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day} ${time}`;
    };

    // 格式化时间为LocalDateTime格式 (yyyy-MM-dd HH:mm:ss) - 空格格式
    const formatToLocalDateTime = (dateStr: string) => {
      if (!dateStr) return null;

      // 如果是 ISO 格式 (包含T)，转换为空格格式
      if (dateStr.includes('T')) {
        return dateStr.replace('T', ' ');
      }

      // 如果已经是空格格式，直接返回
      if (dateStr.includes(' ')) {
        return dateStr;
      }

      // 如果只有日期，添加默认时间并使用空格格式
      return dateStr + ' 00:00:00';
    };

    // 准备提交数据，严格按照后端CreateProductionPlanDTO格式
    const submitData: CreateProductionPlanDTO = {
      productId: Number(formData.value.productId),
      productName: String(formData.value.productName || ""),
      code: String(formData.value.planCode || ""),
      productLineId: Number(formData.value.productLineId),
      productLineName: String(formData.value.productLineName || ""),
      quantity: Number(formData.value.planQuantity),
      planStartTime: formatToLocalDateTime(formData.value.startDate),
      completeTime: formatToLocalDateTime(formData.value.endDate),  // 改为completeTime
      // 原料数组，字段名必须是material不是materials
      material: formData.value.materials.map((m: any) => ({
        materialId: Number(m.materialId),
        requiredQuantity: Number(m.requiredQuantity),
        unit: String(m.unit || "个"),
        remark: String(m.remark || "主料")
      }))
    };

    console.log('=== 提交数据详情 ===');
    console.log('提交数据:', JSON.stringify(submitData, null, 2));
    console.log('表单原始数据:', JSON.stringify(formData.value, null, 2));
    console.log('是否编辑模式:', isEdit.value);

    if (isEdit.value) {
      // 编辑 - 使用UpdateProductionPlanDTO格式，包含原料信息
      console.log('执行编辑操作...');

      // 格式化时间为LocalDateTime格式 (yyyy-MM-dd HH:mm:ss) - 空格格式
      const formatToLocalDateTime = (dateStr: string) => {
        if (!dateStr) return null;

        // 如果是 ISO 格式 (包含T)，转换为空格格式
        if (dateStr.includes('T')) {
          return dateStr.replace('T', ' ');
        }

        // 如果已经是空格格式，直接返回
        if (dateStr.includes(' ')) {
          return dateStr;
        }

        // 如果只有日期，添加默认时间并使用空格格式
        return dateStr + ' 00:00:00';
      };

      // 准备编辑数据，按照UpdateProductionPlanDTO格式
      const updateData = {
        id: String(formData.value.id), // ID保持为字符串格式
        productId: Number(formData.value.productId),
        productLineId: Number(formData.value.productLineId),
        quantity: Number(formData.value.planQuantity),
        planStartTime: formatToLocalDateTime(formData.value.startDate),
        completeTime: formatToLocalDateTime(formData.value.endDate),
        // 原料数组，字段名必须是material不是materials
        material: formData.value.materials.map((m: any) => ({
          materialId: Number(m.materialId),
          requiredQuantity: Number(m.requiredQuantity),
          unit: String(m.unit || "个"),
          remark: String(m.remark || "主料")
        }))
      };

      console.log('编辑提交数据:', JSON.stringify(updateData, null, 2));
      const updateResponse = await updateProductionPlan(updateData);
      console.log('更新计划响应:', updateResponse);

      // 编辑成功后也重新建立WebSocket连接
      if (formData.value.id) {
        console.log(`🔗 重新为编辑的计划 ${formData.value.id} 建立WebSocket连接`);
        connectWebSocketForPlan(formData.value.id);
      }

      ElMessage.success("更新成功");
    } else {
      // 新增
      console.log('执行新增操作...');
      console.log('🎯 即将创建的计划编号:', submitData.code);

      const createResponse = await createProductionPlan(submitData);
      console.log('创建计划响应:', createResponse);

      ElMessage.success("新增成功");

      // 记录刚创建的计划编号，用于后续查找
      const createdPlanCode = submitData.code;
      console.log('📝 记录新创建的计划编号:', createdPlanCode);

      closeDialog();

      // 先刷新数据获取最新的计划列表（包括新创建的计划）
      await getList();

      // 刷新数据后，通过计划编号找到新创建的计划并建立WebSocket连接
      setTimeout(() => {
        console.log('🔄 数据刷新完成，查找新创建的计划...');

        // 通过计划编号查找新创建的计划
        const newPlan = tableData.value.find((plan: any) => plan.planCode === createdPlanCode);

        if (newPlan) {
          console.log(`🎯 找到新创建的计划: ID=${newPlan.id}, 编号=${newPlan.planCode}`);
          console.log(`� ID类型检查: ${typeof newPlan.id}, 原始值: ${newPlan.id}`);

          // 检查ID精度问题
          const originalId = newPlan.id;
          const idAsString = String(originalId);
          const idAsNumber = Number(originalId);

          console.log(`🔍 ID精度检查:`);
          console.log(`  - 原始ID: ${originalId}`);
          console.log(`  - 转为字符串: ${idAsString}`);
          console.log(`  - 转为数字: ${idAsNumber}`);
          console.log(`  - 精度是否丢失: ${originalId !== idAsNumber}`);

          console.log(`🔗 立即为新创建的计划 ${idAsString} 建立WebSocket连接`);

          // 强制为新创建的计划建立连接，使用字符串ID
          connectedPlans.delete(idAsString); // 先删除可能存在的连接记录
          connectWebSocketForPlan(idAsString);
        } else {
          console.warn(`⚠️ 未找到计划编号为 ${createdPlanCode} 的计划`);
          console.log('📊 当前表格数据中的计划编号:', tableData.value.map((p: any) => p.planCode));
          console.log('📊 当前表格数据中的计划ID:', tableData.value.map((p: any) => ({ id: p.id, code: p.planCode })));
        }

        // 同时为所有计划建立WebSocket连接（确保不遗漏）
        console.log('🔄 为所有计划建立WebSocket连接...');
        connectWebSocketForActivePlans();
      }, 1000); // 增加延迟确保数据完全加载
    }
  } catch (error: any) {
    console.error('提交失败:', error);

    // 详细错误处理
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.message || error.response.data?.msg || '未知错误';

      if (status === 401) {
        ElMessage.error('用户未登录或登录已过期，请重新登录');
        // 可以在这里跳转到登录页面
        // router.push('/login');
      } else if (status === 400) {
        ElMessage.error(`请求参数错误: ${message}`);
        console.error('400错误详情:', error.response.data);
      } else if (status === 403) {
        ElMessage.error('没有权限执行此操作');
      } else if (status === 500) {
        ElMessage.error('服务器内部错误，请联系管理员');
      } else {
        ElMessage.error(`操作失败: ${message}`);
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络');
    } else {
      ElMessage.error('操作失败，请稍后重试');
    }
  }
};

// 查看原料消耗 - 跳转到原料消耗界面
const viewMaterialConsume = (row: any) => {
  console.log('跳转到原料消耗页面，计划信息:', { id: row.id, planCode: row.planCode });

  if (!row.planCode) {
    ElMessage.warning("该生产计划缺少计划编号，无法查看原料消耗");
    return;
  }

  // 跳转到新的原料消耗界面，直接传递计划编码
  router.push({
    path: `/production/plan/material-consumption/${row.planCode}`
  });
};



// 处理产品选择变化
const handleProductChange = (value: number) => {
  const selectedProduct = productOptions.value.find(p => p.id === value);
  if (selectedProduct) {
    formData.value.productId = selectedProduct.id;
    formData.value.productName = selectedProduct.name;
  }
};

// 处理产线选择变化
const handleProductLineChange = (value: number) => {
  const selectedLine = productionLineOptions.value.find(line => line.id === value);
  if (selectedLine) {
    formData.value.productLineId = selectedLine.id;
    formData.value.productLineName = selectedLine.name;
  }
};

// 获取产线列表
const getProductionLineOptions = async () => {
  try {
    const response = await http.request({
      method: 'get',
      url: '/api/production/line/all'
    });

    if (response.success && response.data) {
      productionLineOptions.value = Array.isArray(response.data) ? response.data : [];
      console.log('产线选项加载成功:', productionLineOptions.value);
    } else {
      console.warn('产线API返回异常:', response);
      // 使用备用数据
      productionLineOptions.value = [
        { id: 1, name: "SMT贴片生产线", code: "SMT-01" },
        { id: 2, name: "包装生产线", code: "PKG-01" },
        { id: 3, name: "组装测试线", code: "ASM-01" },
        { id: 4, name: "DIP插件生产线", code: "DIP-01" },
        { id: 5, name: "返修检测线", code: "REP-01" }
      ];
    }
  } catch (error) {
    console.error('获取产线列表失败:', error);
    // 使用备用数据
    productionLineOptions.value = [
      { id: 1, name: "SMT贴片生产线", code: "SMT-01" },
      { id: 2, name: "包装生产线", code: "PKG-01" },
      { id: 3, name: "组装测试线", code: "ASM-01" },
      { id: 4, name: "DIP插件生产线", code: "DIP-01" },
      { id: 5, name: "返修检测线", code: "REP-01" }
    ];
  }
};

// 原料相关方法
const loadMaterials = async () => {
  try {
    const response = await getAllMaterials();
    if (response.success) {
      // 后端返回的是分页对象，需要取records字段
      let materialList = [];
      if (response.data && response.data.records) {
        // 分页数据格式
        materialList = response.data.records;
      } else if (Array.isArray(response.data)) {
        // 直接数组格式
        materialList = response.data;
      } else {
        materialList = [];
      }

      // 为每个原料添加临时字段用于在对话框中输入数量
      availableMaterials.value = materialList.map((material: any) => ({
        ...material,
        tempQuantity: 1 // 默认数量为1
      }));
      console.log('加载原料列表成功:', availableMaterials.value);
    } else {
      console.error('加载原料列表失败:', response.msg);
      ElMessage.error(response.msg || '加载原料列表失败');
    }
  } catch (error) {
    console.error('加载原料列表失败:', error);
    ElMessage.error('加载原料列表失败');
  }
};

// 打开原料选择对话框
const openMaterialDialog = async () => {
  try {
    // 先加载原料列表
    await loadMaterials();

    // 预先选择已有的原料
    selectedMaterials.value = availableMaterials.value.filter(material =>
      formData.value.materials.some(m => m.materialId === material.id)
    );

    // 为已选择的原料设置正确的数量
    selectedMaterials.value.forEach(material => {
      const existingMaterial = formData.value.materials.find(m => m.materialId === material.id);
      if (existingMaterial) {
        material.tempQuantity = existingMaterial.requiredQuantity;
      }
    });

    console.log('编辑模式预选择的原料:', selectedMaterials.value);
    showMaterialDialog.value = true;
  } catch (error) {
    console.error('打开原料选择对话框失败:', error);
    ElMessage.error('打开原料选择对话框失败');
  }
};

const handleMaterialSelection = (selection: MaterialWithTemp[]) => {
  selectedMaterials.value = selection;
  console.log('选中的原料:', selection.length, selection);
};

// 更新临时数量
const updateTempQuantity = (row: any) => {
  // 确保数量不小于0
  if (row.tempQuantity < 0) {
    row.tempQuantity = 0;
  }
};



const confirmMaterialSelection = async () => {
  // 验证选中的原料是否都有有效的数量
  const invalidMaterials = selectedMaterials.value.filter(material =>
    !material.tempQuantity || material.tempQuantity <= 0
  );

  if (invalidMaterials.length > 0) {
    ElMessage.warning('请为所有选中的原料设置有效的数量');
    return;
  }

  // 临时开关：是否启用库存验证
  const enableStockValidation = true; // 启用库存验证

  // 准备库存验证数据
  const materialsToValidate = selectedMaterials.value.map(material => ({
    materialId: Number(material.id!), // 确保是数字类型
    materialName: material.name,
    requiredQuantity: Number(material.tempQuantity || 1), // 确保是数字类型
    unit: material.unit
  }));

  // 验证库存（可选）
  if (enableStockValidation) {
    try {
      console.log('发送库存验证请求:', materialsToValidate);
      const stockRes = await validateMaterialStock(materialsToValidate);
      console.log('库存验证响应:', stockRes);

      if (!stockRes || !stockRes.success) {
        const errorMsg = stockRes?.msg || '库存验证失败';
        console.warn('库存验证未通过:', errorMsg);
        ElMessage.error(`库存验证失败：${errorMsg}`);
        return;
      }
      console.log('库存验证通过');
    } catch (error: any) {
      console.error('库存验证请求失败:', error);

      // 检查具体的错误类型
      if (error?.response) {
        const status = error.response.status;
        const data = error.response.data;
        console.error('HTTP错误 - 状态码:', status);
        console.error('HTTP错误 - 响应数据:', data);

        if (status === 404) {
          ElMessage.error('库存验证接口不存在，请联系管理员');
        } else if (status === 500) {
          ElMessage.error('服务器内部错误，请稍后重试');
        } else {
          ElMessage.error(`库存验证失败：${data?.msg || data?.message || '服务器错误'}`);
        }
      } else if (error?.request) {
        console.error('网络请求失败:', error.request);
        ElMessage.error('网络连接失败，请检查网络连接');
      } else {
        console.error('请求配置错误:', error.message);
        ElMessage.error(`请求错误：${error.message}`);
      }
      return;
    }
  } else {
    console.log('库存验证已禁用，跳过验证步骤');
  }

  // 记录成功添加的原料数量
  let addedCount = 0;
  let updatedCount = 0;

  selectedMaterials.value.forEach(material => {
    // 检查是否已经添加过该原料
    const exists = formData.value.materials.find(m => m.materialId === material.id);
    if (!exists) {
      formData.value.materials.push({
        materialId: material.id!,
        materialName: material.name,
        requiredQuantity: material.tempQuantity || 1,
        unit: material.unit,
        remark: "主料" // 添加默认备注
      });
      addedCount++;
    } else {
      // 如果已存在，更新数量
      exists.requiredQuantity = material.tempQuantity || 1;
      updatedCount++;
    }
  });

  // 显示成功消息
  if (addedCount > 0 && updatedCount > 0) {
    ElMessage.success(`库存验证通过！成功添加 ${addedCount} 个原料，更新 ${updatedCount} 个原料`);
  } else if (addedCount > 0) {
    ElMessage.success(`库存验证通过！成功添加 ${addedCount} 个原料`);
  } else if (updatedCount > 0) {
    ElMessage.success(`库存验证通过！成功更新 ${updatedCount} 个原料`);
  } else {
    ElMessage.info('未选择任何原料');
  }

  // 关闭对话框并清空选择
  showMaterialDialog.value = false;
  selectedMaterials.value = [];
};

const removeMaterial = (index: number) => {
  formData.value.materials.splice(index, 1);
};

// 获取库存状态类型
const getStockStatusType = (quantity: number) => {
  if (!quantity || quantity <= 0) {
    return 'danger';   // 无库存 - 红色
  } else if (quantity <= 10) {
    return 'warning';  // 库存不足 - 橙色
  } else {
    return 'success';  // 库存充足 - 绿色
  }
};

// 时间验证逻辑
const handleStartTimeChange = (value: string) => {
  if (value && formData.value.endDate) {
    // 如果完成时间早于开始时间，清空完成时间
    if (new Date(formData.value.endDate) <= new Date(value)) {
      formData.value.endDate = "";
      ElMessage.warning("完成时间必须晚于开始时间，已清空完成时间，请重新选择");
    }
  }
};

// 禁用完成日期（不能早于开始日期）
const disabledEndDate = (time: Date) => {
  if (!formData.value.startDate) return false;
  const startDate = new Date(formData.value.startDate);
  return time.getTime() < startDate.getTime() - 24 * 60 * 60 * 1000; // 允许同一天
};

// 禁用完成时间的小时
const disabledEndHours = () => {
  if (!formData.value.startDate || !formData.value.endDate) return [];

  const startDate = new Date(formData.value.startDate);
  const endDate = new Date(formData.value.endDate);

  // 如果是同一天，禁用早于开始时间的小时
  if (startDate.toDateString() === endDate.toDateString()) {
    const disabledHours = [];
    for (let i = 0; i < startDate.getHours(); i++) {
      disabledHours.push(i);
    }
    return disabledHours;
  }
  return [];
};

// 禁用完成时间的分钟
const disabledEndMinutes = (hour: number) => {
  if (!formData.value.startDate || !formData.value.endDate) return [];

  const startDate = new Date(formData.value.startDate);
  const endDate = new Date(formData.value.endDate);

  // 如果是同一天且同一小时，禁用早于开始时间的分钟
  if (startDate.toDateString() === endDate.toDateString() && hour === startDate.getHours()) {
    const disabledMinutes = [];
    for (let i = 0; i < startDate.getMinutes(); i++) {
      disabledMinutes.push(i);
    }
    return disabledMinutes;
  }
  return [];
};

// 禁用完成时间的秒
const disabledEndSeconds = (hour: number, minute: number) => {
  if (!formData.value.startDate || !formData.value.endDate) return [];

  const startDate = new Date(formData.value.startDate);
  const endDate = new Date(formData.value.endDate);

  // 如果是同一天、同一小时、同一分钟，禁用早于或等于开始时间的秒
  if (startDate.toDateString() === endDate.toDateString() &&
      hour === startDate.getHours() &&
      minute === startDate.getMinutes()) {
    const disabledSeconds = [];
    for (let i = 0; i <= startDate.getSeconds(); i++) {
      disabledSeconds.push(i);
    }
    return disabledSeconds;
  }
  return [];
};

// WebSocket 连接管理
let socket: WebSocket | null = null;
const connectedPlans = new Set<string>(); // 记录已连接的计划ID，改为字符串类型

// 为特定计划连接 WebSocket
const connectWebSocketForPlan = (planId: number | string) => {
  const planIdStr = String(planId); // 统一转换为字符串

  // 检查是否已经连接
  if (connectedPlans.has(planIdStr)) {
    console.log(`计划 ${planIdStr} 的 WebSocket 已连接，跳过重复连接`);
    return;
  }

  try {
    console.log(`🔗 尝试为计划 ${planIdStr} 建立 WebSocket 连接...`);
    // 通过网关连接WebSocket，使用网关工具函数
    const wsUrl = buildWebSocketUrl('/reminder', { planId: planIdStr });
    console.log(`WebSocket URL (通过网关): ${wsUrl}`);

    const planSocket = new WebSocket(wsUrl);

    planSocket.onopen = () => {
      console.log(`✅ 计划 ${planIdStr} WebSocket 连接成功`);
      connectedPlans.add(planIdStr);
    };

    planSocket.onmessage = (event) => {
      console.log(`📩 收到计划 ${planIdStr} 的 WebSocket 消息:`, event.data);

      // 使用通知组件显示提醒消息
      ElNotification({
        title: '🔔 生产计划提醒',
        message: event.data,
        type: 'warning',
        duration: 15000,
        showClose: true,
        position: 'top-right'
      });

      // 同时在控制台输出详细信息
      console.log(`🚨 生产计划提醒 - 计划ID: ${planIdStr}, 消息: ${event.data}`);
    };

    planSocket.onclose = () => {
      console.log(`❌ 计划 ${planIdStr} WebSocket 连接关闭`);
      connectedPlans.delete(planIdStr);
    };

    planSocket.onerror = (error) => {
      console.error(`⚠️ 计划 ${planIdStr} WebSocket 出错：`, error);
      connectedPlans.delete(planIdStr);
    };
  } catch (error) {
    console.error(`❌ 计划 ${planIdStr} WebSocket 连接失败:`, error);
  }
};

// 为所有进行中的计划建立 WebSocket 连接
const connectWebSocketForActivePlans = () => {
  console.log('🔍 检查生产计划并建立WebSocket连接...');
  console.log('📊 当前表格数据:', tableData.value);

  if (!tableData.value || tableData.value.length === 0) {
    console.log('📝 当前没有生产计划数据，无需建立 WebSocket 连接');
    return;
  }

  // 遍历当前的生产计划列表，为所有计划建立连接（确保不遗漏任何提醒）
  tableData.value.forEach((plan: any) => {
    const planIdStr = String(plan.id); // 确保ID为字符串类型
    console.log(`检查计划: ${planIdStr} - ${plan.planCode}, 状态: "${plan.status}" (类型: ${typeof plan.status})`);
    console.log(`📊 计划ID类型: ${typeof plan.id}, 原始值: ${plan.id}, 字符串值: ${planIdStr}`);

    // 为所有计划建立WebSocket连接，确保能接收到所有提醒
    console.log(`🔗 为计划 ${planIdStr} - ${plan.planCode} 建立WebSocket连接`);
    connectWebSocketForPlan(planIdStr);
  });

  console.log(`📡 已尝试为 ${tableData.value.length} 个计划建立 WebSocket 连接`);
  console.log(`� 当前已连接的计划数量: ${connectedPlans.size}`);
};

// 关闭所有 WebSocket 连接
const closeAllWebSockets = () => {
  console.log('🔌 关闭所有 WebSocket 连接...');
  connectedPlans.clear();
  if (socket) {
    socket.close();
    socket = null;
  }
};

// 测试WebSocket连接的方法
const testWebSocketConnection = () => {
  console.log('🧪 开始测试WebSocket连接...');

  // 显示当前表格数据
  console.log('📊 当前表格数据:', tableData.value);

  // 显示所有计划ID
  const planIds = tableData.value.map(plan => plan.id);
  console.log('📋 当前页面的所有计划ID:', planIds);

  // 检查特定的计划ID是否存在
  const targetPlanId = '1944407812176244737';
  const foundPlan = tableData.value.find(plan => plan.id == targetPlanId);
  console.log(`🔍 查找计划ID ${targetPlanId}:`, foundPlan ? '找到了' : '未找到');
  if (foundPlan) {
    console.log('📝 找到的计划详情:', foundPlan);
  }

  // 重新检查并连接
  connectWebSocketForActivePlans();

  // 强制为特定计划建立连接
  if (foundPlan) {
    console.log(`🔗 强制为目标计划建立连接: ${targetPlanId}`);
    connectWebSocketForPlan(foundPlan.id);
  }

  // 强制测试：为前3个计划建立WebSocket连接（不管状态）
  console.log('🔧 强制测试模式：为前3个计划建立WebSocket连接...');
  tableData.value.slice(0, 3).forEach((plan: any) => {
    console.log(`🔗 强制测试计划: ${plan.id} - ${plan.planCode}`);
    connectWebSocketForPlan(plan.id);
  });

  ElMessage.success('WebSocket测试已启动，请查看控制台输出');
};

// 在控制台暴露测试函数
if (typeof window !== 'undefined') {
  (window as any).testWebSocketConnection = testWebSocketConnection;
  console.log('🔧 测试函数已暴露到全局: window.testWebSocketConnection()');
}

// 生命周期
onMounted(async () => {
  // 先加载数据
  await getList();
  getProductOptions();
  getProductionLineOptions();
  loadMaterials();

  // 数据加载完成后，为进行中的计划建立 WebSocket 连接
  setTimeout(() => {
    connectWebSocketForActivePlans();
  }, 1000); // 延迟1秒确保数据已加载
});

// 页面销毁时关闭 WebSocket 连接
onBeforeUnmount(() => {
  closeAllWebSockets();
});
</script>

<style scoped>
.production-plan-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-left .page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  font-size: 32px;
}

.page-subtitle {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-right .el-button {
  padding: 12px 24px;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
}

.search-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.search-form {
  padding: 8px 0;
}

.search-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 表格内容样式 */
.plan-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.plan-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.plan-code {
  font-size: 12px;
  color: #909399;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.production-line {
  font-size: 12px;
  color: #909399;
}

.quantity-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.quantity-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-label {
  font-size: 12px;
  color: #606266;
  min-width: 32px;
}

.date-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #606266;
  font-size: 13px;
}

.date-icon {
  color: #409eff;
}

.empty-date {
  color: #c0c4cc;
  font-style: italic;
}

.operation-buttons {
  display: flex;
  justify-content: center;
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .search-form .el-col {
    margin-bottom: 12px;
  }

  .search-buttons {
    justify-content: center;
  }
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f8f9ff !important;
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 原料配置区域样式 */
.materials-section {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
  max-height: 400px; /* 限制最大高度 */
  overflow-y: auto; /* 超出时显示滚动条 */
}

.materials-header {
  margin-bottom: 12px;
}

/* 原料表格样式优化 */
.materials-section .el-table {
  font-size: 12px; /* 减小字体大小 */
}

.materials-section .el-table th,
.materials-section .el-table td {
  padding: 8px 4px; /* 减小单元格内边距 */
}

.materials-section .el-input-number {
  width: 100% !important;
}

.materials-section .el-input {
  font-size: 12px;
}

.materials-section .el-button--small {
  padding: 4px 8px;
  font-size: 11px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 对话框响应式样式 */
.production-plan-dialog {
  max-width: 1200px;
}

@media (max-width: 1400px) {
  .production-plan-dialog .el-dialog {
    width: 80% !important;
  }
}

@media (max-width: 1200px) {
  .production-plan-dialog .el-dialog {
    width: 90% !important;
  }
}

@media (max-width: 768px) {
  .production-plan-dialog .el-dialog {
    width: 95% !important;
  }

  .materials-section .el-table th,
  .materials-section .el-table td {
    padding: 6px 2px;
  }
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  padding: 2px 0;
}

.operation-buttons .action-btn {
  min-width: 45px;
  max-width: 60px;
  height: 26px;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 13px;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.operation-buttons .action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮特定样式 */
.operation-buttons .edit-btn {
  background: linear-gradient(135deg, #409eff, #337ecc);
  border: none;
}

.operation-buttons .material-btn {
  background: linear-gradient(135deg, #67c23a, #529b2e);
  border: none;
}

.operation-buttons .delete-btn {
  background: linear-gradient(135deg, #f56c6c, #dd6161);
  border: none;
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .operation-buttons {
    gap: 3px;
  }

  .operation-buttons .action-btn {
    min-width: 40px;
    max-width: 55px;
    font-size: 10px;
    padding: 2px 6px;
  }
}

@media (max-width: 1400px) {
  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }

  .operation-buttons .action-btn {
    width: 100%;
    min-width: 40px;
    max-width: none;
    font-size: 10px;
    padding: 2px 4px;
    height: 22px;
  }
}
</style>