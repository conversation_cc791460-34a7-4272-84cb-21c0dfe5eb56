// contract.ts
import { http } from "@/utils/http";

/** 订单类型定义 */
export type Order = {
  id: number;
  number: string;
  orderType: string;
  amount: number;
  orderStatus: number;
  payStatus: number;
  isInvoiced: boolean;
  orderDate: string;
};

/** 订单查询参数 */
export type OrderQueryVO = {
  number?: string;
  orderType?: string;
};

/** 订单分页返回结构 */
export type OrderListResult = {
  success: boolean;
  data: {
    records: Order[];
    total: number;
    size?: number;
    current?: number;
  };
};

/** 通用接口返回 */
export type BaseResult = {
  success: boolean;
  message?: string;
  data?: any;
};

/**
 * 获取订单分页列表
 * @param params 查询参数
 */
export const fetchOrderList = (params: {
  now?: number;
  size?: number;
  query?: OrderQueryVO;
}) => {
  return http.request<OrderListResult>("post", "/api/orders/list", {
    params: {
      // 将分页参数作为查询参数
      now: params.now || 1,
      size: params.size || 10
    },
    data: params.query || {} // 查询条件放在请求体
  });
};

/**
 * 获取订单详情
 * @param id 订单ID
 */
export const getOrderDetail = (id: number) => {
  return http.request<BaseResult>("get", `/api/orders/${id}`);
};

/**
 * 添加订单
 * @param data 订单数据
 */
export const createOrder = (data: Omit<Order, "id">) => {
  return http.request<BaseResult>("post", "/api/orders/add", { data });
};

/**
 * 更新订单
 * @param data 订单数据
 */
export const updateOrder = (data: Partial<Order>) => {
  return http.request<BaseResult>("put", "/api/orders", { data });
};

/**
 * 删除订单
 * @param id 订单ID
 */
export const deleteOrderById = (id: number) => {
  return http.request<BaseResult>("delete", `/api/orders/${id}`);
};
