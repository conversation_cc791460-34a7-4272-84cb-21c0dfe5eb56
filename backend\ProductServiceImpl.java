package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Product;
import com.geek.factory.mapper.ProductMapper;
import com.geek.factory.service.ProductService;
import com.geek.factory.vo.query.ProductQueryVO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @Description 产品服务实现类
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public IPage<Product> selectProductPage(ProductQueryVO queryVO) {
        Page<Product> page = new Page<>(queryVO.getCurrent(), queryVO.getSize());
        
        // 使用自定义SQL进行复杂查询
        if (hasComplexQuery(queryVO)) {
            return baseMapper.selectProductPage(page, queryVO);
        }
        
        // 使用QueryWrapper进行简单查询
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        
        // 产品名称模糊查询
        if (StringUtils.hasText(queryVO.getName())) {
            queryWrapper.like("name", queryVO.getName());
        }
        
        // 产品型号模糊查询
        if (StringUtils.hasText(queryVO.getModel())) {
            queryWrapper.like("model", queryVO.getModel());
        }
        
        // 产品分类精确查询
        if (StringUtils.hasText(queryVO.getCategory())) {
            queryWrapper.eq("category", queryVO.getCategory());
        }
        
        // 价格区间查询
        if (queryVO.getMinPrice() != null) {
            queryWrapper.ge("price", queryVO.getMinPrice());
        }
        if (queryVO.getMaxPrice() != null) {
            queryWrapper.le("price", queryVO.getMaxPrice());
        }
        
        // 按ID降序排列
        queryWrapper.orderByDesc("id");
        
        return this.page(page, queryWrapper);
    }

    @Override
    public boolean addProduct(Product product) {
        return this.save(product);
    }

    @Override
    public boolean updateProduct(Product product) {
        return this.updateById(product);
    }

    @Override
    public boolean deleteProduct(Integer id) {
        return this.removeById(id);
    }

    @Override
    public boolean deleteProductBatch(List<Integer> ids) {
        return this.removeByIds(ids);
    }

    @Override
    public List<Product> getProductsByCategory(String category) {
        return baseMapper.selectByCategory(category);
    }

    @Override
    public List<Product> getProductsByPriceRange(Double minPrice, Double maxPrice) {
        return baseMapper.selectByPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<Product> getProductCategoryStatistics() {
        return baseMapper.selectCategoryStatistics();
    }

    /**
     * 判断是否有复杂查询条件
     */
    private boolean hasComplexQuery(ProductQueryVO queryVO) {
        return (queryVO.getMinPrice() != null && queryVO.getMaxPrice() != null) ||
               (StringUtils.hasText(queryVO.getName()) && StringUtils.hasText(queryVO.getCategory()));
    }
}
