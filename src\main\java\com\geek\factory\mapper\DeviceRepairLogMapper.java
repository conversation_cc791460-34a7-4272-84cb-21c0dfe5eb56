package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geek.factory.dto.DeviceRepairLogDTO;
import com.geek.factory.entity.DeviceRepairLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Description 设备维修记录日志Mapper接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Mapper
public interface DeviceRepairLogMapper extends BaseMapper<DeviceRepairLog> {

    /**
     * 获取所有维修记录日志（包含关联的产线名称）
     */
    @Select("SELECT drl.*, pl.name AS productLineName " +
            "FROM device_repair_log drl " +
            "LEFT JOIN production_line pl ON drl.product_line_id = pl.id " +
            "ORDER BY drl.create_time DESC")
    List<DeviceRepairLogDTO> getAllWithLineName();
}
