import { http } from "@/utils/http";

// 统一结果集类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 生产计划创建DTO（根据您的后端实例）
export interface CreateProductionPlanDTO {
  productId: number;
  productName: string;
  code: string;
  productLineId: number;
  productLineName: string;
  quantity: number;
  planStartTime: string;
  completeTime: string;  // 改为completeTime
  status?: number;
  material: MaterialUsageForPlan[];
}

// 生产计划中的原料使用DTO
export interface MaterialUsageForPlan {
  materialId: number;
  requiredQuantity: number;
  unit: string;
  remark?: string; // 添加可选的备注字段
}

// 更新生产计划DTO（与后端UpdateProductionPlanDTO保持一致）
export interface UpdateProductionPlanDTO {
  id: string;  // 计划ID - 改为字符串避免精度问题
  productId: number;
  productLineId: number;
  quantity: number;
  planStartTime: string;
  completeTime: string;
  material: MaterialUsageForPlan[];
}

// 生产计划原料消耗详情DTO（与后端保持一致）
export interface ProductionPlanMaterialDTO {
  materialId: number;
  materialName: string;
  unit: string;
  requiredQuantity: number;
  remark?: string;
}

// 生产计划实体类型（与后端DTO保持一致）
export interface ProductionPlan {
  id?: string;  // 改为字符串避免精度问题
  planCode?: string;
  productId: number;
  productName?: string;
  productLineId?: number;
  productLineName?: string;
  quantity?: number;
  planStartTime?: string;
  completeTime?: string;  // 改为completeTime
  status?: string;
  createTime?: string;
  isInStock?: number;
}

// 生产明细类型
export interface ProductionDetail {
  id?: string;  // 改为字符串避免精度问题
  planId: string;  // 改为字符串避免精度问题
  processName: string;
  operator: string;
  quantityCompleted: number;
  quantityDefective: number;
  startTime: string;
  endTime?: string;
  status: string;
  notes?: string;
}

// 生产明细列表结果
export interface ProductionDetailListResult {
  records: ProductionDetail[];
  total: number;
}

// --- 通用类型 ---
// 如果其他模块也用得到，可以考虑移到 types/api.d.ts
export interface ListResult<T> {
  records: T[];
  total: number;
}

// --- 生产计划API ---

// 分页查询参数类型
export interface ProductionPlanQueryParams {
  current?: number;
  size?: number;
  productId?: number;
  productLineId?: number;
  status?: number;
  planCode?: string;
}

// 分页结果类型
export interface PageResult<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

/**
 * 分页查询生产计划列表
 */
export const getProductionPlanList = (params?: ProductionPlanQueryParams) => {
  return http.request<Result<PageResult<ProductionPlan>>>(
    "post",
    "/api/plan/list",
    { data: params }
  );
};

/**
 * 获取所有生产计划列表
 */
export const getAllProductionPlans = () => {
  return http.request<Result<ProductionPlan[]>>(
    "get",
    "/api/plan/all"
  );
};

export const getProductionPlanById = (id: string) => {
  return http.request<Result<ProductionPlan>>(
    "get",
    `/api/plan/detail/${id}`
  );
};

export const createProductionPlan = (data: CreateProductionPlanDTO) => {
  return http.request<Result>("post", "/api/plan/create", { data });
};

export const updateProductionPlan = (data: UpdateProductionPlanDTO) => {
  return http.request<Result>("put", "/api/plan/update", { data });
};

export const deleteProductionPlan = (id: string) => {
  return http.request("delete", `/api/plan/${id}`);
};

/**
 * 获取下一个可用的计划编号
 */
export const getNextPlanCode = () => {
  return http.request<Result<string>>(
    "get",
    "/api/plan/next-code"
  );
};

/**
 * 修复现有生产计划的编号
 */
export const fixPlanCodes = () => {
  return http.request<Result<number>>(
    "post",
    "/api/plan/fix-plan-codes"
  );
};

/**
 * 根据生产计划ID查询原料消耗详情
 */
/**
 * 根据生产计划ID查询原料消耗详情
 */
export const getPlanMaterials = (planId: string) => {
  return http.request<Result<ProductionPlanMaterialDTO[]>>(
    "get",
    `/api/plan/materials/${planId}`
  );
};

/**
 * 根据生产计划编码查询原料消耗详情
 */
export const getPlanMaterialsByCode = (planCode: string) => {
  return http.request<Result<ProductionPlanMaterialDTO[]>>(
    "get",
    `/api/plan/code/${planCode}`
  );
};

// --- 生产明细API ---

/**
 * 获取生产明细分页列表
 */
export const getProductionDetailList = (params: {
  now?: number;
  size?: number;
  query?: any;
}) => {
  return http.request<Result<ProductionDetailListResult>>(
    "post",
    "/api/production/details/list",
    {
      params: {
        now: params.now || 1,
        size: params.size || 10
      },
      data: params.query || {}
    }
  );
};

/**
 * 根据ID获取生产明细
 */
export const getProductionDetailById = (id: number) => {
  return http.request<Result<ProductionDetail>>(
    "get",
    `/api/production/details/${id}`
  );
};

/**
 * 创建生产明细
 */
export const createProductionDetail = (data: ProductionDetail) => {
  return http.request<Result<any>>(
    "post",
    "/api/production/details",
    { data }
  );
};

/**
 * 更新生产明细
 */
export const updateProductionDetail = (data: ProductionDetail) => {
  return http.request<Result<any>>(
    "put",
    "/api/production/details",
    { data }
  );
};

/**
 * 删除生产明细
 */
export const deleteProductionDetail = (id: number) => {
  return http.request<Result<any>>(
    "delete",
    `/api/production/details/${id}`
  );
};

// --- 生产排班API ---

// 修改生产排班DTO类型定义
export interface ProductionScheduleDTO {
  id?: number;
  scheduleCode?: string; // 排班编号
  productionPlanId?: number;
  productLineId?: number;
  shiftName?: string;
  startTime?: string; // LocalDate格式：yyyy-MM-dd
  endTime?: string;   // LocalDate格式：yyyy-MM-dd
  status?: string;
  // 补充字段
  lineName?: string;        // 来自 product_line 表
  productionPlanCode?: string;     // 来自 production_plan 表
  // 分页参数
  current?: number;
  size?: number;
}

// 修改生产排班实体类型
export interface ProductionSchedule {
  id?: number;
  scheduleCode?: string; // 排班编号，系统自动生成
  productionPlanId: number;
  productLineId: number;
  shiftName: string;
  startTime: string; // 只包含日期部分 YYYY-MM-DD
  endTime: string;   // 只包含日期部分 YYYY-MM-DD
  status: string;
  // 后端自动填充的字段
  productionPlanCode?: string;
  lineName?: string;
}

/**
 * 获取排班管理列表
 */
export const getProductionScheduleList = (params: ProductionScheduleDTO) => {
  return http.request<Result<ProductionScheduleDTO[]>>(
    "post",
    "/api/schedule/list",
    { data: params }
  );
};

/**
 * 根据ID获取排班详情
 */
export const getProductionScheduleById = (id: number) => {
  return http.request<Result<ProductionSchedule>>("get", `/api/schedule/${id}`);
};

/**
 * 创建排班
 */
export const createProductionSchedule = (data: ProductionSchedule) => {
  return http.request<Result<any>>("post", "/api/schedule/add", { data });
};

/**
 * 更新排班
 */
export const updateProductionSchedule = (data: ProductionSchedule) => {
  return http.request<Result<any>>("put", "/api/schedule/update", { data });
};

/**
 * 删除排班
 */
export const deleteProductionSchedule = (id: number) => {
  return http.request<Result<any>>("delete", `/api/schedule/delete/${id}`);
};

/**
 * 根据班次查询排班
 */
export const getProductionScheduleByShift = (shift: string) => {
  return http.request<Result<ProductionScheduleDTO[]>>("get", `/api/schedule/shift/${shift}`);
};

/**
 * 获取产线列表
 */
export const getProductionLineList = () => {
  return http.request<Result<any[]>>("get", "/api/production/line/all");
};
