package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.geek.factory.constant.MessageConstant;
import com.geek.factory.entity.Material;
import com.geek.factory.result.Result;
import com.geek.factory.service.MaterialService;
import com.geek.factory.vo.MaterialQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 原料管理控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/material/management")
@Api(tags = "原料管理")
@CrossOrigin
@Validated
public class MaterialController {

    @Autowired
    private MaterialService materialService;

    /**
     * 分页查询原料列表
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询原料列表", notes = "支持按名称、类型、单位、价格区间查询")
    public Result getMaterialPage(
            @ApiParam(value = "查询条件", required = true) @RequestBody MaterialQueryVO queryVO) {
        log.info("分页查询原料列表，查询条件：{}", queryVO);
        try {
            IPage<Material> page = materialService.getMaterialPage(queryVO);
            log.info("分页查询原料列表成功，共查询到{}条记录", page.getTotal());
            return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, page);
        } catch (Exception e) {
            log.error("分页查询原料列表失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }

    /**
     * 根据ID查询原料详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询原料详情", notes = "根据原料ID查询详细信息")
    public Result<Material> getMaterialById(
            @ApiParam(value = "原料ID", required = true, example = "1") @PathVariable Integer id) {
        log.info("根据ID查询原料详情，ID：{}", id);
        try {
            if (id == null || id <= 0) {
                return Result.error(MessageConstant.PARAMETER_ERROR);
            }

            Material material = materialService.getMaterialById(id);
            if (material != null) {
                log.info("查询原料详情成功，原料名称：{}", material.getName());
                return Result.success(material);
            } else {
                log.warn("原料不存在，ID：{}", id);
                return Result.error(MessageConstant.MATERIAL_NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("根据ID查询原料详情失败，ID：{}", id, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 新增原料
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增原料", notes = "新增原料信息，所有必填字段不能为空")
    public Result addMaterial(
            @ApiParam(value = "原料信息", required = true) @RequestBody Material material) {
        log.info("新增原料，原料信息：{}", material);
        try {
            boolean success = materialService.addMaterial(material);
            if (success) {
                log.info("新增原料成功，原料名称：{}", material.getName());
                return new Result(true, MessageConstant.ADD_MATERIAL_SUCCESS, null);
            } else {
                log.warn("新增原料失败，原料信息：{}", material);
                return new Result(false, MessageConstant.ADD_MATERIAL_FAILED, null);
            }
        } catch (Exception e) {
            log.error("新增原料失败，原料信息：{}", material, e);
            return new Result(false, MessageConstant.ADD_MATERIAL_FAILED, null);
        }
    }

    /**
     * 修改原料
     */
    @PutMapping
    @ApiOperation(value = "修改原料", notes = "修改原料信息，原料ID不能为空")
    public Result<Void> updateMaterial(
            @ApiParam(value = "原料信息", required = true) @RequestBody Material material) {
        log.info("修改原料，原料信息：{}", material);
        try {
            boolean success = materialService.updateMaterial(material);
            if (success) {
                log.info("修改原料成功，原料ID：{}", material.getId());
                return Result.success();
            } else {
                log.warn("修改原料失败，原料信息：{}", material);
                return Result.error(MessageConstant.MATERIAL_UPDATE_FAILED);
            }
        } catch (Exception e) {
            log.error("修改原料失败，原料信息：{}", material, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 删除原料
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除原料", notes = "根据原料ID删除原料")
    public Result<Void> deleteMaterial(
            @ApiParam(value = "原料ID", required = true, example = "1") @PathVariable Integer id) {
        log.info("删除原料，ID：{}", id);
        try {
            if (id == null || id <= 0) {
                return Result.error(MessageConstant.PARAMETER_ERROR);
            }

            boolean success = materialService.deleteMaterial(id);
            if (success) {
                log.info("删除原料成功，ID：{}", id);
                return Result.success();
            } else {
                log.warn("删除原料失败，ID：{}", id);
                return Result.error(MessageConstant.MATERIAL_DELETE_FAILED);
            }
        } catch (Exception e) {
            log.error("删除原料失败，ID：{}", id, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 批量删除原料
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除原料", notes = "根据原料ID列表批量删除原料")
    public Result<Void> deleteMaterialBatch(
            @ApiParam(value = "原料ID列表", required = true) @RequestBody List<Integer> ids) {
        log.info("批量删除原料，IDs：{}", ids);
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.error(MessageConstant.PARAMETER_ERROR);
            }

            boolean success = materialService.deleteMaterialBatch(ids);
            if (success) {
                log.info("批量删除原料成功，删除数量：{}", ids.size());
                return Result.success();
            } else {
                log.warn("批量删除原料失败，IDs：{}", ids);
                return Result.error(MessageConstant.MATERIAL_DELETE_FAILED);
            }
        } catch (Exception e) {
            log.error("批量删除原料失败，IDs：{}", ids, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 获取所有原料列表（不分页）
     */
    @GetMapping("/all")
    @ApiOperation(value = "获取所有原料列表", notes = "获取所有原料列表，不分页")
    public Result<List<Material>> getAllMaterials() {
        log.info("获取所有原料列表");
        try {
            List<Material> materials = materialService.getAllMaterials();
            log.info("获取所有原料列表成功，共{}条记录", materials.size());
            return Result.success(materials);
        } catch (Exception e) {
            log.error("获取所有原料列表失败", e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 根据类型查询原料列表
     */
    @GetMapping("/type/{type}")
    @ApiOperation(value = "根据类型查询原料列表", notes = "根据原料类型查询原料列表")
    public Result<List<Material>> getMaterialsByType(
            @ApiParam(value = "原料类型", required = true, example = "半导体材料") @PathVariable String type) {
        log.info("根据类型查询原料列表，类型：{}", type);
        try {
            List<Material> materials = materialService.getMaterialsByType(type);
            log.info("根据类型查询原料列表成功，类型：{}，共{}条记录", type, materials.size());
            return Result.success(materials);
        } catch (Exception e) {
            log.error("根据类型查询原料列表失败，类型：{}", type, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 根据价格区间查询原料
     */
    @GetMapping("/price")
    @ApiOperation(value = "根据价格区间查询原料", notes = "根据价格区间查询原料列表")
    public Result<List<Material>> getMaterialsByPriceRange(
            @ApiParam(value = "最小价格", required = true, example = "50.00") @RequestParam BigDecimal minPrice,
            @ApiParam(value = "最大价格", required = true, example = "500.00") @RequestParam BigDecimal maxPrice) {
        log.info("根据价格区间查询原料，价格区间：{} - {}", minPrice, maxPrice);
        try {
            List<Material> materials = materialService.getMaterialsByPriceRange(minPrice, maxPrice);
            log.info("根据价格区间查询原料成功，价格区间：{} - {}，共{}条记录", minPrice, maxPrice, materials.size());
            return Result.success(materials);
        } catch (Exception e) {
            log.error("根据价格区间查询原料失败，价格区间：{} - {}", minPrice, maxPrice, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 根据单位查询原料列表
     */
    @GetMapping("/unit/{unit}")
    @ApiOperation(value = "根据单位查询原料列表", notes = "根据计量单位查询原料列表")
    public Result<List<Material>> getMaterialsByUnit(
            @ApiParam(value = "计量单位", required = true, example = "片") @PathVariable String unit) {
        log.info("根据单位查询原料列表，单位：{}", unit);
        try {
            List<Material> materials = materialService.getMaterialsByUnit(unit);
            log.info("根据单位查询原料列表成功，单位：{}，共{}条记录", unit, materials.size());
            return Result.success(materials);
        } catch (Exception e) {
            log.error("根据单位查询原料列表失败，单位：{}", unit, e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }

    /**
     * 获取原料类型统计
     */
    @GetMapping("/statistics/type")
    @ApiOperation(value = "获取原料类型统计", notes = "获取各类型下的原料数量统计")
    public Result<List<Map<String, Object>>> getMaterialTypeStatistics() {
        log.info("获取原料类型统计");
        try {
            List<Map<String, Object>> statistics = materialService.getMaterialTypeStatistics();
            log.info("获取原料类型统计成功，共{}个类型", statistics.size());
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取原料类型统计失败", e);
            return Result.error(MessageConstant.SYSTEM_ERROR);
        }
    }
}
