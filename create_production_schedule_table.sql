-- 创建生产排班表
CREATE TABLE IF NOT EXISTS `production_schedule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `production_plan_id` bigint DEFAULT NULL COMMENT '生产计划ID',
  `production_line_id` bigint DEFAULT NULL COMMENT '产线ID',
  `shift_name` varchar(50) NOT NULL COMMENT '班次名称',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `status` varchar(20) DEFAULT '未开始' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_production_plan_id` (`production_plan_id`),
  KEY `idx_production_line_id` (`production_line_id`),
  KEY `idx_shift_name` (`shift_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产排班表';

-- 插入测试数据
INSERT INTO `production_schedule` (`production_plan_id`, `production_line_id`, `shift_name`, `start_time`, `end_time`, `status`) VALUES
(1, 1, '早班', '2025-07-07 08:00:00', '2025-07-07 16:00:00', '未开始'),
(2, 2, '中班', '2025-07-07 16:00:00', '2025-07-08 00:00:00', '未开始'),
(3, 3, '晚班', '2025-07-08 00:00:00', '2025-07-08 08:00:00', '未开始');

SELECT '✅ 生产排班表创建完成！' as status;
