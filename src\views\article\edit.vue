<template>
  <div class="createPost-container">
    <!--表单-->
    <el-form
      ref="postFormRef"
      :model="postForm"
      :rules="rules"
      class="form-container"
    >
      <el-row>
        <el-col :span="18">
          <el-form-item
            label-width="60px"
            style="margin: 20px 0"
            label="标题"
            class="postInfo-container-item"
            prop="title"
          >
            <el-input v-model="postForm.title" name="title" required />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <el-form-item
            label-width="60px"
            label="作者"
            class="postInfo-container-item"
            prop="author"
          >
            <el-input v-model="postForm.author" name="author" required />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <div :style="{ textAlign: 'center' }">
            <el-button
              v-loading="loading"
              style="margin-left: 10px"
              type="success"
              @click="submitForm"
            >
              保存
            </el-button>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item
            label-width="60px"
            style="margin-bottom: 30px"
            label="内容"
            class="postInfo-container-item"
            prop="content"
          >
            <!-- 子组件 富文本编辑器 -->
            <WangEditor
              v-model="postForm.content"
              height="400px"
              required
              @func="receiveFromChild"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item
            label-width="60px"
            style="margin-bottom: 30px"
            class="postInfo-container-item"
          >
            <div style="width: 95%; margin-top: 10px">
              <textarea
                v-model="postForm.content"
                readonly
                style="width: 100%; height: 200px; outline: none"
              />
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { update, getDetail } from "@/api/article";
import { ElNotification } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { format } from "date-fns";

const route = useRoute();
const id = route.params.id as string;
console.log("id", id);

const router = useRouter();
const postFormRef = ref(null);
const loading = ref(false);

// 表单数据
const postForm = reactive({
  id: "", // id
  title: "", // 文章题目
  content: "初始化内容", // 文章内容
  publishDate: "", // 发布时间
  author: "", // 作者
  abstracts: "", // 摘要
  pic: "" // 文章图片
});

// 表单校验规则
const validateRequire = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(rule.field + "为必传项"));
  } else {
    callback();
  }
};

const rules = reactive({
  title: [{ validator: validateRequire }],
  content: [{ validator: validateRequire }],
  author: [{ validator: validateRequire }]
});

// 提交表单
const submitForm = () => {
  postForm.publishDate = formatDate(new Date().getTime());
  loading.value = true;
  postFormRef.value.validate(async valid => {
    if (valid) {
      await update(postForm);
      ElNotification.success({
        title: "成功",
        message: "更新文章成功",
        type: "success",
        duration: 2000
      });
      loading.value = false;
      router.push("/article/list");
    }
  });
};

// 处理时间
const formatDate = timestamp => {
  return format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss");
};

// 接收子组件传递的数据 文件上传后的返回的地址
const receiveFromChild = url => {
  postForm.pic = url;
};

// 页面加载时获取文章详情
onMounted(async () => {
  postForm.id = id;
  const response = await getDetail(id);
  if (response.success) {
    const article = response.data;
    postForm.title = article.title;
    postForm.content = article.content;
    postForm.author = article.author;
    postForm.pic = article.pic || "";
  }
});
</script>

<style lang="scss" scoped>
.editorWrapper {
  margin-top: 20px;

  :deep(.w-e-text-container) {
    text-align: left;
  }

  .info {
    text-align: left;
    padding-left: 20px;
    margin-bottom: 20px;
    font-size: 12px;
  }

  .section {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }

  .el-row {
    margin-bottom: 20px;
  }
}
</style>
