<template>
  <div class="production-schedule-add">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>新增排班</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="schedule-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排班编号" prop="schedule_code">
              <el-input
                v-model="formData.schedule_code"
                placeholder="提交时系统自动生成有序编号"
                readonly
                style="background-color: #f5f7fa;"
              >
                <template #append>
                  <el-tag type="info" size="small">自动生成</el-tag>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="员工姓名" prop="employee_name">
              <el-input
                v-model="formData.employee_name"
                placeholder="请输入员工姓名"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="员工工号" prop="employee_id">
              <el-input
                v-model="formData.employee_id"
                placeholder="请输入员工工号"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="部门" prop="department">
              <el-input
                v-model="formData.department"
                placeholder="请输入部门"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位" prop="position">
              <el-input
                v-model="formData.position"
                placeholder="请输入岗位"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="工作日期" prop="work_date">
              <el-date-picker
                v-model="formData.work_date"
                type="date"
                placeholder="选择工作日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班次" prop="shift">
              <el-select
                v-model="formData.shift"
                placeholder="请选择班次"
                style="width: 100%"
              >
                <el-option label="早班" value="早班" />
                <el-option label="中班" value="中班" />
                <el-option label="晚班" value="晚班" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="开始时间" prop="start_time">
              <el-time-picker
                v-model="formData.start_time"
                placeholder="选择开始时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="结束时间" prop="end_time">
              <el-time-picker
                v-model="formData.end_time"
                placeholder="选择结束时间"
                format="HH:mm"
                value-format="HH:mm"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="工作时长" prop="work_hours">
              <el-input-number
                v-model="formData.work_hours"
                :min="0"
                :max="24"
                :precision="1"
                placeholder="请输入工作时长"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="加班时长" prop="overtime_hours">
              <el-input-number
                v-model="formData.overtime_hours"
                :min="0"
                :max="12"
                :precision="1"
                placeholder="请输入加班时长"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="未开始" :value="0" />
                <el-option label="进行中" :value="1" />
                <el-option label="已完成" :value="2" />
                <el-option label="请假" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="formData.remarks"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            保存
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { createProductionSchedule, type ProductionSchedule } from "@/api/production";

const router = useRouter();
const formRef = ref();
const loading = ref(false);

// 表单数据
const formData = reactive<ProductionSchedule>({
  schedule_code: "", // 后端自动生成有序编号
  employee_name: "",
  employee_id: "",
  department: "",
  position: "",
  work_date: "",
  shift: "",
  start_time: "",
  end_time: "",
  work_hours: 8,
  status: 0,
  overtime_hours: 0,
  remarks: ""
});

// 表单验证规则
const rules = {
  schedule_code: [
    { required: true, message: "请输入排班编号", trigger: "blur" }
  ],
  employee_name: [
    { required: true, message: "请输入员工姓名", trigger: "blur" }
  ],
  employee_id: [
    { required: true, message: "请输入员工工号", trigger: "blur" }
  ],
  department: [
    { required: true, message: "请输入部门", trigger: "blur" }
  ],
  position: [
    { required: true, message: "请输入岗位", trigger: "blur" }
  ],
  work_date: [
    { required: true, message: "请选择工作日期", trigger: "change" }
  ],
  shift: [
    { required: true, message: "请选择班次", trigger: "change" }
  ],
  start_time: [
    { required: true, message: "请选择开始时间", trigger: "change" }
  ],
  end_time: [
    { required: true, message: "请选择结束时间", trigger: "change" }
  ],
  work_hours: [
    { required: true, message: "请输入工作时长", trigger: "blur" },
    { type: "number", min: 0, max: 24, message: "工作时长必须在0-24小时之间", trigger: "blur" }
  ],
  status: [
    { required: true, message: "请选择状态", trigger: "change" }
  ]
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    await createProductionSchedule(formData);
    ElMessage.success("创建成功");
    router.push("/production/schedule/list");
  } catch (error) {
    console.error("创建失败:", error);
    ElMessage.error("创建失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  router.back();
};
</script>

<style scoped>
.production-schedule-add {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-form {
  max-width: 800px;
}
</style> 