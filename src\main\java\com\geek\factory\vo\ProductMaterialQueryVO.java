package com.geek.factory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 产品原料关联查询VO
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@ApiModel(value = "ProductMaterialQueryVO", description = "产品原料关联查询参数")
public class ProductMaterialQueryVO {

    @ApiModelProperty(value = "产品ID", example = "1")
    private Integer productId;

    @ApiModelProperty(value = "原料ID", example = "1")
    private Integer materialId;

    @ApiModelProperty(value = "产品名称", example = "智能手机主板")
    private String productName;

    @ApiModelProperty(value = "原料名称", example = "硅晶圆")
    private String materialName;

    @ApiModelProperty(value = "最小用量", example = "1.000")
    private BigDecimal minQuantity;

    @ApiModelProperty(value = "最大用量", example = "10.000")
    private BigDecimal maxQuantity;

    @ApiModelProperty(value = "单位", example = "片")
    private String unit;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100;
        }
    }
}
