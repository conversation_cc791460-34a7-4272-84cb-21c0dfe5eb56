package com.geek.factory.controller;

import com.geek.factory.dto.ProductionScheduleDTO;
import com.geek.factory.entity.ProductionSchedule;
import com.geek.factory.result.Result;
import com.geek.factory.service.ProductionScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 生产排班控制器
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/schedule")
@RequiredArgsConstructor
@Api(tags = "生产排班管理")
public class ProductionScheduleController {

    private final ProductionScheduleService scheduleService;

    @PostMapping("/list")
    @ApiOperation("获取排班列表")
    public Result list(@RequestBody ProductionScheduleDTO queryDTO) {
        try {
            log.info("查询排班列表，查询条件：{}", queryDTO);

            // 如果有班次名称条件，调用根据班次查询的方法
            if (queryDTO.getShiftName() != null && !queryDTO.getShiftName().trim().isEmpty()) {
                log.info("根据班次查询排班，班次：{}", queryDTO.getShiftName());
                List<ProductionScheduleDTO> dtoList = scheduleService.getByShift(queryDTO.getShiftName().trim());

                log.info("根据班次查询成功，共{}条记录", dtoList.size());
                return new Result(true, "查询成功", dtoList);
            } else {
                // 否则返回所有数据
                List<ProductionScheduleDTO> list = scheduleService.getAllWithLineName();
                log.info("获取排班列表成功，共{}条记录", list.size());
                return new Result(true, "查询成功", list);
            }
        } catch (Exception e) {
            log.error("获取排班列表失败", e);
            return new Result(false, "查询失败", null);
        }
    }


    @GetMapping("/{id}")
    @ApiOperation("根据ID获取排班详情")
    public Result getById(@PathVariable Long id) {
        try {
            ProductionSchedule schedule = scheduleService.getById(id);
            if (schedule != null) {
                return new Result(true, "查询成功", schedule);
            } else {
                return new Result(false, "未找到对应的排班信息", null);
            }
        } catch (Exception e) {
            log.error("获取排班详情失败", e);
            return new Result(false, "查询失败", null);
        }
    }

    @PostMapping("/add")
    @ApiOperation("新增排班")
    public Result add(@RequestBody ProductionSchedule schedule) {
        try {
            log.info("新增排班: {}", schedule);
            scheduleService.add(schedule);
            return new Result(true, "新增成功", null);
        } catch (Exception e) {
            log.error("新增排班失败", e);
            return new Result(false, "新增失败: " + e.getMessage(), null);
        }
    }

    @PutMapping("/update")
    @ApiOperation("更新排班")
    public Result update(@RequestBody ProductionSchedule schedule) {
        try {
            log.info("更新排班: {}", schedule);
            scheduleService.update(schedule);
            return new Result(true, "更新成功", null);
        } catch (Exception e) {
            log.error("更新排班失败", e);
            return new Result(false, "更新失败: " + e.getMessage(), null);
        }
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除排班")
    public Result delete(@PathVariable Long id) {
        try {
            scheduleService.deleteById(id);
            return new Result(true, "删除成功", null);
        } catch (Exception e) {
            log.error("删除排班失败", e);
            return new Result(false, "删除失败", null);
        }
    }

    @GetMapping("/shift/{shift}")
    @ApiOperation("根据班次查询排班")
    public Result getByShift(@PathVariable String shift) {
        try {
            log.info("根据班次查询排班，班次：{}", shift);
            List<ProductionSchedule> schedules = scheduleService.getByShift(shift);
            if (schedules != null && !schedules.isEmpty()) {
                log.info("查询成功，找到{}条排班记录", schedules.size());
                return new Result(true, "查询成功", schedules);
            } else {
                log.info("没有找到该班次的排班记录");
                return new Result(true, "没有找到该班次的排班", null);
            }
        } catch (Exception e) {
            log.error("根据班次查询排班失败，班次：{}", shift, e);
            return new Result(false, "查询失败", null);
        }
    }
}
