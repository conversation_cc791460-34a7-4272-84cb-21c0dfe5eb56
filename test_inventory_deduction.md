# 库存扣减功能测试指南

## 🎯 功能说明
现在当用户创建生产计划时，系统会自动扣减所选原料的库存数量，实现真实的工厂管理逻辑。

## 🔧 实现的功能

### 1. 库存验证端点
- **路径**: `POST /api/material/stock`
- **功能**: 验证原料库存是否足够
- **请求体**: `List<MaterialUsageDTO>`
- **响应**: 成功/失败 + 错误信息

### 2. 库存扣减端点
- **路径**: `POST /api/material/deduct`
- **功能**: 扣减原料库存
- **请求体**: `List<MaterialUsageDTO>`
- **响应**: 成功/失败 + 错误信息

### 3. 生产计划创建时自动扣减
- 在 `ProductionPlanServiceImpl.createPlan()` 中实现
- 使用事务确保数据一致性
- 如果库存不足，整个创建过程回滚

## 📊 数据库更新

### 原料表结构更新
```sql
ALTER TABLE `materials` ADD COLUMN `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '库存数量';
ALTER TABLE `materials` ADD COLUMN `code` varchar(50) DEFAULT NULL COMMENT '原料编号';
```

### 测试数据
已为所有原料添加了合理的库存数量：
- 硅晶圆: 100.00 片
- 电容器: 800.00 个
- 电阻器: 1000.00 个
- 陶瓷电容 10μF 50V: 1000.00 个
- 等等...

## 🧪 测试步骤

### 1. 数据库准备
```bash
# 执行库存更新脚本
mysql -u root -p factory < backend/update_material_stock.sql
```

### 2. 启动后端服务
```bash
cd src/main/java
mvn spring-boot:run
```

### 3. 前端测试
1. 打开生产计划列表页面
2. 点击"新增"按钮
3. 填写生产计划信息
4. 添加原料（例如：陶瓷电容 10μF 50V，数量 20）
5. 提交创建

### 4. 验证结果
1. 检查生产计划是否创建成功
2. 查看原料管理页面，确认库存数量已减少
3. 查看后端日志，确认扣减过程

## 🔍 预期行为

### 成功场景
- 库存充足时，生产计划创建成功
- 原料库存自动减少相应数量
- 前端显示成功消息

### 失败场景
- 库存不足时，显示具体错误信息
- 生产计划不会被创建
- 库存数量保持不变

## 📝 日志示例

### 成功日志
```
INFO  - 开始创建生产计划，计划编号：plan-20241205-01
INFO  - 生产计划保存成功，计划ID：123
INFO  - 开始扣减原料库存，原料数量：2
INFO  - 成功扣减原料库存，原料：陶瓷电容 10μF 50V，扣减数量：20.00，剩余库存：980.00
INFO  - 原料库存扣减完成
INFO  - 生产计划创建完成，计划编号：plan-20241205-01
```

### 失败日志
```
ERROR - 原料库存不足，原料：陶瓷电容 10μF 50V，当前库存：5.00，需要：20.00
ERROR - 库存扣减失败
```

## 🚀 下一步优化建议

1. **库存预警**: 当库存低于最小值时发送警告
2. **库存历史**: 记录库存变动历史
3. **批量操作**: 支持批量库存调整
4. **库存盘点**: 定期库存盘点功能
5. **供应商管理**: 关联供应商信息，支持自动补货

## 🔧 故障排除

### 常见问题
1. **数据库字段不存在**: 确保执行了 `update_material_stock.sql`
2. **库存为0**: 检查数据库中原料的 `quantity` 字段
3. **API路径错误**: 确认前端调用的是 `/api/material/stock`
4. **事务回滚**: 检查后端日志中的错误信息
