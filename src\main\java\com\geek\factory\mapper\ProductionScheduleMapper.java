package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geek.factory.dto.ProductionScheduleDTO;
import com.geek.factory.entity.ProductionSchedule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 生产排班Mapper接口
 */
@Mapper
public interface ProductionScheduleMapper extends BaseMapper<ProductionSchedule> {

    /**
     * 获取所有排班信息（包含关联的产线名称和生产计划编号）
     */
    @Select("SELECT ps.*, pl.name AS productLineName, pp.plan_code AS productionPlanCode " +
            "FROM production_schedule ps " +
            "LEFT JOIN production_line pl ON ps.production_line_id = pl.id " +
            "LEFT JOIN production_plan pp ON ps.production_plan_id = pp.id " +
            "ORDER BY ps.create_time DESC")
    List<ProductionScheduleDTO> getAllWithLineName();

    /**
     * 查询排班列表（含产线名称和计划编码）
     * 目前直接返回所有数据，后续可根据需要添加条件筛选
     */
    @Select("SELECT ps.*, pl.name AS productLineName, pp.plan_code AS productionPlanCode " +
            "FROM production_schedule ps " +
            "LEFT JOIN production_line pl ON ps.production_line_id = pl.id " +
            "LEFT JOIN production_plan pp ON ps.production_plan_id = pp.id " +
            "ORDER BY ps.create_time DESC")
    List<ProductionScheduleDTO> getScheduleWithNames(@Param("dto") ProductionScheduleDTO dto);

    /**
     * 根据班次查询排班信息（包含关联的产线名称和生产计划编号）
     */
    @Select("SELECT ps.*, pl.name AS productLineName, pp.plan_code AS productionPlanCode " +
            "FROM production_schedule ps " +
            "LEFT JOIN production_line pl ON ps.production_line_id = pl.id " +
            "LEFT JOIN production_plan pp ON ps.production_plan_id = pp.id " +
            "WHERE ps.shift_name = #{shift} " +
            "ORDER BY ps.create_time DESC")
    List<ProductionScheduleDTO> getByShift(@Param("shift") String shift);

    /**
     * 获取指定日期前缀的最大序号
     *
     * @param prefix 日期前缀 (如: PLAN-20250707-)
     * @return 最大序号，如果没有记录则返回null
     */
    @Select("SELECT MAX(CAST(SUBSTRING(schedule_code, 14) AS UNSIGNED)) as max_seq " +
            "FROM production_schedule " +
            "WHERE schedule_code LIKE CONCAT(#{prefix}, '%') " +
            "AND LENGTH(schedule_code) = 15 " +
            "AND SUBSTRING(schedule_code, 14) REGEXP '^[0-9]+$'")
    Integer getMaxSequenceForDate(@Param("prefix") String prefix);
}
