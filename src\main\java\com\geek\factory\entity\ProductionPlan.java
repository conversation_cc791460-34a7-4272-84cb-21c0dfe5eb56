package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产计划实体类
 */
@Data
@TableName("production_plan")
@ApiModel(value = "ProductionPlan", description = "生产计划实体")
public class ProductionPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("plan_code")
    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @TableField("product_id")
    @ApiModelProperty(value = "产品ID")
    private Long productId;

    @TableField("product_line_id")
    @ApiModelProperty(value = "产线ID")
    private Long productLineId;

    @TableField("quantity")
    @ApiModelProperty(value = "计划数量")
    private BigDecimal quantity;

    @TableField("plan_start_time")
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    @TableField("complete_time")
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime completeTime;

    @TableField("status")
    @ApiModelProperty(value = "状态")
    private String status;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField("is_in_stock")
    @ApiModelProperty(value = "是否入库 (0-未入库, 1-已入库)")
    private Integer isInStock;

    @TableField("priority")
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @TableField("remarks")
    @ApiModelProperty(value = "备注")
    private String remarks;
}
