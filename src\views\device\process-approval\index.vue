<template>
  <div class="process-approval-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card shadow="never" class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="审批人">
            <el-input
              v-model="searchForm.approver"
              placeholder="请输入审批人"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="审批决定">
            <el-select
              v-model="searchForm.decision"
              placeholder="请选择审批决定"
              clearable
              style="width: 150px"
            >
              <el-option label="通过" value="approved" />
              <el-option label="拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <template #icon>
                <component :is="useRenderIcon(Search)" />
              </template>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <template #icon>
                <component :is="useRenderIcon(Refresh)" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-card shadow="never" class="action-card">
        <el-button type="primary" @click="openAdd">
          <template #icon>
            <component :is="useRenderIcon(Plus)" />
          </template>
          新增审批
        </el-button>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card shadow="never" class="table-card">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="approver" label="审批人" width="120" />
          <el-table-column prop="decision" label="审批决定" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getDecisionTagType(row.decision)"
                size="small"
              >
                {{ getDecisionText(row.decision) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="审批意见" min-width="200" show-overflow-tooltip />
          <el-table-column prop="approvalTime" label="审批时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.approvalTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="info"
                size="small"
                @click="openView(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(View)" />
                </template>
                查看
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="openEdit(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Edit)" />
                </template>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Delete)" />
                </template>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="paginationConfig.current"
            v-model:page-size="paginationConfig.size"
            :total="paginationConfig.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="getList"
            @current-change="getList"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="报修单ID" prop="reportId">
          <el-input-number
            v-model="formData.reportId"
            placeholder="请输入报修单ID"
            style="width: 100%"
            :min="1"
          />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input
            v-model="formData.approver"
            placeholder="请输入审批人"
          />
        </el-form-item>
        <el-form-item label="审批决定" prop="decision">
          <el-select
            v-model="formData.decision"
            placeholder="请选择审批决定"
            style="width: 100%"
          >
            <el-option label="通过" value="approved" />
            <el-option label="拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="审批详情"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="审批ID">
          {{ detailData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="报修单ID">
          {{ detailData.reportId }}
        </el-descriptions-item>
        <el-descriptions-item label="审批人">
          {{ detailData.approver }}
        </el-descriptions-item>
        <el-descriptions-item label="审批决定">
          <el-tag :type="getDecisionTagType(detailData.decision)" size="small">
            {{ getDecisionText(detailData.decision) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批时间">
          {{ formatDateTime(detailData.approvalTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="审批意见" :span="2">
          {{ detailData.comment || '无' }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import Edit from "@iconify-icons/ep/edit";
import Delete from "@iconify-icons/ep/delete";
import View from "@iconify-icons/ep/view";

import {
  getApprovalLogList,
  getApprovalLogById,
  createApprovalLog,
  updateApprovalLog,
  deleteApprovalLog,
  type DeviceRepairApprovalLogDTO,
  type ApprovalQueryParams
} from "@/api/approval";

// 搜索表单
const searchForm = reactive<ApprovalQueryParams>({
  approver: "",
  decision: ""
});

// 分页配置
const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

// 表格数据
const tableData = ref<DeviceRepairApprovalLogDTO[]>([]);
const loading = ref(false);

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref<DeviceRepairApprovalLogDTO>({} as DeviceRepairApprovalLogDTO);

// 表单
const formRef = ref<FormInstance>();
const formData = ref<DeviceRepairApprovalLogDTO>({
  reportId: undefined,
  approver: "",
  decision: "",
  comment: ""
});

// 表单验证规则
const formRules: FormRules = {
  reportId: [{ required: true, message: "请输入报修单ID", trigger: "blur" }],
  approver: [{ required: true, message: "请输入审批人", trigger: "blur" }],
  decision: [{ required: true, message: "请选择审批决定", trigger: "change" }],
  comment: [{ required: true, message: "请输入审批意见", trigger: "blur" }]
};

// 获取审批列表
const getApprovalList = async () => {
  try {
    const result = await getApprovalLogList();
    return result;
  } catch (error) {
    console.error('获取审批列表失败:', error);
    throw error;
  }
};

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const result = await getApprovalList();
    if (result && result.success) {
      tableData.value = result.data || [];
      paginationConfig.total = result.data?.length || 0;
    } else {
      ElMessage.error("获取数据失败");
    }
  } catch (error) {
    console.error('获取列表失败:', error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  paginationConfig.current = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.approver = "";
  searchForm.decision = "";
  paginationConfig.current = 1;
  getList();
};

// 打开新增对话框
const openAdd = () => {
  dialogTitle.value = "新增审批";
  isEdit.value = false;
  formData.value = {
    reportId: undefined,
    approver: "",
    decision: "",
    comment: ""
  };
  dialogVisible.value = true;
};

// 打开编辑对话框
const openEdit = async (row: DeviceRepairApprovalLogDTO) => {
  dialogTitle.value = "编辑审批";
  isEdit.value = true;

  try {
    const res = await getApprovalLogById(row.id!);
    if (res && res.success && res.data) {
      formData.value = { ...res.data };
    } else {
      ElMessage.error("获取详情失败");
      return;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
    return;
  }

  dialogVisible.value = true;
};

// 打开详情对话框
const openView = async (row: DeviceRepairApprovalLogDTO) => {
  try {
    const res = await getApprovalLogById(row.id!);
    if (res && res.success && res.data) {
      detailData.value = { ...res.data };
    } else {
      ElMessage.error("获取详情失败");
      return;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
    return;
  }

  detailDialogVisible.value = true;
};

// 删除
const handleDelete = async (row: DeviceRepairApprovalLogDTO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除审批记录 ${row.id} 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const result = await deleteApprovalLog(row.id!);
    if (result && result.success) {
      ElMessage.success("删除成功");
      getList();
    } else {
      ElMessage.error(result?.msg || "删除失败");
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error("删除失败");
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (isEdit.value) {
      // 新增时设置审批时间为当前时间（ISO格式）
      const submitData = {
        ...formData.value,
        approvalTime: new Date().toISOString().slice(0, 19)
      };

      const result = await updateApprovalLog(submitData);
      if (result && result.success) {
        ElMessage.success("更新成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "更新失败");
      }
    } else {
      // 新增时设置审批时间为当前时间（ISO格式）
      const submitData = {
        ...formData.value,
        approvalTime: new Date().toISOString().slice(0, 19)
      };

      const result = await createApprovalLog(submitData);
      if (result && result.success) {
        ElMessage.success("新增成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "新增失败");
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error("提交失败");
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 获取审批决定标签类型
const getDecisionTagType = (decision: string) => {
  switch (decision?.toLowerCase()) {
    case "approved":
      return "success";
    case "rejected":
      return "danger";
    case "pending":
      return "warning";
    default:
      return "info";
  }
};

// 获取审批决定文本
const getDecisionText = (decision: string) => {
  switch (decision?.toLowerCase()) {
    case "approved":
      return "通过";
    case "rejected":
      return "拒绝";
    case "pending":
      return "待审批";
    default:
      return decision || "未知";
  }
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  try {
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

// 生命周期
onMounted(() => {
  getList();
});
</script>

<style scoped>
.process-approval-container {
  padding: 20px;
}

.search-section,
.action-section,
.table-section {
  margin-bottom: 20px;
}

.search-card,
.action-card,
.table-card {
  border-radius: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 操作按钮样式优化 */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 60px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-width: 1px;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.view-btn {
  color: #409eff;
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.view-btn:hover {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.edit-btn {
  color: #e6a23c;
  border-color: #e6a23c;
  background-color: rgba(230, 162, 60, 0.1);
}

.edit-btn:hover {
  background-color: #e6a23c;
  color: white;
  border-color: #e6a23c;
}

.delete-btn {
  color: #f56c6c;
  border-color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
}

.delete-btn:hover {
  background-color: #f56c6c;
  color: white;
  border-color: #f56c6c;
}

@media (max-width: 768px) {
  .process-approval-container {
    padding: 10px;
  }

  .el-dialog {
    width: 90% !important;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn {
    min-width: 50px;
    font-size: 12px;
  }
}
</style>
