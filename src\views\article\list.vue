<script setup lang="ts">
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

import {
  fetchList,
  deleteById,
  create,
  batchDelete,
  update,
  getDetail
} from "@/api/article";

interface Article {
  id: string;
  title: string;
  publishDate: string;
  author: string;
  content: string;
  pic?: string;
}

const tableData = ref<Article[]>([]);
const paginationConfig = reactive({
  total: 0, // 总记录数
  size: 5, // 每页显示的记录数
  current: 1 // 当前页码
});

// 查询文章列表
const getList = () => {
  const params = {
    size: paginationConfig.size,
    now: paginationConfig.current,
    query: {
      planCode: searchForm.planCode,
      planName: searchForm.planName
    }
  };

  fetchList(params)
    .then(response => {
      // 防御性处理，兼容后端 data 可能为 null
      if (response && response.success && response.data) {
        tableData.value = response.data.records || [];
        paginationConfig.total = response.data.total || 0;
      } else {
        tableData.value = [];
        paginationConfig.total = 0;
      }
    })
    .catch(error => {
      console.error("获取文章列表出错:", error);
      ElMessage.error("获取数据失败");
    });
};
getList();

// 删除数据 删除成功之后重新获取数据
const handleDelete = (index: number, row: Article) => {
  deleteById(row.id).then(() => {
    getList();
  });
};

// 编辑数据
const handleEdit = (id: string) => {
  router.push(`/article/edit/${id}`);
};

// 分页 点击页码重新获取数据
const handleCurrentChange = (current: number) => {
  paginationConfig.current = current;
  getList();
};
</script>

<template>
  <div>
    <el-table :data="tableData" style="width: 100%" class="table-container">
      <el-table-column label="编号" width="80" header-align="center">
        <template #default="scope">
          <span style="margin-left: 10px">{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" width="180" header-align="center">
        <template #default="scope">
          <span style="margin-left: 10px">{{ scope.row.publishDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" width="180" header-align="center">
        <template #default="scope">
          <span style="margin-left: 10px">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="封面" width="180" header-align="center">
        <template #default="scope">
          <span style="margin-left: 10px">
            <img
              v-if="scope.row.pic"
              width="100px"
              height="auto"
              :src="`http://localhost:8080${scope.row.pic}`"
            />
          </span>
        </template>
      </el-table-column>
      <el-table-column label="作者" width="180" header-align="center">
        <template #default="scope">
          <span style="margin-left: 10px">{{ scope.row.author }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="内容"
        width="180"
        :show-overflow-tooltip="true"
        header-align="center"
      >
        <template #default="scope">
          <div
            class="ellipsis"
            style="margin-left: 10px"
            v-html="scope.row.content"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" header-align="center" width="180">
        <template #default="scope">
          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.$index, scope.row)"
          >
            删除
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleEdit(scope.row.id)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="paginationConfig.total"
        :page-size="paginationConfig.size"
        :current-page="paginationConfig.current"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style scoped>
.ellipsis {
  display: -webkit-box;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.table-container {
  display: flex;
  justify-content: center;
}

.pagination-container {
  margin-top: 35px;
  display: flex;
  justify-content: center;
}
</style>
