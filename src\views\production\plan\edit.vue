<template>
  <div class="production-plan-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <el-icon class="title-icon" size="28">
              <Edit v-if="!isViewMode" />
              <View v-else />
            </el-icon>
            <div class="title-text">
              <h1 class="page-title">{{ isViewMode ? '查看生产计划' : '编辑生产计划' }}</h1>
              <p class="page-subtitle">{{ isViewMode ? 'View Production Plan' : 'Edit Production Plan' }}</p>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button
            size="large"
            @click="handleCancel"
            class="cancel-btn"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单卡片 -->
    <div class="form-section">
      <div class="form-card">
        <div class="form-header">
          <div class="form-title">
            <el-icon class="form-icon">
              <Edit v-if="!isViewMode" />
              <View v-else />
            </el-icon>
            <span>{{ isViewMode ? '计划详情' : '编辑信息' }}</span>
          </div>
          <div class="form-subtitle">
            {{ isViewMode ? '查看生产计划的详细信息' : '修改生产计划的详细信息' }}
          </div>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          class="modern-form"
          label-position="top"
          :disabled="isViewMode"
        >
          <!-- 基础信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Document /></el-icon>
              <span>基础信息</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="计划编号" prop="plan_code">
                  <el-input
                    v-model="formData.plan_code"
                    placeholder="系统自动生成或手动输入"
                    clearable
                    prefix-icon="DocumentCopy"
                    size="large"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="计划名称" prop="plan_name">
                  <el-input
                    v-model="formData.plan_name"
                    placeholder="请输入计划名称"
                    clearable
                    prefix-icon="Edit"
                    size="large"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 产品信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Box /></el-icon>
              <span>产品信息</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="product_name">
                  <el-input
                    v-model="formData.product_name"
                    placeholder="请输入产品名称"
                    clearable
                    prefix-icon="Goods"
                    size="large"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="计划数量" prop="plan_quantity">
                  <el-input-number
                    v-model="formData.plan_quantity"
                    :min="1"
                    placeholder="请输入计划数量"
                    style="width: 100%"
                    size="large"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="实际数量" prop="actual_quantity">
                  <el-input-number
                    v-model="formData.actual_quantity"
                    :min="0"
                    placeholder="请输入实际数量"
                    style="width: 100%"
                    size="large"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 时间安排区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Calendar /></el-icon>
              <span>时间安排</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="计划开始时间" prop="planStartTime">
                  <el-date-picker
                    v-model="formData.planStartTime"
                    type="datetime"
                    placeholder="选择计划开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                    size="large"
                    prefix-icon="Calendar"
                    @change="handleStartTimeChange"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="计划结束时间" prop="completeTime">
                  <el-date-picker
                    v-model="formData.completeTime"
                    type="datetime"
                    placeholder="选择计划结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                    size="large"
                    prefix-icon="Calendar"
                    :disabled-date="disabledEndDate"
                    :disabled-hours="disabledEndHours"
                    :disabled-minutes="disabledEndMinutes"
                    :disabled-seconds="disabledEndSeconds"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 配置信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Setting /></el-icon>
              <span>配置信息</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="优先级" prop="priority">
                  <el-select
                    v-model="formData.priority"
                    placeholder="请选择优先级"
                    style="width: 100%"
                    size="large"
                  >
                    <el-option label="低" :value="1">
                      <div class="priority-option">
                        <el-tag type="info" size="small">低</el-tag>
                        <span class="option-desc">普通优先级</span>
                      </div>
                    </el-option>
                    <el-option label="中" :value="2">
                      <div class="priority-option">
                        <el-tag type="warning" size="small">中</el-tag>
                        <span class="option-desc">中等优先级</span>
                      </div>
                    </el-option>
                    <el-option label="高" :value="3">
                      <div class="priority-option">
                        <el-tag type="danger" size="small">高</el-tag>
                        <span class="option-desc">高优先级</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select
                    v-model="formData.status"
                    placeholder="请选择状态"
                    style="width: 100%"
                    size="large"
                  >
                    <el-option label="未开始" :value="0">
                      <div class="status-option">
                        <el-tag type="info" size="small">未开始</el-tag>
                        <span class="option-desc">计划尚未开始</span>
                      </div>
                    </el-option>
                    <el-option label="进行中" :value="1">
                      <div class="status-option">
                        <el-tag type="warning" size="small">进行中</el-tag>
                        <span class="option-desc">计划正在执行</span>
                      </div>
                    </el-option>
                    <el-option label="已完成" :value="2">
                      <div class="status-option">
                        <el-tag type="success" size="small">已完成</el-tag>
                        <span class="option-desc">计划已完成</span>
                      </div>
                    </el-option>
                    <el-option label="已暂停" :value="3">
                      <div class="status-option">
                        <el-tag type="danger" size="small">已暂停</el-tag>
                        <span class="option-desc">计划已暂停</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 描述信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Document /></el-icon>
              <span>描述信息</span>
            </div>

            <el-form-item label="计划描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="4"
                placeholder="请输入计划的详细描述信息..."
                maxlength="500"
                show-word-limit
                resize="none"
              />
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <template v-if="!isViewMode">
              <el-button
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                size="large"
                class="submit-btn"
              >
                <el-icon v-if="!loading"><Check /></el-icon>
                {{ loading ? '保存中...' : '保存修改' }}
              </el-button>
              <el-button
                @click="handleCancel"
                size="large"
                class="cancel-btn"
              >
                <el-icon><Close /></el-icon>
                取消
              </el-button>
            </template>
            <template v-else>
              <el-button
                @click="handleCancel"
                size="large"
                class="cancel-btn"
              >
                <el-icon><ArrowLeft /></el-icon>
                返回
              </el-button>
            </template>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import {
  Edit,
  View,
  ArrowLeft,
  Document,
  Box,
  Calendar,
  Setting,
  Check,
  Close
} from "@element-plus/icons-vue";
import { getProductionPlanById, updateProductionPlan, type ProductionPlan } from "@/api/production";

const router = useRouter();
const route = useRoute();
const formRef = ref();
const loading = ref(false);

// 判断是否为查看模式
const isViewMode = computed(() => route.query.mode === 'view');

// 表单数据
const formData = reactive<ProductionPlan>({
  plan_code: "",
  plan_name: "",
  product_name: "",
  plan_quantity: 0,
  actual_quantity: 0,
  planStartTime: "",
  completeTime: "",  // 改为completeTime
  status: 0,
  priority: 2,
  description: ""
});

// 表单验证规则
const rules = {
  plan_code: [
    { required: true, message: "请输入计划编号", trigger: "blur" }
  ],
  plan_name: [
    { required: true, message: "请输入计划名称", trigger: "blur" }
  ],
  product_name: [
    { required: true, message: "请输入产品名称", trigger: "blur" }
  ],
  plan_quantity: [
    { required: true, message: "请输入计划数量", trigger: "blur" },
    { type: "number", min: 1, message: "计划数量必须大于0", trigger: "blur" }
  ],
  planStartTime: [
    { required: true, message: "请选择计划开始时间", trigger: "change" }
  ],
  completeTime: [  // 改为completeTime
    { required: true, message: "请选择计划结束时间", trigger: "change" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value && formData.planStartTime) {
          if (new Date(value) <= new Date(formData.planStartTime)) {
            callback(new Error("完成时间必须晚于开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  priority: [
    { required: true, message: "请选择优先级", trigger: "change" }
  ],
  status: [
    { required: true, message: "请选择状态", trigger: "change" }
  ]
};

// 获取生产计划详情
const fetchPlanDetail = async () => {
  const id = route.params.id as string;
  if (!id) return;

  loading.value = true;
  try {
    const response = await getProductionPlanById(parseInt(id));
    Object.assign(formData, response.data);
  } catch (error) {
    console.error("获取生产计划详情失败:", error);
    ElMessage.error("获取生产计划详情失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    const id = route.params.id as string;
    await updateProductionPlan({ ...formData, id: parseInt(id) });
    ElMessage.success("更新成功");
    router.push("/production/plan/list");
  } catch (error) {
    console.error("更新失败:", error);
    ElMessage.error("更新失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  router.back();
};

// 时间验证逻辑
const handleStartTimeChange = (value: string) => {
  if (value && formData.completeTime) {  // 改为completeTime
    // 如果完成时间早于开始时间，清空完成时间
    if (new Date(formData.completeTime) <= new Date(value)) {
      formData.completeTime = "";  // 改为completeTime
      ElMessage.warning("完成时间必须晚于开始时间，已清空完成时间，请重新选择");
    }
  }
};

// 禁用完成日期（不能早于开始日期）
const disabledEndDate = (time: Date) => {
  if (!formData.planStartTime) return false;
  const startDate = new Date(formData.planStartTime);
  return time.getTime() < startDate.getTime() - 24 * 60 * 60 * 1000; // 允许同一天
};

// 禁用完成时间的小时
const disabledEndHours = () => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天，禁用早于开始时间的小时
  if (startDate.toDateString() === endDate.toDateString()) {
    const disabledHours = [];
    for (let i = 0; i < startDate.getHours(); i++) {
      disabledHours.push(i);
    }
    return disabledHours;
  }
  return [];
};

// 禁用完成时间的分钟
const disabledEndMinutes = (hour: number) => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天且同一小时，禁用早于开始时间的分钟
  if (startDate.toDateString() === endDate.toDateString() && hour === startDate.getHours()) {
    const disabledMinutes = [];
    for (let i = 0; i < startDate.getMinutes(); i++) {
      disabledMinutes.push(i);
    }
    return disabledMinutes;
  }
  return [];
};

// 禁用完成时间的秒
const disabledEndSeconds = (hour: number, minute: number) => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天、同一小时、同一分钟，禁用早于或等于开始时间的秒
  if (startDate.toDateString() === endDate.toDateString() &&
      hour === startDate.getHours() &&
      minute === startDate.getMinutes()) {
    const disabledSeconds = [];
    for (let i = 0; i <= startDate.getSeconds(); i++) {
      disabledSeconds.push(i);
    }
    return disabledSeconds;
  }
  return [];
};

onMounted(() => {
  fetchPlanDetail();
});
</script>

<style scoped>
.production-plan-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  color: #667eea;
  font-size: 32px;
}

.title-text {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn {
  height: 44px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 24px;
}

.form-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.form-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-icon {
  color: #667eea;
  font-size: 22px;
}

.form-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.modern-form {
  padding: 32px;
}

/* 表单分组样式 */
.form-section-group {
  margin-bottom: 32px;
  padding: 24px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.section-icon {
  color: #667eea;
  font-size: 18px;
}

/* 表单项样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 禁用状态样式 */
:deep(.el-form-item.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  border-color: #e4e7ed;
  color: #a8abb2;
}

:deep(.el-form-item.is-disabled .el-textarea__inner) {
  background-color: #f5f5f5;
  border-color: #e4e7ed;
  color: #a8abb2;
}

/* 选择器选项样式 */
.priority-option, .status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.option-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 操作按钮样式 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 32px;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 0 32px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.cancel-btn {
  border-radius: 8px;
  padding: 0 32px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .production-plan-container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .modern-form {
    padding: 20px;
  }

  .form-section-group {
    padding: 16px;
    margin-bottom: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .submit-btn, .cancel-btn {
    width: 100%;
  }
}
</style>