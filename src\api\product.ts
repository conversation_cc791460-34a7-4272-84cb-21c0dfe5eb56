import { http } from "@/utils/http";

// 产品类型定义（匹配后端ProductDTO）
export interface Product {
  id?: number;
  name: string;
  code: string;
  model: string;
  category: string;
  price: number;
  completeTime?: string;
  pic?: string;  // 产品图片（用于列表显示）
  status?: number; // 产品状态：0未完成，1已完成
  description?: string; // 产品描述（用于列表显示）
  isInStock?: number; // 入库状态：0未入库，1已入库
  stockQuantity?: number; // 库存数量
}

// 产品入库DTO（匹配后端ProductInStockDTO）
export interface ProductInStockDTO {
  productId: string | number;  // 支持字符串和数字，让后端处理类型转换
  quantity: number;
}

// 产品库存信息（匹配后端ProductStock实体）
export interface ProductStock {
  id?: number;
  productId: number;
  productName?: string;
  productCode?: string;
  quantity: number;
  inStockTime?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

// 产品查询参数
export interface ProductQueryVO {
  name?: string;
  code?: string;
  model?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  status?: number;
  current?: number;
  size?: number;
}

// 统一结果集类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 分页结果类型
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
}

/**
 * 获取所有产品列表（用于下拉选择）
 */
export const getAllProducts = () => {
  return http.request<Result<Product[]>>("get", "/api/product/list");
};

/**
 * 根据ID查询产品详情
 */
export const getProductById = (id: number) => {
  return http.request<Result<Product>>("get", `/api/product/get/${id}`);
};

/**
 * 根据产品编码查询产品
 */
export const getProductByCode = (code: string) => {
  return http.request<Result<Product>>("get", `/api/product/code/${code}`);
};

/**
 * 新增产品
 */
export const addProduct = (data: Product) => {
  return http.request<Result>("post", "/api/product/add", { data });
};

/**
 * 创建产品（别名）
 */
export const createProduct = (data: Product) => {
  return http.request<Result>("post", "/api/product/add", { data });
};

/**
 * 更新产品
 */
export const updateProduct = (data: Product) => {
  return http.request<Result>("put", "/api/product/update", { data });
};

/**
 * 删除产品
 */
export const deleteProduct = (id: number) => {
  return http.request<Result>("delete", `/api/product/delete/${id}`);
};

/**
 * 产品入库
 */
export const productInStock = (data: ProductInStockDTO) => {
  // 确保数据类型正确
  const requestData = {
    productId: Number(data.productId),
    quantity: Number(data.quantity)
  };

  return http.request<Result>("post", "/api/product/stock", {
    data: requestData,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * 查看产品库存
 */
export const getProductStock = (productId: number) => {
  return http.request<Result<ProductStock>>("get", `/api/stock/get/${productId}`);
};

/**
 * 获取所有库存列表
 */
export const getAllProductStock = () => {
  return http.request<Result<ProductStock[]>>("get", "/api/stock/list");
};
