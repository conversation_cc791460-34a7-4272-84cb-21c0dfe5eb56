-- 检查原料消耗数据的SQL脚本

-- 1. 检查生产计划表
SELECT '=== 生产计划表数据 ===' as info;
SELECT id, plan_code, product_name, status, create_time 
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 5;

-- 2. 检查原料表
SELECT '=== 原料表数据 ===' as info;
SELECT id, code, name, unit, quantity 
FROM materials 
LIMIT 5;

-- 3. 检查生产计划原料关联表
SELECT '=== 生产计划原料关联表数据 ===' as info;
SELECT COUNT(*) as total_records 
FROM production_plan_material;

-- 4. 查看具体的原料消耗记录
SELECT '=== 原料消耗详情 ===' as info;
SELECT 
    ppm.id,
    ppm.plan_id,
    pp.plan_code,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark,
    ppm.create_time
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
ORDER BY ppm.plan_id, ppm.material_id
LIMIT 10;

-- 5. 按生产计划分组统计
SELECT '=== 按生产计划分组统计 ===' as info;
SELECT 
    ppm.plan_id,
    pp.plan_code,
    pp.product_name,
    COUNT(ppm.id) as material_count,
    SUM(ppm.required_quantity) as total_quantity
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
GROUP BY ppm.plan_id, pp.plan_code, pp.product_name
ORDER BY ppm.plan_id;

-- 6. 测试API查询（模拟后端SQL）
SELECT '=== 模拟API查询结果 ===' as info;
SELECT 
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 1  -- 测试计划ID=1
ORDER BY ppm.material_id;
