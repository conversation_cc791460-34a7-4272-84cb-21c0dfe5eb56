package com.geek.factory.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产计划视图对象，包含关联的产品名称和产线名称
 */
@Data
public class ProductionPlanVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 计划编号
     */
    private String planCode;

    /**
     * 产品ID
     */
    private Long productId;
    
    /**
     * 产品名称（关联查询）
     */
    private String productName;
    
    /**
     * 产线ID
     */
    private Long productLineId;
    
    /**
     * 产线名称（关联查询）
     */
    private String productLineName;
    
    /**
     * 计划数量
     */
    private BigDecimal quantity;
    
    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 是否入库 (0-未入库, 1-已入库)
     */
    private Integer isInStock;
    
    /**
     * 优先级（如果需要的话）
     */
    private Integer priority;
    
    /**
     * 备注
     */
    private String remarks;
}
