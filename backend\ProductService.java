package com.geek.factory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.entity.Product;
import com.geek.factory.vo.query.ProductQueryVO;

import java.util.List;

/**
 * @Description 产品服务接口
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询产品列表
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<Product> selectProductPage(ProductQueryVO queryVO);

    /**
     * 新增产品
     * @param product 产品信息
     * @return 是否成功
     */
    boolean addProduct(Product product);

    /**
     * 更新产品
     * @param product 产品信息
     * @return 是否成功
     */
    boolean updateProduct(Product product);

    /**
     * 删除产品
     * @param id 产品ID
     * @return 是否成功
     */
    boolean deleteProduct(Integer id);

    /**
     * 批量删除产品
     * @param ids 产品ID列表
     * @return 是否成功
     */
    boolean deleteProductBatch(List<Integer> ids);

    /**
     * 根据分类查询产品列表
     * @param category 产品分类
     * @return 产品列表
     */
    List<Product> getProductsByCategory(String category);

    /**
     * 根据价格区间查询产品
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 产品列表
     */
    List<Product> getProductsByPriceRange(Double minPrice, Double maxPrice);

    /**
     * 获取产品分类统计
     * @return 统计结果
     */
    List<Product> getProductCategoryStatistics();
}
