<template>
  <div class="p-4">
    <el-form :model="form" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="发票代码">
            <el-input v-model="form.code" />
          </el-form-item>
          <el-form-item label="发票号码">
            <el-input v-model="form.number" />
          </el-form-item>
          <el-form-item label="开票日期">
            <el-date-picker
              v-model="form.date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="校验码">
            <el-input v-model="form.checkCode" />
          </el-form-item>
          <el-form-item label="机器编号">
            <el-input v-model="form.machineCode" />
          </el-form-item>
          <el-form-item label="购方名称">
            <el-input v-model="form.buyerName" />
          </el-form-item>
          <el-form-item label="购方税号">
            <el-input v-model="form.buyerTaxCode" />
          </el-form-item>
          <el-form-item label="购方地址电话">
            <el-input v-model="form.buyerAddressPhone" />
          </el-form-item>
          <el-form-item label="购方开户行及账号">
            <el-input v-model="form.buyerBankAccount" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="销方名称">
            <el-input v-model="form.sellerName" />
          </el-form-item>
          <el-form-item label="销方税号">
            <el-input v-model="form.sellerTaxCode" />
          </el-form-item>
          <el-form-item label="销方地址电话">
            <el-input v-model="form.sellerAddressPhone" />
          </el-form-item>
          <el-form-item label="销方开户行及账号">
            <el-input v-model="form.sellerBankAccount" />
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.remark" rows="2" />
          </el-form-item>
          <el-form-item label="票种类型">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option label="电子普通发票" value="电子普通发票" />
              <el-option label="增值税专用发票" value="增值税专用发票" />
            </el-select>
          </el-form-item>
          <el-form-item label="收款人">
            <el-input v-model="form.receiptor" />
          </el-form-item>
          <el-form-item label="开票人">
            <el-input v-model="form.issuer" />
          </el-form-item>
          <el-form-item label="复核人">
            <el-input v-model="form.reviewer" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="详细发票类型">
            <el-input v-model="form.typeOrg" />
          </el-form-item>
          <el-form-item label="价税合计(大写)">
            <el-input v-model="form.amountInWords" />
          </el-form-item>
          <el-form-item label="价税合计(小写)">
            <el-input v-model="form.amountInFiguers" />
          </el-form-item>
          <el-form-item label="密码区">
            <el-input v-model="form.password" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="图片地址">
            <el-input v-model="form.url" placeholder="可为OSS地址" />
          </el-form-item>
          <el-form-item label="发票详情ID">
            <el-input v-model="form.invoiceItemId" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { useRouter } from "vue-router";
import { createInvoice } from "@/api/invoice";
import { ElMessage } from "element-plus";

const router = useRouter();

const form = reactive({
  code: "",
  number: "",
  date: "",
  checkCode: "",
  machineCode: "",
  buyerName: "",
  buyerTaxCode: "",
  buyerAddressPhone: "",
  buyerBankAccount: "",
  sellerName: "",
  sellerTaxCode: "",
  sellerAddressPhone: "",
  sellerBankAccount: "",
  remark: "",
  type: "",
  receiptor: "",
  issuer: "",
  reviewer: "",
  invoiceItemId: undefined,
  typeOrg: "",
  amountInWords: "",
  amountInFiguers: "",
  password: "",
  url: ""
});

const submit = async () => {
  const res = await createInvoice(form);
  if (res.success) {
    ElMessage.success("新增成功");
    router.push("/contract/tInvoice/list");
  }
};

const goBack = () => {
  router.push("/contract/tInvoice/list");
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 16px;
}
</style>
