import { $t } from "@/plugins/i18n";
import { device } from "@/router/enums";

const Layout = () => import("@/layout/index.vue");

export default {
    path: "/device",
    name: "Device",
    component: Layout,
    redirect: "/device/repair-report",
    meta: {
      title: $t("设备管理"),
      icon: "ri:tools-line",
      rank: device
    },
    children: [
      {
        path: "/device/repair-report",
        name: "DeviceRepairReport",
        component: () => import("@/views/device/repair-report/index.vue"),
        meta: {
          title: $t("维修上报"),
          showParent: true
        }
      },
      {
        path: "/device/process-approval",
        name: "DeviceProcessApproval",
        component: () => import("@/views/device/process-approval/index.vue"),
        meta: {
          title: $t("流程审批"),
          showParent: true
        }
      },
      {
        path: "/device/repair-record",
        name: "DeviceRepairRecord",
        component: () => import("@/views/device/repair-record/index.vue"),
        meta: {
          title: $t("维修记录单"),
          showParent: true
        }
      }
    ]
  };
