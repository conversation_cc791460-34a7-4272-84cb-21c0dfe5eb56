package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Device;
import com.geek.factory.mapper.DeviceMapper;
import com.geek.factory.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description 设备服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {
}
