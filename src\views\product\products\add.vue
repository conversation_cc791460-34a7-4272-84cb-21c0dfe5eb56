<template>
  <div class="product-add-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button type="default" @click="goBack" class="back-btn">
            <template #icon>
              <component :is="useRenderIcon(ArrowLeft)" />
            </template>
            返回产品列表
          </el-button>
          <div class="page-title-section">
            <component :is="useRenderIcon(Plus)" class="title-icon" />
            <div class="title-text">
              <h1 class="page-title">新增产品</h1>
              <p class="page-subtitle">Add New Product</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="main-content">
      <el-card class="form-card">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          class="product-form"
        >
          <div class="form-section">
            <div class="section-title">基本信息</div>
            
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入产品名称"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品编码" prop="code">
                  <el-input
                    v-model="formData.code"
                    placeholder="请输入产品编码"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="产品型号" prop="model">
                  <el-input
                    v-model="formData.model"
                    placeholder="请输入产品型号"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品分类" prop="category">
                  <el-select
                    v-model="formData.category"
                    placeholder="请选择产品分类"
                    style="width: 100%"
                  >
                    <el-option label="主板类" value="主板类" />
                    <el-option label="显示器件" value="显示器件" />
                    <el-option label="电源类" value="电源类" />
                    <el-option label="存储器件" value="存储器件" />
                    <el-option label="连接器件" value="连接器件" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="单价" prop="price">
                  <el-input-number
                    v-model="formData.price"
                    :min="0"
                    :precision="2"
                    placeholder="请输入单价"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="完成时间" prop="completeTime">
                  <el-date-picker
                    v-model="formData.completeTime"
                    type="datetime"
                    placeholder="请选择完成时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DDTHH:mm:ss"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-actions">
            <el-button @click="goBack">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="loading">
              保存
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { createProduct } from "@/api/product";

import ArrowLeft from "@iconify-icons/ep/arrow-left";
import Plus from "@iconify-icons/ep/plus";

const router = useRouter();
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const formData = reactive({
  name: "",
  code: "",
  model: "",
  category: "",
  price: 0,
  completeTime: ""
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: "请输入产品名称", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入产品编码", trigger: "blur" }
  ],
  model: [
    { required: true, message: "请输入产品型号", trigger: "blur" }
  ],
  category: [
    { required: true, message: "请选择产品分类", trigger: "change" }
  ],
  price: [
    { required: true, message: "请输入单价", trigger: "blur" }
  ]
};

// 返回列表页
const goBack = () => {
  router.push("/production/product/list");
};

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        console.log("提交的表单数据:", formData);
        const response = await createProduct(formData);
        console.log("创建产品响应:", response);
        ElMessage.success("产品创建成功");
        goBack();
      } catch (error) {
        console.error("创建产品失败:", error);
        // 显示更详细的错误信息
        if (error?.response?.data?.msg) {
          ElMessage.error(`创建失败: ${error.response.data.msg}`);
        } else if (error?.message) {
          ElMessage.error(`创建失败: ${error.message}`);
        } else {
          ElMessage.error("创建产品失败，请检查网络连接");
        }
      } finally {
        loading.value = false;
      }
    } else {
      console.log("表单验证失败");
      ElMessage.warning("请填写完整的产品信息");
    }
  });
};
</script>

<style scoped>
.product-add-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  height: 40px;
  padding: 0 16px;
}

.page-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.title-text {
  display: flex;
  flex-direction: column;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.main-content {
  max-width: 800px;
}

.form-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.product-form {
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
