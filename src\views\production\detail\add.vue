<template>
  <div class="p-4">
    <el-form :model="form" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生产计划ID">
            <el-input v-model="form.planId" type="number" placeholder="请输入关联的生产计划ID" />
          </el-form-item>
          <el-form-item label="工序名称">
            <el-input v-model="form.processName" />
          </el-form-item>
          <el-form-item label="操作员">
            <el-input v-model="form.operator" />
          </el-form-item>
          <el-form-item label="完成数量">
            <el-input v-model="form.quantityCompleted" type="number" />
          </el-form-item>
          <el-form-item label="不良品数量">
            <el-input v-model="form.quantityDefective" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="选择日期时间"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.status" placeholder="请选择生产状态">
              <el-option label="未开始" value="未开始" />
              <el-option label="进行中" value="进行中" />
              <el-option label="已完成" value="已完成" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.notes" rows="2" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" @click="submit">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive } from "vue";
import { useRouter } from "vue-router";
import { createProductionDetail } from "@/api/production";
import { ElMessage } from "element-plus";

const router = useRouter();

const form = reactive({
  planId: null,
  processName: "",
  operator: "",
  quantityCompleted: 0,
  quantityDefective: 0,
  startTime: "",
  status: "未开始",
  notes: ""
});

const submit = async () => {
  const res = await createProductionDetail(form);
  if (res.success) {
    ElMessage.success("新增成功");
    router.push({ name: "productionDetailList" });
  } else {
    ElMessage.error(res.message || "新增失败");
  }
};

const goBack = () => {
  router.push({ name: "productionDetailList" });
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 16px;
}
</style>