<template>
  <el-form
    ref="formRef"
    :model="localFormData"
    :rules="rules"
    label-width="120px"
    label-position="left"
  >
    <!-- 订单编号 -->
    <el-form-item label="订单编号" prop="number">
      <el-input v-model="localFormData.number" placeholder="请输入订单编号" />
    </el-form-item>

    <!-- 订单类型 -->
    <el-form-item label="订单类型" prop="order_type">
      <el-select
        v-model="localFormData.order_type"
        placeholder="请选择订单类型"
        clearable
      >
        <el-option
          v-for="type in orderTypes"
          :key="type.value"
          :label="type.label"
          :value="type.value"
        />
      </el-select>
    </el-form-item>

    <!-- 金额 -->
    <el-form-item label="金额(元)" prop="amount">
      <el-input-number
        v-model="localFormData.amount"
        :min="0"
        :precision="2"
        controls-position="right"
      />
    </el-form-item>

    <!-- 订单状态 -->
    <el-form-item label="订单状态" prop="order_status">
      <el-radio-group v-model="localFormData.order_status">
        <el-radio :label="0">待处理</el-radio>
        <el-radio :label="1">已确认</el-radio>
        <el-radio :label="2">已发货</el-radio>
        <el-radio :label="3">已完成</el-radio>
        <el-radio :label="4">已取消</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 支付状态 -->
    <el-form-item label="支付状态" prop="pay_status">
      <el-radio-group v-model="localFormData.pay_status">
        <el-radio :label="0">未支付</el-radio>
        <el-radio :label="1">已支付</el-radio>
      </el-radio-group>
    </el-form-item>

    <!-- 开票状态 -->
    <el-form-item label="开票状态" prop="is_invoiced">
      <el-switch
        v-model="localFormData.is_invoiced"
        active-text="已开票"
        inactive-text="未开票"
      />
    </el-form-item>

    <!-- 下单时间 -->
    <el-form-item label="下单时间" prop="order_date">
      <el-date-picker
        v-model="localFormData.order_date"
        type="datetime"
        placeholder="选择日期时间"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import type { Order } from "@/views/contract/order/types";
import cloneDeep from "lodash/cloneDeep";

// 接收 props
const props = defineProps({
  formData: {
    type: Object as () => Order,
    required: true
  }
});

const emit = defineEmits(["submit"]);

// 拷贝 props.formData 到本地副本（避免直接修改 props）
const localFormData = reactive<Order>(cloneDeep(props.formData));

// 如果 props.formData 发生变化，同步更新 localFormData（可选）
watch(
  () => props.formData,
  newVal => {
    Object.assign(localFormData, cloneDeep(newVal));
  },
  { deep: true }
);

// 表单验证规则
const rules = reactive<FormRules>({
  number: [
    { required: true, message: "请输入订单编号", trigger: "blur" },
    { min: 3, max: 50, message: "长度在 3 到 50 个字符", trigger: "blur" }
  ],
  order_type: [
    { required: true, message: "请选择订单类型", trigger: "change" }
  ],
  amount: [
    { required: true, message: "请输入订单金额", trigger: "blur" },
    { type: "number", min: 0, message: "金额不能小于0", trigger: "blur" }
  ],
  order_date: [{ required: true, message: "请选择下单时间", trigger: "change" }]
});

// 订单类型选项
const orderTypes = ref([
  { value: "零售订单", label: "零售订单" },
  { value: "批发订单", label: "批发订单" },
  { value: "在线订单", label: "在线订单" },
  { value: "定制订单", label: "定制订单" },
  { value: "预售订单", label: "预售订单" }
]);

const formRef = ref<FormInstance>();

// 暴露提交方法供父组件调用
const submitForm = async () => {
  if (!formRef.value) return false;
  return new Promise(resolve => {
    formRef.value!.validate(valid => {
      if (valid) {
        emit("submit", { ...localFormData }); // 只传出副本数据
        resolve(true);
      } else {
        resolve(false);
      }
    });
  });
};

// 暴露方法
defineExpose({
  submitForm
});
</script>
