import { http } from "@/utils/http";

// 发票类型定义
export type Invoice = {
  id: string;
  code: string;
  number: string;
  date: string;
  checkCode: string;
  machineCode: string;
  buyerName: string;
  buyerTaxCode: string;
  buyerAddressPhone: string;
  buyerBankAccount: string;
  sellerName: string;
  sellerTaxCode: string;
  sellerAddressPhone: string;
  sellerBankAccount: string;
  remark: string;
  type: string;
  receiptor: string;
  issuer: string;
  reviewer: string;
  invoiceItemId: number;
  typeOrg: string;
  amountInWords: string;
  amountInFiguers: string;
  password: string;
  url: string;
};

export type InvoiceListResult = {
  success: boolean;
  data: {
    list: Invoice[];
    total: number;
    pageSize?: number;
    currentPage?: number;
  };
};

export type BaseResult = {
  success: boolean;
  message?: string;
  data?: any;
};

/**
 * 获取发票分页列表
 * @param params 查询参数
 */
export const fetchInvoiceList = (params: {
  size?: number;
  current?: number;
  [key: string]: any;
}) => {
  return http.request<InvoiceListResult>("post", "/api/invoices/list", {
    params
  });
};

/**
 * 获取发票详情
 * @param id 发票ID
 */
export const getInvoiceDetail = (id: string) => {
  return http.request<BaseResult>("get", `/api/invoices/${id}`);
};

/**
 * 新增发票
 * @param data 发票数据（不含id）
 */
export const createInvoice = (data: Omit<Invoice, "id">) => {
  return http.request<BaseResult>("post", "/api/invoices/add", { data });
};

/**
 * 更新发票
 * @param data 修改内容（必须包含id）
 */
export const updateInvoice = (data: Partial<Invoice>) => {
  return http.request<BaseResult>("put", "/api/invoices", { data });
};

/**
 * 删除发票
 * @param id 发票ID
 */
export const deleteInvoice = (id: string) => {
  return http.request<BaseResult>("delete", `/api/invoices/${id}`);
};

/**
 * 批量删除发票（如果你后端支持）
 * @param ids ID字符串，英文逗号分隔
 */
export const batchDeleteInvoice = (ids: string) => {
  return http.request<BaseResult>("delete", `/api/invoices/${ids}`);
};
