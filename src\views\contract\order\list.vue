<template>
  <div class="order-container">
    <!-- 顶部操作栏和搜索栏 -->
    <div class="action-bar">
      <el-button type="primary" icon="Plus" @click="goToAddPage"
        >新增订单</el-button
      >

      <el-form :model="searchForm" label-width="80px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="订单编号" prop="number">
              <el-input
                v-model="searchForm.number"
                placeholder="请输入订单编号"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="订单类型" prop="order_type">
              <el-select
                v-model="searchForm.order_type"
                placeholder="请选择订单类型"
                clearable
              >
                <el-option
                  v-for="type in orderTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="金额范围" prop="amountRange">
              <el-input-number
                v-model="searchForm.amountRange[0]"
                :min="0"
                placeholder="最小值"
              />
              <span style="margin: 0 5px">-</span>
              <el-input-number
                v-model="searchForm.amountRange[1]"
                :min="0"
                placeholder="最大值"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="支付状态" prop="pay_status">
              <el-select
                v-model="searchForm.pay_status"
                placeholder="请选择支付状态"
                clearable
              >
                <el-option label="未支付" :value="0" />
                <el-option label="已支付" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="订单状态" prop="order_status">
              <el-select
                v-model="searchForm.order_status"
                placeholder="请选择订单状态"
                clearable
              >
                <el-option label="待处理" :value="0" />
                <el-option label="已确认" :value="1" />
                <el-option label="已发货" :value="2" />
                <el-option label="已完成" :value="3" />
                <el-option label="已取消" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="开票状态" prop="is_invoiced">
              <el-select
                v-model="searchForm.is_invoiced"
                placeholder="请选择开票状态"
                clearable
              >
                <el-option label="未开票" :value="false" />
                <el-option label="已开票" :value="true" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 订单列表表格 -->
    <el-table v-loading="loading" :data="orderList" stripe style="width: 100%">
      <!-- 订单ID -->
      <el-table-column prop="id" label="订单ID" width="120" sortable />

      <!-- 订单编号 -->
      <el-table-column prop="number" label="订单编号" width="180" />

      <!-- 订单类型 -->
      <el-table-column prop="order_type" label="订单类型" width="150" />

      <!-- 订单金额 -->
      <el-table-column prop="amount" label="金额(元)" width="120" align="right">
        <template #default="{ row }">
          {{ formatAmount(row.amount) }}
        </template>
      </el-table-column>

      <!-- 订单状态 -->
      <el-table-column prop="order_status" label="订单状态" width="120">
        <template #default="{ row }">
          <el-tag :type="statusTagType(row.order_status)">
            {{ formatStatus(row.order_status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 支付状态 -->
      <el-table-column prop="pay_status" label="支付状态" width="120">
        <template #default="{ row }">
          {{ formatPayStatus(row.pay_status) }}
        </template>
      </el-table-column>

      <!-- 开票状态 -->
      <el-table-column prop="is_invoiced" label="开票状态" width="100">
        <template #default="{ row }">
          {{ row.is_invoiced ? "已开票" : "未开票" }}
        </template>
      </el-table-column>

      <!-- 订单日期 -->
      <el-table-column prop="order_date" label="下单时间" width="180" sortable>
        <template #default="{ row }">
          {{ formatDate(row.order_date) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="openEditDialog(row)"
            >编辑</el-button
          >
          <el-button size="small" type="danger" @click="handleDelete(row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        layout="total, prev, pager, next, jumper"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 编辑订单弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="`编辑订单 #${currentOrder?.id}`"
      width="600px"
      destroy-on-close
    >
      <OrderForm
        ref="editFormRef"
        :form-data="currentOrder"
        @submit="handleEditSubmit"
      />

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEditForm">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import OrderForm from "@/views/contract/order/orderForm.vue";
import type { Order } from "@/views/contract/order/types";
import { deleteOrderById, fetchOrderList, updateOrder } from "@/api/order";
const router = useRouter();

// 订单数据类型
// interface Order {
//   id: number;
//   number: string;
//   order_type: string;
//   amount: number;
//   order_status: number;
//   pay_status: number;
//   is_invoiced: boolean;
//   order_date: string;
// }

// 订单类型选项
const orderTypes = ref([
  { value: "零售订单", label: "零售订单" },
  { value: "批发订单", label: "批发订单" },
  { value: "在线订单", label: "在线订单" },
  { value: "定制订单", label: "定制订单" },
  { value: "预售订单", label: "预售订单" }
]);

// 搜索表单
const searchForm = reactive({
  number: "",
  order_type: "",
  amountRange: [null, null] as [number | null, number | null],
  pay_status: null as number | null,
  order_status: null as number | null,
  is_invoiced: null as boolean | null
});

// 订单列表数据
const orderList = ref<Order[]>([]);
const loading = ref(false); // 添加加载状态

// 编辑弹窗相关状态
const editDialogVisible = ref(false);
const currentOrder = ref<Order | null>(null);
const editFormRef = ref<any>(null);

// 分页参数
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
});

// 初始化时加载订单数据
onMounted(() => {
  loadOrderData();
});

// 从API加载订单数据
const loadOrderData = async () => {
  try {
    loading.value = true;
    const queryParams = {
      now: pagination.value.current,
      size: pagination.value.size,
      query: {
        number: searchForm.number,
        orderType: searchForm.order_type,
        minAmount: searchForm.amountRange[0],
        maxAmount: searchForm.amountRange[1],
        payStatus: searchForm.pay_status,
        orderStatus: searchForm.order_status,
        isInvoiced: searchForm.is_invoiced
      }
    };

    const response = await fetchOrderList(queryParams);

    if (response.success) {
      orderList.value = response.data.records.map(order => ({
        id: order.id,
        number: order.number,
        order_type: order.orderType,
        amount: order.amount,
        order_status: order.orderStatus,
        pay_status: order.payStatus,
        is_invoiced: order.isInvoiced,
        order_date: order.orderDate
      }));
      pagination.value.total = response.data.total;
    } else {
      // 修改为使用可能存在的error字段或默认消息
      ElMessage.error((response as any).error || "加载订单数据失败");
    }
  } catch (error) {
    console.error("API请求详细错误:", error);
    ElMessage.error("加载订单数据失败，请检查API连接");
  } finally {
    loading.value = false;
  }
};
// 搜索处理
const handleSearch = () => {
  pagination.value.current = 1; // 重置到第一页
  loadOrderData();
};
// 重置搜索条件
const resetSearch = () => {
  searchForm.number = "";
  searchForm.order_type = "";
  searchForm.amountRange = [null, null];
  searchForm.pay_status = null;
  searchForm.order_status = null;
  searchForm.is_invoiced = null;
  handleSearch();
};
// 处理分页变化
const handlePageChange = (current: number) => {
  pagination.value.current = current;
  loadOrderData();
};

// 跳转到添加页面
const goToAddPage = () => {
  router.push({ name: "orderAdd" }); // 使用路由名称add跳转
};

// 打开编辑弹窗
const openEditDialog = (order: Order) => {
  currentOrder.value = { ...order };
  editDialogVisible.value = true;
};

// 提交编辑表单
const submitEditForm = async () => {
  if (editFormRef.value) {
    await editFormRef.value.submitForm();
  }
};

// 处理编辑提交
const handleEditSubmit = async (formData: Order) => {
  try {
    const response = await updateOrder({
      id: formData.id,
      number: formData.number,
      orderType: formData.order_type,
      amount: formData.amount,
      orderStatus: formData.order_status,
      payStatus: formData.pay_status,
      isInvoiced: formData.is_invoiced,
      orderDate: formData.order_date
    });

    if (response.success) {
      ElMessage.success("订单更新成功！");
      loadOrderData(); // 重新加载数据
      editDialogVisible.value = false;
    } else {
      ElMessage.error(response.message || "订单更新失败");
    }
  } catch (error) {
    ElMessage.error("订单更新失败");
    console.error("API error:", error);
  }
};

// 删除订单
const handleDelete = (orderId: number) => {
  ElMessageBox.confirm("确定要删除该订单吗？此操作不可恢复。", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        const response = await deleteOrderById(orderId);
        if (response.success) {
          ElMessage.success("订单已删除");
          loadOrderData(); // 重新加载数据
        } else {
          ElMessage.error(response.message || "删除订单失败");
        }
      } catch (error) {
        ElMessage.error("删除订单失败");
        console.error("API error:", error);
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 金额格式化
const formatAmount = (amount: number) => {
  return `¥${amount.toFixed(2)}`;
};

// 订单状态转换
const formatStatus = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "待处理",
    1: "已确认",
    2: "已发货",
    3: "已完成",
    4: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 支付状态转换
const formatPayStatus = (status: number) => {
  return status === 1 ? "已支付" : "未支付";
};

// 日期格式化
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString();
};

// 状态标签样式
const statusTagType = (
  status: number
): "success" | "warning" | "info" | "primary" | "danger" => {
  const types: Record<
    number,
    "success" | "warning" | "info" | "primary" | "danger"
  > = {
    0: "warning",
    1: "success",
    2: "primary", // 将空字符串改为合法值
    3: "info",
    4: "danger"
  };
  return types[status] || "primary"; // 未知状态返回默认值
};
</script>

<style scoped>
.order-container {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.search-form {
  margin-top: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 添加分页样式 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
