package com.geek.factory.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 原料查询条件VO
 */
@Data
@ApiModel("原料查询条件")
public class MaterialQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "原料名称")
    private String name;

    @ApiModelProperty(value = "原料类型")
    private String type;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "最小价格")
    private Double minPrice;

    @ApiModelProperty(value = "最大价格")
    private Double maxPrice;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current = 1L;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size = 10L;
}
