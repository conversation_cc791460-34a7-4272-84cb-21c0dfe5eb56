<template>
  <div class="p-4">
    <div class="mb-4 flex justify-between items-center">
      <el-button type="primary" @click="openAdd">新增排班</el-button>
      <div class="flex gap-2">
        <el-input v-model="searchForm.shiftName" placeholder="班次名称" clearable />
        <el-select v-model="searchForm.status" placeholder="状态" clearable style="width: 120px">
          <el-option label="未开始" value="未开始" />
          <el-option label="进行中" value="进行中" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已暂停" value="已暂停" />
        </el-select>
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </div>

    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="productionPlanCode" label="生产计划编号" width="150" />
      <el-table-column prop="lineName" label="产线名称" width="120" />
      <el-table-column prop="shiftName" label="班次名称" width="120" />
      <el-table-column prop="startTime" label="开始日期" width="120" align="center">
        <template #default="{ row }">
          {{ formatDate(row.startTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="endTime" label="结束日期" width="120" align="center">
        <template #default="{ row }">
          {{ formatDate(row.endTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="large">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="openEdit(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="mt-4"
      background
      layout="total, prev, pager, next"
      :total="paginationConfig.total"
      :page-size="paginationConfig.size"
      :current-page="paginationConfig.current"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="50%">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产计划ID" prop="productionPlanId">
              <el-input-number
                v-model="formData.productionPlanId"
                placeholder="请输入生产计划ID"
                :min="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产计划编号" prop="productionPlanCode">
              <el-input
                v-model="formData.productionPlanCode"
                placeholder="系统自动生成"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产线" prop="lineName">
              <el-select
                v-model="formData.lineName"
                placeholder="请选择产线"
                style="width: 100%"
              >
                <el-option label="SMT贴片生产线" value="SMT贴片生产线" />
                <el-option label="DIP插件生产线" value="DIP插件生产线" />
                <el-option label="组装测试线" value="组装测试线" />
                <el-option label="包装生产线" value="包装生产线" />
                <el-option label="返修检测线" value="返修检测线" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="班次名称" prop="shiftName">
              <el-select
                v-model="formData.shiftName"
                placeholder="请选择班次"
                style="width: 100%"
              >
                <el-option label="早班" value="早班" />
                <el-option label="中班" value="中班" />
                <el-option label="晚班" value="晚班" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="未开始" value="未开始" />
                <el-option label="进行中" value="进行中" />
                <el-option label="已完成" value="已完成" />
                <el-option label="已暂停" value="已暂停" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import {
  getProductionScheduleList,
  getProductionScheduleByShift,
  createProductionSchedule,
  updateProductionSchedule,
  deleteProductionSchedule,
  type ProductionScheduleDTO,
  type ProductionSchedule
} from "@/api/production";

const searchForm = reactive({
  shiftName: "",
  status: "",
  current: 1,
  size: 10
});

const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

const tableData = ref<ProductionScheduleDTO[]>([]);
const dialogVisible = ref(false);
const dialogTitle = ref("");
const formRef = ref<FormInstance>();
const isEdit = ref(false);

const initialFormData: ProductionSchedule = {
  id: undefined,
  productionPlanId: 1, // 默认值改为1
  productLineId: 1, // 默认值改为1
  productionPlanCode: "", // 系统自动生成
  lineName: "SMT贴片生产线", // 默认选择第一个产线
  shiftName: "早班", // 默认选择早班
  startTime: "",
  endTime: "",
  status: "未开始"
};
const formData = ref<ProductionSchedule>({ ...initialFormData });

// 工具函数
const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return '-';
  // 只返回日期部分，不包含时间
  return dateStr.split(' ')[0];
};

// 日期格式转换函数：前端YYYY-MM-DD转后端yyyy-MM-dd
const convertDateFormat = (dateStr: string) => {
  if (!dateStr) return '';
  // Element Plus返回的是YYYY-MM-DD格式，后端需要yyyy-MM-dd格式
  // 实际上这两个格式在大多数情况下是相同的，但为了确保兼容性
  return dateStr;
};

const getStatusType = (status: string) => {
  switch (status) {
    case '未开始': return '';
    case '进行中': return 'warning';
    case '已完成': return 'success';
    case '已暂停': return 'danger';
    default: return 'info';
  }
};

const formRules = reactive<FormRules>({
  productionPlanId: [{ required: true, message: "请输入生产计划ID", trigger: "blur" }],
  lineName: [{ required: true, message: "请选择产线", trigger: "change" }],
  shiftName: [{ required: true, message: "请选择班次", trigger: "change" }],
  startTime: [{ required: true, message: "请选择开始日期", trigger: "change" }],
  endTime: [{ required: true, message: "请选择结束日期", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
});

// 注意：生产计划现在改为手动输入，产线使用固定选项，不再需要API调用

// 获取排班列表
const getList = async () => {
  try {
    let response: any;

    // 如果有班次名称搜索条件，使用专门的班次查询接口
    if (searchForm.shiftName && searchForm.shiftName.trim()) {
      console.log("根据班次查询排班，班次：", searchForm.shiftName);
      response = await getProductionScheduleByShift(searchForm.shiftName.trim());
    } else {
      // 否则获取所有排班列表
      const params: ProductionScheduleDTO = {
        ...searchForm,
        current: paginationConfig.current,
        size: paginationConfig.size
      };
      response = await getProductionScheduleList(params);
    }

    if (response.success && response.data) {
      // 处理数据，确保数据格式一致
      let dataList: ProductionScheduleDTO[] = Array.isArray(response.data) ? response.data : [];

      // 如果有状态筛选条件，进行前端过滤
      if (searchForm.status && searchForm.status.trim()) {
        dataList = dataList.filter((item: ProductionScheduleDTO) => item.status === searchForm.status);
      }

      tableData.value = dataList;
      paginationConfig.total = dataList.length;
      console.log("获取排班列表成功，共", dataList.length, "条记录");
    } else {
      tableData.value = [];
      paginationConfig.total = 0;
      ElMessage.warning(response.msg || "未找到相关数据");
    }
  } catch (error) {
    console.error("获取排班列表失败：", error);
    ElMessage.error("获取数据失败");
    tableData.value = [];
    paginationConfig.total = 0;
  }
};

const handleCurrentChange = (page: number) => {
  paginationConfig.current = page;
  getList();
};

const handleDelete = (id: number) => {
  ElMessageBox.confirm("确认删除该排班吗？", "提示", { type: "warning" })
    .then(() => {
      deleteProductionSchedule(id).then(() => {
        ElMessage.success("删除成功");
        getList();
      });
    })
    .catch(() => {});
};

const resetSearch = () => {
  searchForm.shiftName = "";
  searchForm.status = "";
  getList();
};

const openAdd = () => {
  isEdit.value = false;
  dialogTitle.value = "新增排班";
  formData.value = { ...initialFormData };
  // 生产计划编号将由后端自动生成
  formData.value.productionPlanCode = "系统自动生成";
  dialogVisible.value = true;
};

const openEdit = (row: ProductionScheduleDTO) => {
  isEdit.value = true;
  dialogTitle.value = "编辑排班";
  formData.value = {
    id: row.id,
    productionPlanId: row.productionPlanId || 1,
    productLineId: row.productLineId || 1,
    productionPlanCode: row.productionPlanCode || "",
    lineName: row.lineName || "", 
    shiftName: row.shiftName || "",
    // 确保只使用日期部分
    startTime: row.startTime || "",
    endTime: row.endTime || "",
    status: row.status || "未开始"
  };
  dialogVisible.value = true;
};

// 移除不需要的格式化函数
// const formatTimeForEdit = (timeStr: string | undefined): string => { ... }
// const formatTimeForSubmit = (timeStr: string | undefined): string => { ... }

// 格式化提交时的时间（LocalDate格式，只需要日期）
const formatTimeForSubmit = (timeStr: string | undefined): string => {
  if (!timeStr) return "";
  
  // 确保只返回日期部分 YYYY-MM-DD
  if (timeStr.includes(' ')) {
    return timeStr.split(' ')[0];
  }
  
  return timeStr;
};

// 根据产线ID获取产线名称的辅助函数
const getProductLineNameById = (productLineId: number): string => {
  const lineMapping: { [key: number]: string } = {
    1: "SMT贴片生产线",
    2: "DIP插件生产线",
    3: "组装测试线",
    4: "包装生产线",
    5: "返修检测线"
  };
  return lineMapping[productLineId] || "SMT贴片生产线";
};

const closeDialog = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

const submitForm = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      // 准备提交数据，确保字段正确
      const submitData: any = {
        id: formData.value.id,
        productionPlanId: formData.value.productionPlanId,
        productLineId: getProductLineIdByName(formData.value.lineName || ""),
        shiftName: formData.value.shiftName,
        // 只发送日期部分，不包含时间
        startTime: formData.value.startTime,
        endTime: formData.value.endTime,
        status: formData.value.status
      };

      console.log("提交的数据：", submitData);

      if (isEdit.value) {
        updateProductionSchedule(submitData).then(() => {
          ElMessage.success("更新成功");
          closeDialog();
          getList();
        }).catch(error => {
          console.error("更新失败：", error);
          ElMessage.error("更新失败");
        });
      } else {
        createProductionSchedule(submitData).then(() => {
          ElMessage.success("新增成功");
          closeDialog();
          getList();
        }).catch(error => {
          console.error("新增失败：", error);
          ElMessage.error("新增失败");
        });
      }
    }
  });
};

// 根据产线名称获取产线ID的辅助函数
const getProductLineIdByName = (lineName: string): number => {
  const lineMapping: { [key: string]: number } = {
    "SMT贴片生产线": 1,
    "DIP插件生产线": 2,
    "组装测试线": 3,
    "包装生产线": 4,
    "返修检测线": 5
  };
  return lineMapping[lineName] || 1;
};

onMounted(() => {
  getList();
});
</script> 
