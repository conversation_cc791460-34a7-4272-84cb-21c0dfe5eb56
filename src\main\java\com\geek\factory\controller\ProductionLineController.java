package com.geek.factory.controller;

import com.geek.factory.common.Result;
import com.geek.factory.constant.MessageConstant;
import com.geek.factory.entity.ProductionLine;
import com.geek.factory.service.ProductionLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 产线管理控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/production/line")
@Api(tags = "产线管理")
@CrossOrigin
public class ProductionLineController {

    @Autowired
    private ProductionLineService productionLineService;

    /**
     * 获取所有产线列表
     */
    @GetMapping("/all")
    @ApiOperation(value = "获取所有产线列表", notes = "获取系统中所有产线信息")
    public Result getAllProductionLines() {
        log.info("获取所有产线列表");
        try {
            List<ProductionLine> lines = productionLineService.getAllProductionLines();
            log.info("获取产线列表成功，共{}条记录", lines.size());
            return new Result(true, MessageConstant.QUERY_SUCCESS, lines);
        } catch (Exception e) {
            log.error("获取产线列表失败", e);
            return new Result(false, MessageConstant.QUERY_FAILED, null);
        }
    }

    /**
     * 根据ID获取产线信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取产线信息")
    public Result getProductionLineById(@PathVariable Long id) {
        log.info("根据ID获取产线信息，ID：{}", id);
        try {
            ProductionLine line = productionLineService.getProductionLineById(id);
            if (line != null) {
                log.info("获取产线信息成功，产线名称：{}", line.getName());
                return new Result(true, MessageConstant.QUERY_SUCCESS, line);
            } else {
                log.warn("产线不存在，ID：{}", id);
                return new Result(false, MessageConstant.QUERY_FAILED, null);
            }
        } catch (Exception e) {
            log.error("获取产线信息失败，ID：{}", id, e);
            return new Result(false, MessageConstant.QUERY_FAILED, null);
        }
    }
}
