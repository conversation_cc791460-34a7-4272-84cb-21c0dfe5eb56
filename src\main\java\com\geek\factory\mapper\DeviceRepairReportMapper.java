package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.DeviceRepairReport;
import org.apache.ibatis.annotations.*;

import java.util.List;

import java.util.List;

/**
 * 设备维修上报Mapper接口
 */
@Mapper
public interface DeviceRepairReportMapper extends BaseMapper<DeviceRepairReport> {

    /**
     * 分页查询设备维修上报记录（包含产线名称）
     */
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "device_name", property = "deviceName"),
        @Result(column = "device_type", property = "deviceType"),
        @Result(column = "product_line_id", property = "productLineId"),
        @Result(column = "line_name", property = "productLineName"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "submitter", property = "submitter"),
        @Result(column = "submit_time", property = "submitTime"),
        @Result(column = "process_instance_id", property = "processInstanceId")
    })
    @Select("<script>" +
            "SELECT r.*, l.line_name " +
            "FROM device_repair_report r " +
            "LEFT JOIN product_line l ON r.product_line_id = l.id " +
            "WHERE 1=1 " +
            "<if test='deviceName != null and deviceName != \"\"'>" +
            "AND r.device_name LIKE CONCAT('%', #{deviceName}, '%') " +
            "</if>" +
            "<if test='deviceType != null and deviceType != \"\"'>" +
            "AND r.device_type = #{deviceType} " +
            "</if>" +
            "<if test='productLineId != null'>" +
            "AND r.product_line_id = #{productLineId} " +
            "</if>" +
            "<if test='status != null and status != \"\"'>" +
            "AND r.status = #{status} " +
            "</if>" +
            "<if test='submitter != null and submitter != \"\"'>" +
            "AND r.submitter LIKE CONCAT('%', #{submitter}, '%') " +
            "</if>" +
            "ORDER BY r.submit_time DESC" +
            "</script>")
    IPage<DeviceRepairReport> selectPageWithConditions(
            Page<DeviceRepairReport> page,
            @Param("deviceName") String deviceName,
            @Param("deviceType") String deviceType,
            @Param("productLineId") Long productLineId,
            @Param("status") String status,
            @Param("submitter") String submitter
    );

    /**
     * 根据ID获取维修上报详情（包含产线名称）
     */
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "device_name", property = "deviceName"),
        @Result(column = "device_type", property = "deviceType"),
        @Result(column = "product_line_id", property = "productLineId"),
        @Result(column = "line_name", property = "productLineName"),
        @Result(column = "description", property = "description"),
        @Result(column = "status", property = "status"),
        @Result(column = "submitter", property = "submitter"),
        @Result(column = "submit_time", property = "submitTime"),
        @Result(column = "process_instance_id", property = "processInstanceId")
    })
    @Select("SELECT r.*, l.line_name " +
            "FROM device_repair_report r " +
            "LEFT JOIN product_line l ON r.product_line_id = l.id " +
            "WHERE r.id = #{id}")
    DeviceRepairReport selectDetailById(@Param("id") Long id);

    /**
     * 根据产线ID查询维修上报记录
     */
    @Select("SELECT * FROM device_repair_report WHERE product_line_id = #{productLineId} ORDER BY submit_time DESC")
    List<DeviceRepairReport> selectByProductLineId(@Param("productLineId") Long productLineId);

    /**
     * 根据状态查询维修上报记录
     */
    @Select("SELECT * FROM device_repair_report WHERE status = #{status} ORDER BY submit_time DESC")
    List<DeviceRepairReport> selectByStatus(@Param("status") String status);

    /**
     * 统计各状态的维修上报数量
     */
    @Select("SELECT status, COUNT(*) as count FROM device_repair_report GROUP BY status")
    List<Object> countByStatus();
}
