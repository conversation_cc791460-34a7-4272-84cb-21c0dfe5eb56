package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 原料实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("materials")
@ApiModel(value = "Material对象", description = "原料管理")
public class Material implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "原料名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "原料类型")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "计量单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty(value = "单价")
    @TableField("price")
    private BigDecimal price;

    @ApiModelProperty(value = "图片路径")
    @TableField("pic")
    private String pic;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
