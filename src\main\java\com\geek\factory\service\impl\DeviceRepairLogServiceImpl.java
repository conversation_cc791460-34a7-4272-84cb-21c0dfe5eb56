package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.dto.DeviceRepairLogDTO;
import com.geek.factory.entity.DeviceRepairLog;
import com.geek.factory.mapper.DeviceRepairLogMapper;
import com.geek.factory.service.DeviceRepairLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 设备维修记录日志服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class DeviceRepairLogServiceImpl extends ServiceImpl<DeviceRepairLogMapper, DeviceRepairLog> implements DeviceRepairLogService {

    @Autowired
    private DeviceRepairLogMapper deviceRepairLogMapper;

    @Override
    public List<DeviceRepairLogDTO> getAllWithLineName() {
        return deviceRepairLogMapper.getAllWithLineName();
    }
}
