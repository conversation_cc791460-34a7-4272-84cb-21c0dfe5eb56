-- 插入测试原料消耗数据
-- 这个脚本会为现有的生产计划添加原料消耗记录

-- 首先检查现有数据
SELECT '=== 检查现有生产计划 ===' as info;
SELECT id, plan_code, product_name FROM production_plan ORDER BY id LIMIT 5;

SELECT '=== 检查现有原料 ===' as info;
SELECT id, code, name, unit FROM materials ORDER BY id LIMIT 10;

-- 清理可能存在的测试数据（可选）
-- DELETE FROM production_plan_material WHERE remark LIKE '%测试数据%';

-- 为生产计划ID=1添加原料消耗记录（如果该计划存在）
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time) 
SELECT 1, m.id, 
  CASE 
    WHEN m.id = 1 THEN 50.00
    WHEN m.id = 2 THEN 25.50  
    WHEN m.id = 3 THEN 15.20
    WHEN m.id = 4 THEN 100.00
    WHEN m.id = 5 THEN 200.00
    ELSE 10.00
  END as required_quantity,
  m.unit,
  CONCAT('测试数据 - ', m.name, '用于生产'),
  NOW()
FROM materials m 
WHERE m.id IN (1,2,3,4,5) 
AND EXISTS (SELECT 1 FROM production_plan WHERE id = 1);

-- 为生产计划ID=2添加原料消耗记录（如果该计划存在）
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time) 
SELECT 2, m.id, 
  CASE 
    WHEN m.id = 2 THEN 30.00
    WHEN m.id = 3 THEN 20.50  
    WHEN m.id = 6 THEN 80.00
    WHEN m.id = 7 THEN 150.00
    ELSE 15.00
  END as required_quantity,
  m.unit,
  CONCAT('测试数据 - ', m.name, '用于生产'),
  NOW()
FROM materials m 
WHERE m.id IN (2,3,6,7,8) 
AND EXISTS (SELECT 1 FROM production_plan WHERE id = 2);

-- 为生产计划ID=3添加原料消耗记录（如果该计划存在）
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time) 
SELECT 3, m.id, 
  CASE 
    WHEN m.id = 1 THEN 40.00
    WHEN m.id = 4 THEN 60.00  
    WHEN m.id = 5 THEN 120.00
    WHEN m.id = 9 THEN 90.00
    ELSE 20.00
  END as required_quantity,
  m.unit,
  CONCAT('测试数据 - ', m.name, '用于生产'),
  NOW()
FROM materials m 
WHERE m.id IN (1,4,5,9,10) 
AND EXISTS (SELECT 1 FROM production_plan WHERE id = 3);

-- 验证插入结果
SELECT '=== 插入结果验证 ===' as info;
SELECT 
    ppm.plan_id,
    pp.plan_code,
    COUNT(ppm.id) as material_count,
    SUM(ppm.required_quantity) as total_quantity
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
WHERE ppm.remark LIKE '%测试数据%'
GROUP BY ppm.plan_id, pp.plan_code
ORDER BY ppm.plan_id;

-- 查看详细的原料消耗记录
SELECT '=== 详细原料消耗记录 ===' as info;
SELECT 
    ppm.plan_id,
    pp.plan_code,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.remark LIKE '%测试数据%'
ORDER BY ppm.plan_id, ppm.material_id;

-- 测试API查询（生产计划ID=1）
SELECT '=== 测试API查询 (计划ID=1) ===' as info;
SELECT 
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 1
ORDER BY ppm.material_id;

SELECT '✅ 测试数据插入完成！现在可以测试原料消耗页面了。' as status;
