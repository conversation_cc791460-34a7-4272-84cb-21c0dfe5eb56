package com.geek.factory.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 产品查询条件VO
 */
@Data
@ApiModel("产品查询条件")
public class ProductQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品型号")
    private String model;

    @ApiModelProperty(value = "产品分类")
    private String category;

    @ApiModelProperty(value = "最小价格")
    private Double minPrice;

    @ApiModelProperty(value = "最大价格")
    private Double maxPrice;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Long current = 1L;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Long size = 10L;
}
