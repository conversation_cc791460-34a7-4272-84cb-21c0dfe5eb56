-- 检查现有生产计划的原料消耗记录

-- 1. 查看所有生产计划
SELECT '=== 所有生产计划 ===' as info;
SELECT id, plan_code, product_id, quantity, plan_start_time 
FROM production_plan 
ORDER BY id DESC 
LIMIT 10;

-- 2. 检查哪些生产计划有原料消耗记录
SELECT '=== 有原料消耗记录的生产计划 ===' as info;
SELECT 
    pp.id,
    pp.plan_code,
    pp.quantity as production_quantity,
    COUNT(ppm.id) as material_count,
    SUM(ppm.required_quantity) as total_material_quantity
FROM production_plan pp
LEFT JOIN production_plan_material ppm ON pp.id = ppm.plan_id
WHERE ppm.id IS NOT NULL
GROUP BY pp.id, pp.plan_code, pp.quantity
ORDER BY pp.id DESC;

-- 3. 检查哪些生产计划没有原料消耗记录
SELECT '=== 没有原料消耗记录的生产计划 ===' as info;
SELECT 
    pp.id,
    pp.plan_code,
    pp.quantity as production_quantity,
    pp.plan_start_time
FROM production_plan pp
LEFT JOIN production_plan_material ppm ON pp.id = ppm.plan_id
WHERE ppm.id IS NULL
ORDER BY pp.id DESC
LIMIT 10;

-- 4. 检查最近的几个生产计划的详细情况
SELECT '=== 最近生产计划详情 ===' as info;
SELECT 
    pp.id,
    pp.plan_code,
    pp.quantity as production_quantity,
    CASE 
        WHEN ppm.id IS NOT NULL THEN '有原料记录'
        ELSE '无原料记录'
    END as material_status
FROM production_plan pp
LEFT JOIN production_plan_material ppm ON pp.id = ppm.plan_id
WHERE pp.id IN (
    SELECT id FROM production_plan ORDER BY id DESC LIMIT 5
)
ORDER BY pp.id DESC;

-- 5. 为最近的生产计划添加示例原料消耗记录
-- 获取最新的5个生产计划ID
SET @plan1 = (SELECT id FROM production_plan ORDER BY id DESC LIMIT 1 OFFSET 0);
SET @plan2 = (SELECT id FROM production_plan ORDER BY id DESC LIMIT 1 OFFSET 1);
SET @plan3 = (SELECT id FROM production_plan ORDER BY id DESC LIMIT 1 OFFSET 2);

SELECT '=== 准备为以下计划添加原料消耗记录 ===' as info;
SELECT @plan1 as plan_id_1, @plan2 as plan_id_2, @plan3 as plan_id_3;

-- 为这些计划添加原料消耗记录（如果原料表有数据的话）
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan1, m.id, 
  CASE 
    WHEN m.id % 4 = 1 THEN 50.00
    WHEN m.id % 4 = 2 THEN 25.50  
    WHEN m.id % 4 = 3 THEN 15.20
    ELSE 30.00
  END as required_quantity,
  COALESCE(m.unit, '个') as unit,
  CONCAT('自动生成 - ', m.name) as remark,
  NOW()
FROM materials m 
WHERE @plan1 IS NOT NULL
LIMIT 3;

INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan2, m.id, 
  CASE 
    WHEN m.id % 3 = 1 THEN 40.00
    WHEN m.id % 3 = 2 THEN 20.00  
    ELSE 35.00
  END as required_quantity,
  COALESCE(m.unit, '个') as unit,
  CONCAT('自动生成 - ', m.name) as remark,
  NOW()
FROM materials m 
WHERE @plan2 IS NOT NULL
LIMIT 4;

INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan3, m.id, 
  CASE 
    WHEN m.id % 2 = 1 THEN 60.00
    ELSE 45.00
  END as required_quantity,
  COALESCE(m.unit, '个') as unit,
  CONCAT('自动生成 - ', m.name) as remark,
  NOW()
FROM materials m 
WHERE @plan3 IS NOT NULL
LIMIT 2;

-- 6. 验证插入结果
SELECT '=== 验证插入结果 ===' as info;
SELECT 
    pp.id,
    pp.plan_code,
    pp.quantity as production_quantity,
    COUNT(ppm.id) as material_count,
    GROUP_CONCAT(CONCAT(m.name, ':', ppm.required_quantity, ppm.unit) SEPARATOR '; ') as materials
FROM production_plan pp
LEFT JOIN production_plan_material ppm ON pp.id = ppm.plan_id
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE pp.id IN (@plan1, @plan2, @plan3)
GROUP BY pp.id, pp.plan_code, pp.quantity
ORDER BY pp.id DESC;

SELECT '✅ 检查和数据生成完成！' as status;
