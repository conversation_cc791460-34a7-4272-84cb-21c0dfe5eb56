-- 检查生产计划表中的完成时间数据
SELECT 
    id,
    plan_code,
    status,
    plan_start_time,
    complete_time,
    create_time
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 10;

-- 检查是否有已完成状态的计划
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN status = '已完成' THEN 1 ELSE 0 END) as completed_count,
    SUM(CASE WHEN complete_time IS NOT NULL THEN 1 ELSE 0 END) as has_complete_time_count
FROM production_plan;

-- 更新一条记录为已完成状态并设置完成时间（用于测试）
UPDATE production_plan 
SET 
    status = '已完成',
    complete_time = NOW()
WHERE id = (
    SELECT id FROM (
        SELECT id FROM production_plan 
        WHERE status != '已完成' 
        ORDER BY create_time DESC 
        LIMIT 1
    ) as temp
);
