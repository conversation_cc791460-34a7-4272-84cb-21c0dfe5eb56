<template>
  <div class="add-container">
    <el-card shadow="always">
      <template #header>
        <div class="card-header">
          <span>添加新订单</span>
          <el-button type="primary" @click="goToList">返回订单列表</el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="left"
      >
        <!-- 订单编号 -->
        <el-form-item label="订单编号" prop="number">
          <el-input v-model="formData.number" placeholder="请输入订单编号" />
        </el-form-item>

        <!-- 订单类型（下拉选择） -->
        <el-form-item label="订单类型" prop="order_type">
          <el-select
            v-model="formData.order_type"
            placeholder="请选择订单类型"
            clearable
          >
            <el-option
              v-for="type in orderTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>

        <!-- 订单金额 -->
        <el-form-item label="金额(元)" prop="amount">
          <el-input-number
            v-model="formData.amount"
            :min="0"
            :precision="2"
            controls-position="right"
          />
        </el-form-item>

        <!-- 订单状态 -->
        <el-form-item label="订单状态" prop="order_status">
          <el-radio-group v-model="formData.order_status">
            <el-radio :label="0">待处理</el-radio>
            <el-radio :label="1">已确认</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 支付状态 -->
        <el-form-item label="支付状态" prop="pay_status">
          <el-radio-group v-model="formData.pay_status">
            <el-radio :label="0">未支付</el-radio>
            <el-radio :label="1">已支付</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 开票状态 -->
        <el-form-item label="开票状态" prop="is_invoiced">
          <el-switch
            v-model="formData.is_invoiced"
            active-text="已开票"
            inactive-text="未开票"
          />
        </el-form-item>

        <!-- 下单时间 -->
        <el-form-item label="下单时间" prop="order_date">
          <el-date-picker
            v-model="formData.order_date"
            type="datetime"
            placeholder="选择日期时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">提交订单</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { createOrder } from "@/api/order";

const router = useRouter();

// 表单数据
const formData = reactive({
  number: "",
  order_type: "",
  amount: 0,
  order_status: 0,
  pay_status: 0,
  is_invoiced: false,
  order_date: ""
});

// 表单验证规则
const rules = reactive<FormRules>({
  number: [
    { required: true, message: "请输入订单编号", trigger: "blur" },
    { min: 3, max: 50, message: "长度在 3 到 50 个字符", trigger: "blur" }
  ],
  order_type: [
    { required: true, message: "请选择订单类型", trigger: "change" }
  ],
  amount: [
    { required: true, message: "请输入订单金额", trigger: "blur" },
    { type: "number", min: 0, message: "金额不能小于0", trigger: "blur" }
  ],
  order_date: [{ required: true, message: "请选择下单时间", trigger: "change" }]
});

// 订单类型选项
const orderTypes = ref([
  { value: "零售订单", label: "零售订单" },
  { value: "批发订单", label: "批发订单" },
  { value: "在线订单", label: "在线订单" },
  { value: "定制订单", label: "定制订单" },
  { value: "预售订单", label: "预售订单" }
]);

const formRef = ref<FormInstance>();

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        const requestData = {
          number: formData.number,
          orderType: formData.order_type,
          amount: formData.amount,
          orderStatus: formData.order_status,
          payStatus: formData.pay_status,
          isInvoiced: formData.is_invoiced,
          orderDate: formData.order_date
        };

        console.log("准备发送的数据:", requestData); // 添加日志

        const response = await createOrder(requestData);

        console.log("API响应:", response); // 添加日志

        if (response.success) {
          ElMessage.success("订单添加成功！");
          setTimeout(() => {
            goToList();
          }, 2000);
        } else {
          ElMessage.error(response.message || "订单添加失败");
        }
      } catch (error) {
        console.error("完整错误信息:", error); // 更详细的错误日志
        ElMessage.error(
          "订单添加失败: " + (error.response?.data?.message || error.message)
        );
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
};

// 跳转到订单列表页（使用路由枚举contract）
const goToList = () => {
  router.push({ name: "list" });
};
</script>

<style scoped>
.add-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form-item {
  margin-bottom: 22px;
}
</style>
