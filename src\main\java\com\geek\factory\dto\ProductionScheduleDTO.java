package com.geek.factory.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 生产排班DTO类
 */
@Data
@ApiModel(value = "ProductionScheduleDTO", description = "生产排班数据传输对象")
public class ProductionScheduleDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "排班编号")
    private String scheduleCode;

    @ApiModelProperty(value = "生产计划ID")
    private Long productionPlanId;

    @ApiModelProperty(value = "产线ID")
    private Long productLineId;

    @ApiModelProperty(value = "班次名称")
    private String shiftName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态")
    private String status;

    // 关联查询字段
    @ApiModelProperty(value = "产线名称")
    private String productLineName;

    @ApiModelProperty(value = "生产计划编号")
    private String productionPlanCode;

    // 分页参数
    @ApiModelProperty(value = "当前页")
    private Integer current;

    @ApiModelProperty(value = "页大小")
    private Integer size;
}
