<template>
  <div class="production-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <el-icon class="title-icon" size="24"><Operation /></el-icon>
            <div class="title-text">
              <h1 class="page-title">生产明细管理</h1>
              <p class="page-subtitle">Production Detail Management</p>
            </div>
          </div>

          <!-- 如果是从生产计划跳转过来，显示计划信息 -->
          <div v-if="fromProductionPlan" class="plan-info-card">
            <div class="plan-info-content">
              <el-icon class="plan-icon"><Document /></el-icon>
              <div class="plan-text">
                <span class="plan-label">当前计划:</span>
                <span class="plan-name">{{ planName }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button
            v-if="fromProductionPlan"
            type="default"
            size="large"
            @click="goBack"
            class="back-btn"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回生产计划
          </el-button>
          <el-button
            type="primary"
            size="large"
            @click="goToAdd"
            class="add-btn"
          >
            <el-icon><Plus /></el-icon>
            新增生产明细
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <el-icon class="search-icon"><Search /></el-icon>
          <span class="search-title">筛选条件</span>
        </div>
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="工序名称">
              <el-input
                v-model="searchForm.processName"
                placeholder="请输入工序名称"
                clearable
                prefix-icon="Search"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择状态"
                clearable
                style="width: 150px"
              >
                <el-option label="全部" value="" />
                <el-option label="进行中" value="进行中" />
                <el-option label="已完成" value="已完成" />
                <el-option label="暂停" value="暂停" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getList" :icon="Search">
                查询
              </el-button>
              <el-button @click="resetSearch" :icon="Refresh">
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <el-icon class="table-icon"><List /></el-icon>
            <span>生产明细列表</span>
          </div>
          <div class="table-stats">
            <span class="stats-text">共 {{ paginationConfig.total }} 条记录</span>
          </div>
        </div>

        <el-table
          :data="tableData"
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
            borderBottom: '2px solid #e5e7eb'
          }"
          :row-style="{ height: '60px' }"
          stripe
        >
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">#{{ row.id }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="processName" label="工序名称" min-width="150">
            <template #default="{ row }">
              <div class="process-info">
                <el-icon class="process-icon"><Setting /></el-icon>
                <span class="process-name">{{ row.processName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="operator" label="操作员" width="120">
            <template #default="{ row }">
              <div class="operator-info">
                <el-avatar :size="32" class="operator-avatar">
                  {{ row.operator?.charAt(0) || 'U' }}
                </el-avatar>
                <span class="operator-name">{{ row.operator }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="quantityCompleted" label="完成数量" width="120" align="center">
            <template #default="{ row }">
              <div class="quantity-info">
                <el-tag type="success" size="large">{{ row.quantityCompleted }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="quantityDefective" label="不良品数量" width="130" align="center">
            <template #default="{ row }">
              <div class="defective-info">
                <el-tag
                  :type="row.quantityDefective > 0 ? 'danger' : 'info'"
                  size="large"
                >
                  {{ row.quantityDefective }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="startTime" label="开始时间" width="180">
            <template #default="{ row }">
              <div class="time-info">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span class="time-text">{{ row.startTime }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="large"
                effect="dark"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="openEdit(row)"
                  :icon="Edit"
                  class="action-btn"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row.id)"
                  :icon="Delete"
                  class="action-btn"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="paginationConfig.total"
        :page-size="paginationConfig.size"
        :current-page="paginationConfig.current"
        :page-sizes="[5, 10, 20, 50]"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        class="modern-pagination"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Operation,
  Document,
  ArrowLeft,
  Plus,
  Search,
  Refresh,
  List,
  Setting,
  Clock,
  Edit,
  Delete
} from "@element-plus/icons-vue";
import {
  getProductionDetailList,
  deleteProductionDetail
} from "@/api/production";

const router = useRouter();
const route = useRoute();

// 从路由参数中获取信息
const fromProductionPlan = computed(() => route.query.from === 'production-plan');
const planName = computed(() => route.query.planName || '生产计划');

// 返回生产计划页面
const goBack = () => {
  router.push('/production/plan/list');
};

const searchForm = reactive({
  processName: "",
  status: ""
});

const paginationConfig = reactive({
  total: 0,
  size: 5,
  current: 1
});

const tableData = ref([]);

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success';
    case '进行中':
      return 'warning';
    case '暂停':
      return 'danger';
    default:
      return 'info';
  }
};

const getList = () => {
  const params = {
    now: paginationConfig.current,
    size: paginationConfig.size,
    query: { ...searchForm }
  };
  getProductionDetailList(params)
    .then(response => {
      tableData.value = response.data.records;
      paginationConfig.total = response.data.total;
    })
    .catch(error => {
      console.error("获取生产明细列表出错:", error);
      ElMessage.error("获取数据失败");
    });
};

const handleCurrentChange = (page: number) => {
  paginationConfig.current = page;
  getList();
};

const handleSizeChange = (size: number) => {
  paginationConfig.size = size;
  paginationConfig.current = 1;
  getList();
};

const handleDelete = (id: number) => {
  ElMessageBox.confirm("确认删除该生产明细吗？", "提示", { type: "warning" })
    .then(() => {
      deleteProductionDetail(id).then(() => {
        ElMessage.success("删除成功");
        getList();
      });
    })
    .catch(() => {});
};

const resetSearch = () => {
  searchForm.processName = "";
  searchForm.status = "";
  paginationConfig.current = 1;
  getList();
};

const openEdit = (row: any) => {
  router.push({ name: "productionDetailEdit", params: { id: row.id } });
};

const goToAdd = () => {
  router.push({ name: "productionDetailAdd" });
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.production-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  color: #667eea;
  font-size: 28px;
}

.title-text {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-weight: 400;
}

.plan-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 16px 20px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.plan-info-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.plan-icon {
  font-size: 20px;
  color: #ffd700;
}

.plan-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.plan-label {
  font-size: 12px;
  opacity: 0.8;
}

.plan-name {
  font-size: 16px;
  font-weight: bold;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.back-btn, .add-btn {
  height: 44px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 24px;
}

.search-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.search-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-icon {
  color: #667eea;
  font-size: 18px;
}

.search-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.search-form {
  padding: 24px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 24px;
}

.table-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.table-icon {
  color: #667eea;
  font-size: 20px;
}

.table-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-text {
  font-size: 14px;
  color: #6b7280;
}

/* 表格内容样式 */
.modern-table {
  border-radius: 0;
}

.process-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.process-icon {
  color: #667eea;
  font-size: 16px;
}

.process-name {
  font-weight: 500;
  color: #374151;
}

.operator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.operator-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.operator-name {
  font-weight: 500;
  color: #374151;
}

.quantity-info, .defective-info {
  display: flex;
  justify-content: center;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.time-icon {
  color: #6b7280;
  font-size: 14px;
}

.time-text {
  font-size: 13px;
  color: #374151;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-pagination {
  --el-pagination-bg-color: transparent;
  --el-pagination-text-color: #374151;
  --el-pagination-border-radius: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .production-detail-container {
    padding: 15px;
  }

  .page-title {
    font-size: 20px;
  }

  .search-form .el-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-bottom: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>