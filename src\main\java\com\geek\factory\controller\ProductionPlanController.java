package com.geek.factory.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.dto.CreateProductionPlanDTO;
import com.geek.factory.dto.ProductionPlanMaterialDTO;
import com.geek.factory.dto.UpdateProductionPlanDTO;
import com.geek.factory.entity.ProductionPlan;
import com.geek.factory.result.Result;
import com.geek.factory.service.ProductionPlanService;
import com.geek.factory.service.PlanCodeGeneratorService;
import com.geek.factory.vo.ProductionPlanVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Validated
@RestController
@RequestMapping("/api/plan")
@RequiredArgsConstructor
@Api(tags = "生产计划管理")
public class ProductionPlanController {

    private final ProductionPlanService planService;
    private final PlanCodeGeneratorService planCodeGeneratorService;

    @PostMapping("/create")
    @ApiOperation("创建生产计划")
    public Result createPlan(@RequestBody CreateProductionPlanDTO dto) {
        try {
            planService.createPlan(dto);
            return new Result(true, "创建成功", null);
        } catch (RuntimeException e) {
            return new Result(false, e.getMessage(), null);
        }
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除生产计划")
    public Result deletePlan(@PathVariable Long id) {
        if (id == null) {
            return new Result(false, "计划ID不能为空", null);
        }
        try {
            planService.deletePlan(id);
            return new Result(true, "删除成功", null);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return new Result(false, e.getMessage(), null);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "系统异常：" + e.getMessage(), null);
        }
    }

    @PutMapping("/update")
    @ApiOperation("更新生产计划")
    public Result updatePlan(@RequestBody UpdateProductionPlanDTO dto) {
        if (dto == null || dto.getId() == null) {
            return new Result(false, "请求参数无效", null);
        }
        try {
            planService.updatePlan(dto);
            return new Result(true, "更新成功", null);
        } catch (RuntimeException e) {
            e.printStackTrace();
            return new Result(false, e.getMessage(), null);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "系统异常：" + e.getMessage(), null);
        }
    }

    @PostMapping("/list")
    @ApiOperation("分页查询生产计划")
    public Result getPlanList(@RequestBody(required = false) Map<String, Object> params) {
        try {
            Integer current = params != null && params.get("current") != null ?
                    Integer.parseInt(params.get("current").toString()) : 1;
            Integer size = params != null && params.get("size") != null ?
                    Integer.parseInt(params.get("size").toString()) : 10;

            // 使用新的方法进行关联查询，返回包含产品名称和产线名称的VO
            Page<ProductionPlanVO> resultPage = planService.getProductionPlanWithNames(current, size, params);

            Map<String, Object> result = new HashMap<>();
            result.put("records", resultPage.getRecords());
            result.put("total", resultPage.getTotal());
            result.put("current", resultPage.getCurrent());
            result.put("size", resultPage.getSize());

            return new Result(true, "查询成功", result);

        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "查询失败", null);
        }
    }

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询生产计划详情")
    public Result getPlanById(@PathVariable Long id) {
        try {
            ProductionPlanVO planVO = planService.getProductionPlanVOById(id);
            if (planVO != null) {
                return new Result(true, "查询成功", planVO);
            } else {
                return new Result(false, "未找到对应的生产计划", null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "查询失败", null);
        }
    }

    @GetMapping("/all")
    @ApiOperation("获取所有生产计划")
    public Result getAllPlans() {
        try {
            // 获取所有数据，使用大的分页参数
            Page<ProductionPlanVO> resultPage = planService.getProductionPlanWithNames(1, 999, null);
            return new Result(true, "查询成功", resultPage.getRecords());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "查询失败", null);
        }
    }

    @GetMapping("/materials/{planId}")
    @ApiOperation("根据生产计划ID查询原料消耗详情")
    public Result getPlanMaterials(@PathVariable Long planId) {
        try {
            List<ProductionPlanMaterialDTO> materials = planService.getPlanMaterialDetails(planId);
            return new Result(true, "查询成功", materials);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "查询失败", null);
        }
    }

    @GetMapping("/code/{planCode}")
    @ApiOperation("根据生产计划编码查询原料消耗详情")
    public Result getPlanMaterialsByCode(@PathVariable String planCode) {
        try {
            List<ProductionPlanMaterialDTO> materials = planService.getPlanMaterialDetailsByCode(planCode);
            return new Result(true, "查询成功", materials);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "查询失败", null);
        }
    }

    @GetMapping("/next-code")
    @ApiOperation("获取下一个可用的计划编号")
    public Result getNextPlanCode() {
        try {
            String nextCode = planCodeGeneratorService.getNextProductionPlanCode();
            return new Result(true, "获取成功", nextCode);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "获取计划编号失败", null);
        }
    }

    @PostMapping("/fix-plan-codes")
    @ApiOperation("修复现有生产计划的编号")
    public Result fixPlanCodes() {
        try {
            int fixedCount = planService.fixExistingPlanCodes();
            return new Result(true, "修复成功，共修复 " + fixedCount + " 条记录", fixedCount);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, "修复失败: " + e.getMessage(), null);
        }
    }
}
