<template>
  <div class="p-4">
    <div class="mb-4 flex justify-between items-center">
      <el-button type="primary" @click="goToAdd">新建发票</el-button>
      <div class="flex gap-2">
        <el-input
          v-model="searchForm.code"
          placeholder="发票代码"
          clearable
          style="width: 160px"
        />
        <el-input
          v-model="searchForm.buyerName"
          placeholder="购方名称"
          clearable
          style="width: 160px"
        />
        <el-button type="primary" @click="getList">查询</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </div>

    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="code" label="发票代码" />
      <el-table-column prop="number" label="发票号码" />
      <el-table-column prop="buyerName" label="购方名称" />
      <el-table-column prop="sellerName" label="销方名称" />
      <el-table-column prop="amountInFiguers" label="金额（小写）" />
      <el-table-column prop="date" label="开票日期" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="openEdit(row)"
            >编辑</el-button
          >
          <el-button type="danger" size="small" @click="handleDelete(row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="mt-4"
      background
      layout="prev, pager, next"
      :total="paginationConfig.total"
      :page-size="paginationConfig.size"
      :current-page="paginationConfig.current"
      @current-change="handleCurrentChange"
    />

    <!-- 编辑弹窗保持不变 -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { fetchInvoiceList, deleteInvoice, updateInvoice } from "@/api/invoice";

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  code: "",
  buyerName: ""
});

// 分页配置
const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

interface TInvoice {
  id: string;
  code: string;
  number: string;
  date: string;
  checkCode: string;
  machineCode: string;
  buyerName: string;
  buyerTaxCode: string;
  buyerAddressPhone: string;
  buyerBankAccount: string;
  sellerName: string;
  sellerTaxCode: string;
  sellerAddressPhone: string;
}

// 表格数据 - ref方式
const tableData = ref<TInvoice[]>([]);
// 获取发票列表
const getList = () => {
  const params = {
    size: paginationConfig.size,
    current: paginationConfig.current,
    ...searchForm
  };

  fetchInvoiceList(params)
    .then(response => {
      console.log("获取发票列表成功:", response);
      if (response.success) {
        tableData.value = response.data.records;
        paginationConfig.total = response.data.total;
      } else {
        ElMessage.error(response.msg || "获取数据失败");
      }
    })
    .catch(error => {
      console.error("获取发票列表出错:", error);
      ElMessage.error("获取数据失败");
    });
};

// 分页变化
const handleCurrentChange = (current: number) => {
  paginationConfig.current = current;
  getList();
};

// 删除发票
const handleDelete = (id: string) => {
  ElMessageBox.confirm("确认删除该发票吗？", "提示", { type: "warning" })
    .then(() => {
      deleteInvoice(id).then(res => {
        if (res.success) {
          ElMessage.success("删除成功");
          getList();
        }
      });
    })
    .catch(() => {});
};

// 重置搜索
const resetSearch = () => {
  searchForm.code = "";
  searchForm.buyerName = "";
  getList();
};

// 其他方法保持不变
const editVisible = ref(false);
const editForm = reactive<any>({});

const openEdit = (row: any) => {
  Object.assign(editForm, row);
  editVisible.value = true;
};

const submitEdit = async () => {
  const res = await updateInvoice(editForm);
  if (res.success) {
    ElMessage.success("修改成功");
    editVisible.value = false;
    getList();
  }
};

const goToAdd = () => {
  router.push("/contract/tInvoice/add");
};

// 初始化加载数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-dialog__footer {
  text-align: right;
}
</style>
