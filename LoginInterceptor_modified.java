package com.geek.factory.interceptor;

import cn.hutool.core.util.StrUtil;
import com.geek.factory.exception.CustomException;
import com.geek.factory.utils.JwtHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @desc TODO
 * @date 2025/6/10
 *
 * 登录拦截器
 * 1.implements HandlerInterceptor接口，重写preHandle()，执行位置是在真正执行handler之前
 * 2.生效
 */
@Slf4j
public class LoginInterceptor implements HandlerInterceptor {

    /**
     * 真正执行handler之前
     * @param request  请求
     * @param response 响应
     * @param handler  真正的handler，包名.类名#方法名(参数列表)
     * @return true拦截器放行，false拦截器不放行
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("LoginInterceptor preHandle ~ handler = "+handler);

        //1.从请求头获取token
        String authorization = request.getHeader("Authorization");
        
        // 开发环境临时跳过认证验证
        log.info("LoginInterceptor preHandle ~ 开发环境跳过认证验证，直接放行");
        
        // TODO: 生产环境需要恢复以下认证逻辑
        /*
        //2.验证其是否为空
        if(StringUtils.isEmpty(authorization)){
            throw new CustomException("请求头中未携带Authorization");
        }
        String token = authorization.substring(7);
        log.info("LoginInterceptor preHandle ~ token = "+token);

        //3.验证其是否过期
        if(JwtHelper.isExpiration(token)){
            throw new CustomException("token已过期");
        }

        //4.验证其是否合法
        Long userId = JwtHelper.getUserId(token);
        if(userId==null){
            throw new CustomException("token不合法");
        }

        //..... 获取用户信息等等
        */

        //放行
        return true;
    }
}
