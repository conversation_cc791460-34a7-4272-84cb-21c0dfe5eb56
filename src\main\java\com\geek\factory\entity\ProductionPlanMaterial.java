package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产计划原料关联实体类
 */
@Data
@TableName("production_plan_material")
@ApiModel("生产计划原料关联")
public class ProductionPlanMaterial {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("生产计划ID")
    private Long planId;

    @ApiModelProperty("原料ID")
    private Long materialId;

    @ApiModelProperty("需要数量")
    private BigDecimal requiredQuantity;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
