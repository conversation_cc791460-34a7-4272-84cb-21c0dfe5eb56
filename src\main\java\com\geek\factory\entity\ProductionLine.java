package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 产线实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@TableName("production_line")
@ApiModel(value = "ProductionLine", description = "产线实体")
public class ProductionLine implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "产线ID")
    private Long id;

    @ApiModelProperty(value = "产线编码")
    private String code;

    @ApiModelProperty(value = "产线名称")
    private String name;

    @ApiModelProperty(value = "产线描述")
    private String description;

    @ApiModelProperty(value = "产线状态：0-停用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
