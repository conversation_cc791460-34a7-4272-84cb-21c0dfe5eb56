-- 清理生产计划表中的大数字ID记录
-- 只保留小于1000的正常ID记录

USE factory;

-- 1. 查看当前数据统计
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN id < 1000 THEN 1 END) as small_id_count,
    COUNT(CASE WHEN id >= 1000 THEN 1 END) as large_id_count,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 2. 显示大数字ID的记录
SELECT 
    id, 
    plan_code, 
    status, 
    create_time 
FROM production_plan 
WHERE id >= 1000
ORDER BY create_time DESC;

-- 3. 删除大数字ID的记录（保留小于1000的记录）
DELETE FROM production_plan WHERE id >= 1000;

-- 4. 验证清理结果
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 5. 显示剩余记录
SELECT 
    id, 
    plan_code, 
    status, 
    create_time 
FROM production_plan 
ORDER BY id;

SELECT '✅ 大数字ID记录清理完成！' as status;
