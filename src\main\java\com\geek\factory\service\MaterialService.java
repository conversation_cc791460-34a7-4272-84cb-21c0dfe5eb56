package com.geek.factory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.entity.Material;
import com.geek.factory.vo.MaterialQueryVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 原料服务接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
public interface MaterialService extends IService<Material> {

    /**
     * 分页查询原料列表
     * @param queryVO 查询条件
     * @return 原料分页列表
     */
    IPage<Material> getMaterialPage(MaterialQueryVO queryVO);

    /**
     * 根据ID查询原料详情
     * @param id 原料ID
     * @return 原料详情
     */
    Material getMaterialById(Integer id);

    /**
     * 新增原料
     * @param material 原料信息
     * @return 是否成功
     */
    boolean addMaterial(Material material);

    /**
     * 修改原料
     * @param material 原料信息
     * @return 是否成功
     */
    boolean updateMaterial(Material material);

    /**
     * 删除原料
     * @param id 原料ID
     * @return 是否成功
     */
    boolean deleteMaterial(Integer id);

    /**
     * 批量删除原料
     * @param ids 原料ID列表
     * @return 是否成功
     */
    boolean deleteMaterialBatch(List<Integer> ids);

    /**
     * 获取所有原料列表（不分页）
     * @return 原料列表
     */
    List<Material> getAllMaterials();

    /**
     * 根据类型查询原料列表
     * @param type 原料类型
     * @return 原料列表
     */
    List<Material> getMaterialsByType(String type);

    /**
     * 根据价格区间查询原料
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 原料列表
     */
    List<Material> getMaterialsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 获取原料类型统计
     * @return 类型统计
     */
    List<Map<String, Object>> getMaterialTypeStatistics();

    /**
     * 根据单位查询原料列表
     * @param unit 计量单位
     * @return 原料列表
     */
    List<Material> getMaterialsByUnit(String unit);

    /**
     * 验证原料数据
     * @param material 原料信息
     * @return 验证结果
     */
    boolean validateMaterial(Material material);
}
