package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.ProductionLine;
import com.geek.factory.mapper.ProductionLineMapper;
import com.geek.factory.service.ProductionLineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 产线服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class ProductionLineServiceImpl extends ServiceImpl<ProductionLineMapper, ProductionLine> implements ProductionLineService {

    @Override
    public List<ProductionLine> getAllProductionLines() {
        log.debug("获取所有产线列表");
        return baseMapper.selectList(null);
    }

    @Override
    public ProductionLine getProductionLineById(Long id) {
        log.debug("根据ID查询产线详情，ID：{}", id);
        
        if (id == null || id <= 0) {
            log.warn("产线ID无效：{}", id);
            return null;
        }
        
        return baseMapper.selectById(id);
    }
}
