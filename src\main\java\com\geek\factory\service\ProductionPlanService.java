package com.geek.factory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.dto.CreateProductionPlanDTO;
import com.geek.factory.dto.ProductionPlanMaterialDTO;
import com.geek.factory.entity.ProductionPlan;
import com.geek.factory.vo.ProductionPlanVO;

import java.util.List;
import java.util.Map;

/**
 * 生产计划服务接口
 */
public interface ProductionPlanService extends IService<ProductionPlan> {
    
    /**
     * 创建生产计划
     * 
     * @param dto 创建生产计划DTO
     */
    void createPlan(CreateProductionPlanDTO dto);
    
    /**
     * 更新生产计划
     *
     * @param plan 生产计划实体
     */
    void updatePlan(ProductionPlan plan);

    /**
     * 更新生产计划（包含原料信息）
     *
     * @param dto 更新生产计划DTO
     */
    void updatePlan(UpdateProductionPlanDTO dto);
    
    /**
     * 删除生产计划
     * 
     * @param id 生产计划ID
     */
    void deletePlan(Long id);
    
    /**
     * 分页查询生产计划，包含产品名称和产线名称
     * 
     * @param current 当前页
     * @param size 页面大小
     * @param params 查询参数
     * @return 生产计划VO分页结果
     */
    Page<ProductionPlanVO> getProductionPlanWithNames(Integer current, Integer size, Map<String, Object> params);
    
    /**
     * 根据ID查询生产计划详情，包含产品名称和产线名称
     *
     * @param id 生产计划ID
     * @return 生产计划VO
     */
    ProductionPlanVO getProductionPlanVOById(Long id);

    /**
     * 根据生产计划ID查询原料消耗详情
     *
     * @param planId 生产计划ID
     * @return 原料消耗详情列表
     */
    List<ProductionPlanMaterialDTO> getPlanMaterialDetails(Long planId);

    /**
     * 根据生产计划编码查询原料消耗详情
     *
     * @param planCode 生产计划编码
     * @return 原料消耗详情列表
     */
    List<ProductionPlanMaterialDTO> getPlanMaterialDetailsByCode(String planCode);

    /**
     * 修复现有生产计划的编号
     *
     * @return 修复的记录数量
     */
    int fixExistingPlanCodes();
}
