<template>
  <div class="device-repair-report">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card shadow="never" class="search-card">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="设备名称">
            <el-input
              v-model="searchForm.deviceName"
              placeholder="请输入设备名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item label="产线">
            <el-select
              v-model="searchForm.productLineId"
              placeholder="请选择产线"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="line in productionLineOptions"
                :key="line.id"
                :label="line.name"
                :value="line.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <template #icon>
                <component :is="useRenderIcon(Search)" />
              </template>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <template #icon>
                <component :is="useRenderIcon(Refresh)" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-card shadow="never" class="action-card">
        <el-button type="primary" @click="openAdd">
          <template #icon>
            <component :is="useRenderIcon(Plus)" />
          </template>
          新增维修上报
        </el-button>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card shadow="never" class="table-card">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          :header-cell-style="{ background: '#f8f9fa', color: '#606266', fontWeight: 'bold' }"
          :row-style="{ height: '60px' }"
          v-loading="loading"
        >
          <el-table-column prop="deviceName" label="设备名称" min-width="120" />
          <el-table-column prop="deviceType" label="设备类型" width="100" />
          <el-table-column prop="lineName" label="所属产线" width="120" />
          <el-table-column prop="description" label="故障描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getStatusTagType(row.status)"
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="submitter" label="提交人" width="100" />
          <el-table-column prop="submitTime" label="提交时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.submitTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="success"
                size="small"
                @click="openApproval(row)"
                link
                v-if="row.status === '待处理'"
              >
                <template #icon>
                  <component :is="useRenderIcon(Check)" />
                </template>
                审批
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="openEdit(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Edit)" />
                </template>
                编辑
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="viewDetail(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(View)" />
                </template>
                查看
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row.id)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Delete)" />
                </template>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <el-pagination
          v-model:current-page="paginationConfig.current"
          v-model:page-size="paginationConfig.size"
          :total="paginationConfig.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          style="margin-top: 20px; justify-content: center"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="设备名称" prop="deviceName">
          <el-select
            v-model="formData.deviceName"
            placeholder="请选择设备"
            style="width: 100%"
            @change="handleDeviceChange"
          >
            <el-option
              v-for="device in deviceOptions"
              :key="device.id"
              :label="device.device_name"
              :value="device.device_name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-input
            v-model="formData.deviceType"
            placeholder="选择设备后自动填充"
            readonly
          />
        </el-form-item>
        <el-form-item label="所属产线" prop="productLineId">
          <el-select
            v-model="formData.productLineId"
            placeholder="请选择产线"
            style="width: 100%"
          >
            <el-option
              v-for="line in productionLineOptions"
              :key="line.id"
              :label="line.name"
              :value="line.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="故障描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述设备故障情况"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option label="待处理" value="待处理" />
            <el-option label="处理中" value="处理中" />
            <el-option label="已完成" value="已完成" />
            <el-option label="已取消" value="已取消" />
          </el-select>
        </el-form-item>
        <el-form-item label="提交人" prop="submitter">
          <el-input
            v-model="formData.submitter"
            placeholder="请输入提交人"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="维修上报详情"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">
          {{ detailData.deviceName }}
        </el-descriptions-item>
        <el-descriptions-item label="设备类型">
          {{ detailData.deviceType }}
        </el-descriptions-item>
        <el-descriptions-item label="所属产线">
          {{ detailData.lineName }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTagType(detailData.status)" size="small">
            {{ detailData.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="提交人">
          {{ detailData.submitter }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatDateTime(detailData.submitTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="故障描述" :span="2">
          {{ detailData.description }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 审批对话框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      title="维修审批"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="approvalFormRef"
        :model="approvalFormData"
        :rules="approvalFormRules"
        label-width="100px"
      >
        <el-form-item label="报修单ID">
          <el-input v-model="approvalFormData.reportId" disabled />
        </el-form-item>
        <el-form-item label="审批人" prop="approver">
          <el-input
            v-model="approvalFormData.approver"
            placeholder="请输入审批人姓名"
          />
        </el-form-item>
        <el-form-item label="审批决定" prop="decision">
          <el-radio-group v-model="approvalFormData.decision">
            <el-radio value="approved">通过</el-radio>
            <el-radio value="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="approvalFormData.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审批意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeApprovalDialog">取消</el-button>
        <el-button type="primary" @click="handleApprovalSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import Edit from "@iconify-icons/ep/edit";
import Delete from "@iconify-icons/ep/delete";
import View from "@iconify-icons/ep/view";
import Check from "@iconify-icons/ep/check";

import {
  getDeviceRepairReportList,
  createDeviceRepairReport,
  updateDeviceRepairReport,
  getDeviceRepairReportById,
  deleteDeviceRepairReport,
  type DeviceRepairReport,
  type DeviceRepairReportQueryParams
} from "@/api/device";

import {
  approveRepairReport,
  type ApproveDTO
} from "@/api/approval";

// 搜索表单
const searchForm = reactive<DeviceRepairReportQueryParams>({
  deviceName: "",
  productLineId: undefined,
  submitter: ""
});

// 分页配置
const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

// 表格数据
const tableData = ref<DeviceRepairReport[]>([]);
const loading = ref(false);

// 设备选项类型
interface DeviceOption {
  id: number;
  device_name: string;
  device_type: string;
}

// 设备选项数据（根据数据库去重后的设备）
const deviceOptions = ref<DeviceOption[]>([
  { id: 1, device_name: "贴片机A1", device_type: "贴片机" },
  { id: 2, device_name: "回流焊B2", device_type: "回流焊" },
  { id: 3, device_name: "印刷机C3", device_type: "印刷机" },
  { id: 4, device_name: "AOI检测仪D4", device_type: "检测设备" },
  { id: 5, device_name: "贴片机A2", device_type: "贴片机" },
  { id: 6, device_name: "分板机E5", device_type: "分板设备" },
  { id: 7, device_name: "回流焊B1", device_type: "回流焊" },
  { id: 8, device_name: "点胶机F6", device_type: "点胶设备" },
  { id: 9, device_name: "贴片机A3", device_type: "贴片机" },
  { id: 10, device_name: "波峰焊G7", device_type: "波峰焊" },
  { id: 11, device_name: "印刷机C2", device_type: "印刷机" },
  { id: 12, device_name: "X-ray检测仪H8", device_type: "检测设备" },
  { id: 13, device_name: "贴片机A4", device_type: "贴片机" },
  { id: 14, device_name: "清洗机J9", device_type: "清洗设备" },
  { id: 15, device_name: "回流焊B3", device_type: "回流焊" }
]);

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref<DeviceRepairReport>({} as DeviceRepairReport);

// 审批对话框
const approvalDialogVisible = ref(false);
const approvalFormRef = ref<FormInstance>();
const approvalFormData = ref<ApproveDTO>({
  reportId: 0,
  approver: "",
  decision: "",
  comment: ""
});

// 审批表单验证规则
const approvalFormRules: FormRules = {
  approver: [{ required: true, message: "请输入审批人姓名", trigger: "blur" }],
  decision: [{ required: true, message: "请选择审批决定", trigger: "change" }],
  comment: [{ required: true, message: "请输入审批意见", trigger: "blur" }]
};

// 表单
const formRef = ref<FormInstance>();
const formData = ref<DeviceRepairReport>({
  deviceName: "",
  deviceType: "",
  productLineId: 0,
  description: "",
  status: "待处理",
  submitter: ""
});

// 产线选项类型
interface ProductionLineOption {
  id: number;
  name: string;
  code: string;
}

// 产线选项
const productionLineOptions = ref<ProductionLineOption[]>([
  { id: 1, name: "SMT贴片生产线", code: "PL-001" },
  { id: 2, name: "DIP插件生产线", code: "PL-002" },
  { id: 3, name: "组装测试线", code: "PL-003" },
  { id: 4, name: "包装生产线", code: "PL-004" },
  { id: 5, name: "返修检测线", code: "PL-005" }
]);

// 表单验证规则
const formRules: FormRules = {
  deviceName: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
  deviceType: [{ required: true, message: "请选择设备类型", trigger: "change" }],
  productLineId: [{ required: true, message: "请选择产线", trigger: "change" }],
  description: [{ required: true, message: "请输入故障描述", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  submitter: [{ required: true, message: "请输入提交人", trigger: "blur" }]
};

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      current: paginationConfig.current,
      size: paginationConfig.size,
      ...searchForm
    };
    console.log('调用API查询，参数:', params);
    const res = await getDeviceRepairReportList(params);

    if (res && res.success && res.data) {
      tableData.value = res.data.records || [];
      paginationConfig.total = res.data.total || 0;
      paginationConfig.current = res.data.current || 1;
      paginationConfig.size = res.data.size || 10;
    } else {
      tableData.value = [];
      paginationConfig.total = 0;
    }
  } catch (error) {
    console.error("获取列表失败:", error);
    ElMessage.error("获取列表失败");
    tableData.value = [];
    paginationConfig.total = 0;
  } finally {
    loading.value = false;
  }
};



// 分页变化
const handleCurrentChange = (page: number) => {
  paginationConfig.current = page;
  getList();
};

// 分页大小变化
const handleSizeChange = (size: number) => {
  paginationConfig.size = size;
  paginationConfig.current = 1;
  getList();
};

// 搜索
const handleSearch = () => {
  console.log('点击搜索按钮，查询条件:', searchForm);
  paginationConfig.current = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.deviceName = "";
  searchForm.productLineId = undefined;
  searchForm.submitter = "";
  paginationConfig.current = 1;
  getList();
};

// 打开新增对话框
const openAdd = () => {
  isEdit.value = false;
  dialogTitle.value = "新增维修上报";
  formData.value = {
    deviceName: "",
    deviceType: "",
    productLineId: 0,
    description: "",
    status: "待处理",
    submitter: ""
  };
  dialogVisible.value = true;
};

// 打开编辑对话框
const openEdit = async (row: DeviceRepairReport) => {
  isEdit.value = true;
  dialogTitle.value = "编辑维修上报";

  try {
    const res = await getDeviceRepairReportById(String(row.id));
    if (res && res.success && res.data) {
      formData.value = { ...res.data };
    } else {
      ElMessage.error("获取详情失败");
      return;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
    return;
  }

  dialogVisible.value = true;
};

// 查看详情
const viewDetail = async (row: DeviceRepairReport) => {
  try {
    const res = await getDeviceRepairReportById(String(row.id));
    if (res && res.success && res.data) {
      detailData.value = res.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage.error("获取详情失败");
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
  }
};

// 打开审批对话框
const openApproval = (row: DeviceRepairReport) => {
  approvalFormData.value = {
    reportId: Number(row.id),
    approver: "",
    decision: "",
    comment: ""
  };
  approvalDialogVisible.value = true;
};

// 关闭审批对话框
const closeApprovalDialog = () => {
  approvalDialogVisible.value = false;
  approvalFormRef.value?.resetFields();
};

// 提交审批
const handleApprovalSubmit = async () => {
  if (!approvalFormRef.value) return;

  try {
    await approvalFormRef.value.validate();

    const result = await approveRepairReport(approvalFormData.value);
    if (result && result.success) {
      ElMessage.success("审批成功");
      closeApprovalDialog();
      getList(); // 刷新列表
    } else {
      ElMessage.error(result?.msg || "审批失败");
    }
  } catch (error) {
    console.error('审批失败:', error);
    ElMessage.error("审批失败");
  }
};

// 删除
const handleDelete = (id: string) => {
  ElMessageBox.confirm("确认删除该维修上报记录吗？", "提示", { type: "warning" })
    .then(async () => {
      try {
        const result = await deleteDeviceRepairReport(id);
        if (result && result.success) {
          ElMessage.success("删除成功");
          getList();
        } else {
          ElMessage.error(result?.msg || "删除失败");
        }
      } catch (error) {
        console.error('删除失败:', error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (isEdit.value) {
      const result = await updateDeviceRepairReport(formData.value);
      if (result && result.success) {
        ElMessage.success("更新成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "更新失败");
      }
    } else {
      // 新增时设置提交时间为当前时间（ISO格式）
      const submitData = {
        ...formData.value,
        submitTime: new Date().toISOString().slice(0, 19)
      };

      const result = await createDeviceRepairReport(submitData);
      if (result && result.success) {
        ElMessage.success("新增成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "新增失败");
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error("提交失败");
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case "待处理":
      return "warning";
    case "处理中":
      return "primary";
    case "已完成":
      return "success";
    case "已取消":
      return "danger";
    default:
      return "info";
  }
};

// 格式化日期时间
const formatDateTime = (dateTime: string | null | undefined) => {
  if (!dateTime || dateTime === null || dateTime === undefined || dateTime === '') {
    return '-';
  }

  try {
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) {
      return '-';
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

// 处理设备选择变更
const handleDeviceChange = (deviceName: string) => {
  const selectedDevice = deviceOptions.value.find((device: DeviceOption) => device.device_name === deviceName);
  if (selectedDevice) {
    formData.value.deviceType = selectedDevice.device_type;
  }
};

// 生命周期
onMounted(() => {
  getList();
});
</script>

<style scoped>
.device-repair-report {
  padding: 20px;
}

.search-section,
.action-section,
.table-section {
  margin-bottom: 20px;
}

.search-card,
.action-card,
.table-card {
  border-radius: 8px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.action-card {
  padding: 16px 20px;
}

.table-card {
  padding: 20px;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #f8f9fa !important;
  color: #606266 !important;
  font-weight: bold !important;
}

.el-table td {
  padding: 12px 0;
}

.el-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-descriptions {
  margin-top: 20px;
}

.el-tag {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-repair-report {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 90% !important;
  }
}
</style>
