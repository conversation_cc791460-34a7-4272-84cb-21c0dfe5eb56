-- 为当前测试的生产计划添加原料消耗数据
-- 生产计划ID: 194183190042343010

-- 首先检查该生产计划是否存在
SELECT '=== 检查生产计划 194183190042343010 ===' as info;
SELECT id, plan_code, product_name, status, create_time 
FROM production_plan 
WHERE id = 194183190042343010;

-- 检查现有原料
SELECT '=== 检查可用原料 ===' as info;
SELECT id, code, name, unit, quantity 
FROM materials 
ORDER BY id 
LIMIT 10;

-- 为生产计划 194183190042343010 添加原料消耗记录
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time) VALUES
-- 使用前几个可用的原料ID，您可以根据实际情况调整
(194183190042343010, 1, 50.00, '个', '铝合金板 - 用于主板制造', NOW()),
(194183190042343010, 2, 25.50, 'kg', 'ABS塑料颗粒 - 用于外壳', NOW()),
(194183190042343010, 3, 15.20, 'kg', '环氧树脂 - 用于绝缘', NOW()),
(194183190042343010, 4, 100.00, '个', '锂离子电池 - 电源组件', NOW()),
(194183190042343010, 5, 200.00, '块', 'PCB电路板 - 核心组件', NOW());

-- 如果上面的原料ID不存在，使用动态插入
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT 194183190042343010, m.id, 
  CASE 
    WHEN m.id % 5 = 1 THEN 50.00
    WHEN m.id % 5 = 2 THEN 25.50  
    WHEN m.id % 5 = 3 THEN 15.20
    WHEN m.id % 5 = 4 THEN 100.00
    ELSE 30.00
  END as required_quantity,
  COALESCE(m.unit, '个') as unit,
  CONCAT(m.name, ' - 用于生产') as remark,
  NOW()
FROM materials m 
WHERE EXISTS (SELECT 1 FROM production_plan WHERE id = 194183190042343010)
LIMIT 5;

-- 验证插入结果
SELECT '=== 验证插入结果 ===' as info;
SELECT 
    ppm.plan_id,
    pp.plan_code,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 194183190042343010
ORDER BY ppm.material_id;

-- 测试API查询结果
SELECT '=== 模拟API查询结果 ===' as info;
SELECT 
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 194183190042343010
ORDER BY ppm.material_id;

SELECT '✅ 数据插入完成！请刷新原料消耗页面测试。' as status;
