import "@/utils/sso";
import Cookies from "js-cookie";
import { getConfig } from "@/config";
import NProgress from "@/utils/progress";
import { transformI18n } from "@/plugins/i18n";
import { buildHierarchyTree } from "@/utils/tree";
import remainingRouter from "./modules/remaining";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";

import { isUrl, openLink, storageLocal, isAllEmpty } from "@pureadmin/utils";
import {
  ascending,
  getTopMenu,
  initRouter,
  isOneOfArray,
  getHistoryMode,
  findRouteByPath,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes
} from "./utils";
import {
  type Router,
  createRouter,
  type RouteRecordRaw,
  type RouteComponent
} from "vue-router";
import {
  type DataInfo,
  userKey,
  removeToken,
  multipleTabsKey
} from "@/utils/auth";

// 自动导入路由模块
const modules: Record<string, any> = import.meta.glob(
  ["./modules/**/*.ts", "!./modules/**/remaining.ts"],
  { eager: true }
);



// 初始化路由
const routes = [];
Object.keys(modules).forEach(key => {
  const module = modules[key];
  if (module && module.default) {
    routes.push(module.default);
  }
});

// 导出处理后的路由
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
);

export const constantMenus: Array<RouteComponent> = ascending(
  routes.flat(Infinity)
);

export const remainingPaths = Object.keys(remainingRouter).map(v => remainingRouter[v].path);

// 创建路由实例
export const router: Router = createRouter({
  history: getHistoryMode(import.meta.env.VITE_ROUTER_HISTORY),
  routes: constantRoutes.concat(...(remainingRouter as any)),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else if (from.meta.saveSrollTop) {
        const top = document.documentElement.scrollTop || document.body.scrollTop;
        resolve({ left: 0, top });
      }
    });
  }
});

// 重置路由
export function resetRouter() {
  router.getRoutes().forEach(route => {
    const { name, meta } = route;
    if (name && router.hasRoute(name) && meta?.backstage) {
      router.removeRoute(name);
      router.options.routes = formatTwoStageRoutes(
        formatFlatteningRoutes(buildHierarchyTree(ascending(routes.flat(Infinity))))
      );
    }
  });
  usePermissionStoreHook().clearAllCachePage();
}

// 路由守卫
const whiteList = ["/login"];
router.beforeEach((to, _from, next) => {
  const userInfo = storageLocal().getItem<DataInfo<number>>(userKey);
  NProgress.start();

  if (Cookies.get(multipleTabsKey) && userInfo) {
    // 用户已登录
    if (to.path === "/login") {
      // 如果已登录用户访问登录页，重定向到首页
      next({ path: "/" });
      return;
    }

    // 检查权限
    if (to.meta?.roles && !isOneOfArray(to.meta?.roles, userInfo?.roles)) {
      next({ path: "/error/403" });
      return;
    }

    next();
  } else {
    // 用户未登录
    if (whiteList.includes(to.path)) {
      next();
    } else {
      next("/login");
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;