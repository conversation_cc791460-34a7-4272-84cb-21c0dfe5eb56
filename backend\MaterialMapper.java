package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.Material;
import com.geek.factory.vo.query.MaterialQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 原料Mapper接口
 */
@Mapper
public interface MaterialMapper extends BaseMapper<Material> {

    /**
     * 分页查询原料列表
     * @param page 分页对象
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<Material> selectMaterialPage(Page<Material> page, @Param("query") MaterialQueryVO queryVO);

    /**
     * 根据类型查询原料列表
     * @param type 原料类型
     * @return 原料列表
     */
    List<Material> selectByType(@Param("type") String type);

    /**
     * 根据价格区间查询原料
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 原料列表
     */
    List<Material> selectByPriceRange(@Param("minPrice") Double minPrice, @Param("maxPrice") Double maxPrice);

    /**
     * 统计各类型原料数量
     * @return 统计结果
     */
    List<Material> selectTypeStatistics();
}
