package com.geek.factory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.entity.Product;
import com.geek.factory.vo.ProductQueryVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 产品服务接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询产品列表
     * @param queryVO 查询条件
     * @return 产品分页列表
     */
    IPage<Product> getProductPage(ProductQueryVO queryVO);

    /**
     * 根据ID查询产品详情
     * @param id 产品ID
     * @return 产品详情
     */
    Product getProductById(Integer id);

    /**
     * 新增产品
     * @param product 产品信息
     * @return 是否成功
     */
    boolean addProduct(Product product);

    /**
     * 修改产品
     * @param product 产品信息
     * @return 是否成功
     */
    boolean updateProduct(Product product);

    /**
     * 删除产品
     * @param id 产品ID
     * @return 是否成功
     */
    boolean deleteProduct(Integer id);

    /**
     * 批量删除产品
     * @param ids 产品ID列表
     * @return 是否成功
     */
    boolean deleteProductBatch(List<Integer> ids);

    /**
     * 获取所有产品列表（不分页）
     * @return 产品列表
     */
    List<Product> getAllProducts();

    /**
     * 根据分类查询产品列表
     * @param category 产品分类
     * @return 产品列表
     */
    List<Product> getProductsByCategory(String category);

    /**
     * 根据价格区间查询产品
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 产品列表
     */
    List<Product> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * 获取产品分类统计
     * @return 分类统计
     */
    List<Map<String, Object>> getProductCategoryStatistics();

    /**
     * 验证产品数据
     * @param product 产品信息
     * @return 验证结果
     */
    boolean validateProduct(Product product);
}
