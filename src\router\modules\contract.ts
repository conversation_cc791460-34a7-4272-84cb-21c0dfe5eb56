import { $t } from "@/plugins/i18n";
import { contract } from "@/router/enums";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/contract",
  name: "Contract",
  component: Layout,
  redirect: "/contract/order/list",
  meta: {
    icon: "ri:file-text-line",
    title: $t("合同管理"),
    rank: contract
  },
  children: [
    // 订单管理
    {
      path: "/contract/order/list",
      name: "orderList",
      component: () => import("@/views/contract/order/list.vue"),
      meta: {
        title: $t("订单列表"),
        showParent: true
      }
    },
    {
      path: "/contract/order/add",
      name: "orderAdd",
      component: () => import("@/views/contract/order/add.vue"),
      meta: {
        title: $t("新增订单"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/contract/tInvoice/list",
      name: "invoiceList",
      component: () => import("@/views/contract/tInvoice/list.vue"),
      meta: {
        title: $t("发票列表"),
        showParent: true
      }
    },
    {
      path: "/contract/tInvoice/add",
      name: "invoiceAdd",
      component: () => import("@/views/contract/tInvoice/add.vue"),
      meta: {
        title: $t("新增发票"),
        showLink: false,
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable;
