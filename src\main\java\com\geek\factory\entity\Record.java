package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 维修记录实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("record")
@ApiModel(value = "Record对象", description = "维修记录")
public class Record implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    @ApiModelProperty(value = "记录ID")
    private Integer id;

    @TableField("device_id")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @TableField("device_name")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @TableField("productline_id")
    @ApiModelProperty(value = "产线ID")
    private String productlineId;

    @TableField("record_date")
    @ApiModelProperty(value = "记录日期")
    private String recordDate;

    @TableField("record_content")
    @ApiModelProperty(value = "维修内容")
    private String repairContent;

    @TableField("record_result")
    @ApiModelProperty(value = "维修结果")
    private String repairResult;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
