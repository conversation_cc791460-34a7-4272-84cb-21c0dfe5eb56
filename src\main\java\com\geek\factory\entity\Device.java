package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 设备实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@TableName("device")
@ApiModel(value = "Device", description = "设备实体")
public class Device implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "设备ID")
    private Integer id;

    @ApiModelProperty(value = "设备编码")
    private String code;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备类型")
    private String type;

    @TableField("productline_id")
    @ApiModelProperty(value = "所属产线ID")
    private Integer productlineId;

    @ApiModelProperty(value = "设备状态：0-停用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "设备描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
