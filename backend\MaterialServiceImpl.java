package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Material;
import com.geek.factory.mapper.MaterialMapper;
import com.geek.factory.service.MaterialService;
import com.geek.factory.vo.query.MaterialQueryVO;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @Description 原料服务实现类
 */
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    @Override
    public IPage<Material> selectMaterialPage(MaterialQueryVO queryVO) {
        Page<Material> page = new Page<>(queryVO.getCurrent(), queryVO.getSize());
        
        // 使用自定义SQL进行复杂查询
        if (hasComplexQuery(queryVO)) {
            return baseMapper.selectMaterialPage(page, queryVO);
        }
        
        // 使用QueryWrapper进行简单查询
        QueryWrapper<Material> queryWrapper = new QueryWrapper<>();
        
        // 原料名称模糊查询
        if (StringUtils.hasText(queryVO.getName())) {
            queryWrapper.like("name", queryVO.getName());
        }
        
        // 原料类型精确查询
        if (StringUtils.hasText(queryVO.getType())) {
            queryWrapper.eq("type", queryVO.getType());
        }
        
        // 计量单位精确查询
        if (StringUtils.hasText(queryVO.getUnit())) {
            queryWrapper.eq("unit", queryVO.getUnit());
        }
        
        // 价格区间查询
        if (queryVO.getMinPrice() != null) {
            queryWrapper.ge("price", queryVO.getMinPrice());
        }
        if (queryVO.getMaxPrice() != null) {
            queryWrapper.le("price", queryVO.getMaxPrice());
        }
        
        // 按ID降序排列
        queryWrapper.orderByDesc("id");
        
        return this.page(page, queryWrapper);
    }

    @Override
    public boolean addMaterial(Material material) {
        return this.save(material);
    }

    @Override
    public boolean updateMaterial(Material material) {
        return this.updateById(material);
    }

    @Override
    public boolean deleteMaterial(Integer id) {
        return this.removeById(id);
    }

    @Override
    public boolean deleteMaterialBatch(List<Integer> ids) {
        return this.removeByIds(ids);
    }

    @Override
    public List<Material> getMaterialsByType(String type) {
        return baseMapper.selectByType(type);
    }

    @Override
    public List<Material> getMaterialsByPriceRange(Double minPrice, Double maxPrice) {
        return baseMapper.selectByPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<Material> getMaterialTypeStatistics() {
        return baseMapper.selectTypeStatistics();
    }

    /**
     * 判断是否有复杂查询条件
     */
    private boolean hasComplexQuery(MaterialQueryVO queryVO) {
        return (queryVO.getMinPrice() != null && queryVO.getMaxPrice() != null) ||
               (StringUtils.hasText(queryVO.getName()) && StringUtils.hasText(queryVO.getType()));
    }
}
