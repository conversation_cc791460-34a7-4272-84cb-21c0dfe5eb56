package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.geek.factory.constant.MessageConstant;
import com.geek.factory.entity.Product;
import com.geek.factory.result.Result;
import com.geek.factory.service.ProductService;
import com.geek.factory.vo.query.ProductQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 产品管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/product")
@Api(tags = "产品管理")
@CrossOrigin
public class ProductController {

    @Autowired
    private ProductService productService;

    @ApiOperation("分页查询产品列表")
    @PostMapping("/page")
    public Result getProductPage(@RequestBody ProductQueryVO queryVO) {
        log.info("分页查询产品列表，查询条件：{}", queryVO);
        try {
            IPage<Product> page = productService.selectProductPage(queryVO);
            return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, page);
        } catch (Exception e) {
            log.error("分页查询产品列表失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("根据ID查询产品详情")
    @GetMapping("/{id}")
    public Result getProductById(@PathVariable Integer id) {
        log.info("根据ID查询产品详情，ID：{}", id);
        try {
            Product product = productService.getById(id);
            if (product != null) {
                return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, product);
            } else {
                return new Result(false, MessageConstant.PRODUCT_NOT_FOUND, null);
            }
        } catch (Exception e) {
            log.error("根据ID查询产品详情失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("新增产品")
    @PostMapping("/add")
    public Result addProduct(@RequestBody Product product) {
        log.info("新增产品，产品信息：{}", product);
        try {
            boolean success = productService.addProduct(product);
            if (success) {
                return new Result(true, MessageConstant.ADD_PRODUCT_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.ADD_PRODUCT_FAILED, null);
            }
        } catch (Exception e) {
            log.error("新增产品失败", e);
            return new Result(false, MessageConstant.ADD_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("更新产品")
    @PostMapping("/update")
    public Result updateProduct(@RequestBody Product product) {
        log.info("更新产品，产品信息：{}", product);
        try {
            boolean success = productService.updateProduct(product);
            if (success) {
                return new Result(true, MessageConstant.UPDATE_PRODUCT_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.UPDATE_PRODUCT_FAILED, null);
            }
        } catch (Exception e) {
            log.error("更新产品失败", e);
            return new Result(false, MessageConstant.UPDATE_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("删除产品")
    @DeleteMapping("/{id}")
    public Result deleteProduct(@PathVariable Integer id) {
        log.info("删除产品，ID：{}", id);
        try {
            boolean success = productService.deleteProduct(id);
            if (success) {
                return new Result(true, MessageConstant.DELETE_PRODUCT_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.DELETE_PRODUCT_FAILED, null);
            }
        } catch (Exception e) {
            log.error("删除产品失败", e);
            return new Result(false, MessageConstant.DELETE_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("批量删除产品")
    @DeleteMapping("/batch")
    public Result deleteProductBatch(@RequestBody List<Integer> ids) {
        log.info("批量删除产品，IDs：{}", ids);
        try {
            boolean success = productService.deleteProductBatch(ids);
            if (success) {
                return new Result(true, MessageConstant.DELETE_PRODUCT_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.DELETE_PRODUCT_FAILED, null);
            }
        } catch (Exception e) {
            log.error("批量删除产品失败", e);
            return new Result(false, MessageConstant.DELETE_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("根据分类查询产品列表")
    @GetMapping("/category/{category}")
    public Result getProductsByCategory(@PathVariable String category) {
        log.info("根据分类查询产品列表，分类：{}", category);
        try {
            List<Product> products = productService.getProductsByCategory(category);
            return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, products);
        } catch (Exception e) {
            log.error("根据分类查询产品列表失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("根据价格区间查询产品")
    @GetMapping("/price")
    public Result getProductsByPriceRange(@RequestParam Double minPrice, @RequestParam Double maxPrice) {
        log.info("根据价格区间查询产品，价格区间：{} - {}", minPrice, maxPrice);
        try {
            List<Product> products = productService.getProductsByPriceRange(minPrice, maxPrice);
            return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, products);
        } catch (Exception e) {
            log.error("根据价格区间查询产品失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }

    @ApiOperation("获取产品分类统计")
    @GetMapping("/statistics/category")
    public Result getProductCategoryStatistics() {
        log.info("获取产品分类统计");
        try {
            List<Product> statistics = productService.getProductCategoryStatistics();
            return new Result(true, MessageConstant.QUERY_PRODUCT_SUCCESS, statistics);
        } catch (Exception e) {
            log.error("获取产品分类统计失败", e);
            return new Result(false, MessageConstant.QUERY_PRODUCT_FAILED, null);
        }
    }
}
