package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.dto.MaterialUsageDTO;
import com.geek.factory.entity.Material;
import com.geek.factory.result.Result;
import com.geek.factory.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/material")
@RequiredArgsConstructor
@Api(tags = "原料库存管理")
public class MaterialStockController {

    @Autowired
    private MaterialService materialService;
    
    @PostMapping("/list")
    @ApiOperation("分页查询原料列表")
    public Result getMaterialList(@RequestBody(required = false) Map<String, Object> params) {
        int current = params != null && params.get("current") != null
                ? Integer.parseInt(params.get("current").toString()) : 1;
        int size = params != null && params.get("size") != null
                ? Integer.parseInt(params.get("size").toString()) : 10;
        String keyword = params != null && params.get("keyword") != null
                ? params.get("keyword").toString() : null;

        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(Material::getName, keyword)
                    .or().like(Material::getCode, keyword);
        }

        Page<Material> page = materialService.page(new Page<>(current, size), queryWrapper);

        return new Result(true, "查询成功", page);
    }

    @PostMapping("/stock")
    @ApiOperation("验证所需原料库存是否足够")
    public Result validateMaterialStock(@RequestBody List<MaterialUsageDTO> materials) {
        log.info("验证原料库存，原料数量：{}", materials.size());
        try {
            for (MaterialUsageDTO item : materials) {
                Material dbMaterial = materialService.getById(item.getMaterialId());
                if (dbMaterial == null) {
                    log.warn("原料不存在，ID：{}", item.getMaterialId());
                    return new Result(false, "原料不存在: ID=" + item.getMaterialId(), null);
                }
                if (dbMaterial.getQuantity().compareTo(item.getRequiredQuantity()) < 0) {
                    log.warn("原料库存不足，原料：{}，当前库存：{}，需要：{}", 
                            dbMaterial.getName(), dbMaterial.getQuantity(), item.getRequiredQuantity());
                    return new Result(false, "原料 " + dbMaterial.getName() + " 库存不足", null);
                }
            }
            log.info("库存验证通过");
            return new Result(true, "库存充足", null);
        } catch (Exception e) {
            log.error("库存验证失败", e);
            return new Result(false, "库存验证失败: " + e.getMessage(), null);
        }
    }

    @PostMapping("/deduct")
    @ApiOperation("扣减原料库存")
    @Transactional(rollbackFor = Exception.class)
    public Result deductMaterialStock(@RequestBody List<MaterialUsageDTO> materials) {
        log.info("开始扣减原料库存，原料数量：{}", materials.size());
        try {
            // 先验证库存是否充足
            for (MaterialUsageDTO item : materials) {
                Material dbMaterial = materialService.getById(item.getMaterialId());
                if (dbMaterial == null) {
                    log.warn("原料不存在，ID：{}", item.getMaterialId());
                    return new Result(false, "原料不存在: ID=" + item.getMaterialId(), null);
                }
                if (dbMaterial.getQuantity().compareTo(item.getRequiredQuantity()) < 0) {
                    log.warn("原料库存不足，无法扣减，原料：{}，当前库存：{}，需要：{}", 
                            dbMaterial.getName(), dbMaterial.getQuantity(), item.getRequiredQuantity());
                    return new Result(false, "原料 " + dbMaterial.getName() + " 库存不足，无法扣减", null);
                }
            }
            
            // 执行库存扣减
            for (MaterialUsageDTO item : materials) {
                Material dbMaterial = materialService.getById(item.getMaterialId());
                dbMaterial.setQuantity(dbMaterial.getQuantity().subtract(item.getRequiredQuantity()));
                boolean updateSuccess = materialService.updateById(dbMaterial);
                if (!updateSuccess) {
                    log.error("更新原料库存失败，原料ID：{}", item.getMaterialId());
                    throw new RuntimeException("更新原料库存失败");
                }
                log.info("成功扣减原料库存，原料：{}，扣减数量：{}，剩余库存：{}", 
                        dbMaterial.getName(), item.getRequiredQuantity(), dbMaterial.getQuantity());
            }
            
            log.info("库存扣减完成");
            return new Result(true, "库存扣减成功", null);
        } catch (Exception e) {
            log.error("库存扣减失败", e);
            return new Result(false, "库存扣减失败: " + e.getMessage(), null);
        }
    }
}
