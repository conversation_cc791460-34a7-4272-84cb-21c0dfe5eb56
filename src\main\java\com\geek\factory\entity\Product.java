package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 产品实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("products")
@ApiModel(value = "Product对象", description = "产品管理")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "产品名称", required = true)
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "产品型号", required = true)
    @TableField("model")
    private String model;

    @ApiModelProperty(value = "产品分类", required = true)
    @TableField("category")
    private String category;

    @ApiModelProperty(value = "产品单价", required = true)
    @TableField("price")
    private BigDecimal price;

    @ApiModelProperty(value = "产品描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "产品图片")
    @TableField("pic")
    private String pic;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
