package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.ProductionPlan;
import com.geek.factory.vo.ProductionPlanVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 生产计划Mapper接口
 */
@Mapper
public interface ProductionPlanMapper extends BaseMapper<ProductionPlan> {
    
    /**
     * 分页查询生产计划，包含产品名称和产线名称
     * 
     * @param page 分页参数
     * @param params 查询参数
     * @return 生产计划VO分页结果
     */
    @Select("<script>" +
            "SELECT " +
            "pp.id, " +
            "pp.plan_code, " +
            "pp.product_id, " +
            "COALESCE(p.product_name, p.name, '未知产品') as product_name, " +
            "pp.product_line_id, " +
            "COALESCE(pl.line_name, pl.name, '未知产线') as product_line_name, " +
            "pp.quantity, " +
            "pp.plan_start_time, " +
            "pp.complete_time, " +
            "pp.status, " +
            "pp.create_time, " +
            "pp.is_in_stock, " +
            "pp.priority, " +
            "pp.remarks " +
            "FROM production_plan pp " +
            "LEFT JOIN product p ON pp.product_id = p.id " +
            "LEFT JOIN production_line pl ON pp.product_line_id = pl.id " +
            "<where>" +
            "<if test='params != null'>" +
            "<if test='params.productId != null'>" +
            "AND pp.product_id = #{params.productId} " +
            "</if>" +
            "<if test='params.productLineId != null'>" +
            "AND pp.product_line_id = #{params.productLineId} " +
            "</if>" +
            "<if test='params.status != null and params.status != \"\"'>" +
            "AND pp.status = #{params.status} " +
            "</if>" +
            "<if test='params.planCode != null and params.planCode != \"\"'>" +
            "AND pp.plan_code LIKE CONCAT('%', #{params.planCode}, '%') " +
            "</if>" +
            "</if>" +
            "</where>" +
            "ORDER BY pp.create_time DESC" +
            "</script>")
    Page<ProductionPlanVO> selectProductionPlanWithNames(Page<ProductionPlanVO> page, @Param("params") Map<String, Object> params);
    
    /**
     * 根据ID查询生产计划详情，包含产品名称和产线名称
     * 
     * @param id 生产计划ID
     * @return 生产计划VO
     */
    @Select("SELECT " +
            "pp.id, " +
            "pp.plan_code, " +
            "pp.product_id, " +
            "COALESCE(p.product_name, p.name, '未知产品') as product_name, " +
            "pp.product_line_id, " +
            "COALESCE(pl.line_name, pl.name, '未知产线') as product_line_name, " +
            "pp.quantity, " +
            "pp.plan_start_time, " +
            "pp.complete_time, " +
            "pp.status, " +
            "pp.create_time, " +
            "pp.is_in_stock, " +
            "pp.priority, " +
            "pp.remarks " +
            "FROM production_plan pp " +
            "LEFT JOIN product p ON pp.product_id = p.id " +
            "LEFT JOIN production_line pl ON pp.product_line_id = pl.id " +
            "WHERE pp.id = #{id}")
    ProductionPlanVO selectProductionPlanVOById(@Param("id") Long id);

    /**
     * 获取指定日期前缀的最大序号
     *
     * @param prefix 日期前缀 (如: PLAN-20250707-)
     * @return 最大序号，如果没有记录则返回null
     */
    @Select("SELECT MAX(CAST(SUBSTRING(plan_code, #{prefixLength} + 1) AS UNSIGNED)) as max_seq " +
            "FROM production_plan " +
            "WHERE plan_code LIKE CONCAT(#{prefix}, '%') " +
            "AND LENGTH(plan_code) = #{prefixLength} + 2 " +
            "AND SUBSTRING(plan_code, #{prefixLength} + 1) REGEXP '^[0-9]+$'")
    Integer getMaxSequenceForDate(@Param("prefix") String prefix, @Param("prefixLength") int prefixLength);

    /**
     * 获取指定日期前缀的最大序号（简化版本）
     *
     * @param prefix 日期前缀 (如: PLAN-20250707-)
     * @return 最大序号，如果没有记录则返回null
     */
    @Select("SELECT MAX(CAST(SUBSTRING(plan_code, 14) AS UNSIGNED)) as max_seq " +
            "FROM production_plan " +
            "WHERE plan_code LIKE CONCAT(#{prefix}, '%') " +
            "AND LENGTH(plan_code) = 15 " +
            "AND SUBSTRING(plan_code, 14) REGEXP '^[0-9]+$'")
    Integer getMaxSequenceForDate(@Param("prefix") String prefix);

    /**
     * 根据生产计划ID获取计划编码
     *
     * @param productionPlanId 生产计划ID
     * @return 计划编码
     */
    @Select("SELECT plan_code FROM production_plan WHERE id = #{productionPlanId}")
    String getPlanCodeById(@Param("productionPlanId") Long productionPlanId);
}
