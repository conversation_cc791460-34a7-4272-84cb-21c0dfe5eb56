import { http } from "@/utils/http";

// 通用结果类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 设备维修审批日志DTO类型（匹配后端DeviceRepairApprovalLogDTO）
export interface DeviceRepairApprovalLogDTO {
  id?: number;
  reportId?: number;
  approver?: string;
  comment?: string;
  decision?: string;
  approvalTime?: string; // 前端使用字符串格式，后端LocalDateTime会自动转换
}

// 审批DTO类型
export interface ApproveDTO {
  reportId: number;
  approver: string;
  comment: string;
  decision: string;
}

// 审批查询参数
export interface ApprovalQueryParams {
  approver?: string;
  decision?: string;
}

// --- 设备维修审批 API ---

/** 获取审批日志列表 */
export const getApprovalLogList = () => {
  return http.request<Result<DeviceRepairApprovalLogDTO[]>>(
    "get",
    "/api/repair/approval/list"
  );
};

/** 根据ID获取审批日志详情 */
export const getApprovalLogById = (id: number) => {
  return http.request<Result<DeviceRepairApprovalLogDTO>>(
    "get",
    `/api/repair/approval/${id}`
  );
};

/** 新增审批日志 */
export const createApprovalLog = (data: DeviceRepairApprovalLogDTO) => {
  return http.request<Result>(
    "post",
    "/api/repair/approval/add",
    { data }
  );
};

/** 更新审批日志 */
export const updateApprovalLog = (data: DeviceRepairApprovalLogDTO) => {
  return http.request<Result>(
    "put",
    "/api/repair/approval/update",
    { data }
  );
};

/** 删除审批日志 */
export const deleteApprovalLog = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/repair/approval/delete/${id}`
  );
};

/** 执行审批操作 */
export const approveRepairReport = (data: ApproveDTO) => {
  return http.request<Result>(
    "post",
    "/api/repair/approval/approve",
    { data }
  );
};
