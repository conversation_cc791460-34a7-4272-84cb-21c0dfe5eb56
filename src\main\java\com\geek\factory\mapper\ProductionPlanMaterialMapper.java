package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geek.factory.dto.ProductionPlanMaterialDTO;
import com.geek.factory.entity.ProductionPlanMaterial;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 生产计划原料关联Mapper接口
 */
@Mapper
public interface ProductionPlanMaterialMapper extends BaseMapper<ProductionPlanMaterial> {

    /**
     * 根据生产计划ID查询原料消耗详情
     * 
     * @param planId 生产计划ID
     * @return 原料消耗详情列表
     */
    @Select("SELECT " +
            "ppm.material_id as materialId, " +
            "m.name as materialName, " +
            "ppm.unit, " +
            "ppm.required_quantity as requiredQuantity, " +
            "ppm.remark " +
            "FROM production_plan_material ppm " +
            "LEFT JOIN materials m ON ppm.material_id = m.id " +
            "WHERE ppm.plan_id = #{planId}")
    List<ProductionPlanMaterialDTO> selectPlanMaterialDetails(@Param("planId") Long planId);

    /**
     * 根据生产计划编码查询原料消耗详情
     *
     * @param planCode 生产计划编码
     * @return 原料消耗详情列表
     */
    @Select("SELECT " +
            "ppm.material_id as materialId, " +
            "m.name as materialName, " +
            "ppm.unit, " +
            "ppm.required_quantity as requiredQuantity, " +
            "ppm.remark " +
            "FROM production_plan_material ppm " +
            "LEFT JOIN materials m ON ppm.material_id = m.id " +
            "LEFT JOIN production_plan pp ON ppm.plan_id = pp.id " +
            "WHERE pp.plan_code = #{planCode}")
    List<ProductionPlanMaterialDTO> selectPlanMaterialDetailsByCode(@Param("planCode") String planCode);
}
