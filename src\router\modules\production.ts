import { $t } from "@/plugins/i18n";
import { production } from "@/router/enums";

const Layout = () => import("@/layout/index.vue");

export default {
  path: "/production",
  name: "Production",
  component: Layout,
  redirect: "/production/plan/list",
  meta: {
    icon: "ri:building-4-line",
    title: $t("生产管理"),
    rank: production,
  },
  children: [
    // 生产计划
    {
      path: "/production/plan/list",
      name: "productionPlanList",
      component: () => import("@/views/production/plan/list.vue"),
      meta: {
        title: $t("生产计划"),
        showParent: true
      }
    },
    {
      path: "/production/plan/add",
      name: "productionPlanAdd",
      component: () => import("@/views/production/plan/add.vue"),
      meta: {
        title: $t("新增生产计划"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/plan/edit/:id",
      name: "productionPlanEdit",
      component: () => import("@/views/production/plan/edit.vue"),
      meta: {
        title: $t("编辑生产计划"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/plan/material-consume/:id",
      name: "productionPlanMaterialConsume",
      component: () => import("@/views/production/plan/material-consume.vue"),
      meta: {
        title: $t("原料消耗详情"),
        showLink: false,
        showParent: true
      }
    },
    // 原料消耗界面 - 隐藏页面，只能通过生产计划页面访问
    {
      path: "/production/plan/material-consumption/:planCode",
      name: "materialConsumption",
      component: () => import("@/views/production/plan/material-consumption.vue"),
      meta: {
        title: $t("原料消耗"),
        showLink: false, // 隐藏菜单链接
        showParent: true
      }
    },
    // 生产明细 - 隐藏页面，只能通过生产计划页面访问
    {
      path: "/production/detail/list",
      name: "productionDetailList",
      component: () => import("@/views/production/detail/list.vue"),
      meta: {
        title: $t("生产明细"),
        showLink: false, // 不在菜单中显示
        showParent: false
      }
    },
    {
      path: "/production/detail/add",
      name: "productionDetailAdd",
      component: () => import("@/views/production/detail/add.vue"),
      meta: {
        title: $t("新增生产明细"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/detail/edit/:id",
      name: "productionDetailEdit",
      component: () => import("@/views/production/detail/edit.vue"),
      meta: {
        title: $t("编辑生产明细"),
        showLink: false,
        showParent: true
      }
    },
    // 排班管理
    {
      path: "/production/schedule/list",
      name: "productionScheduleList",
      component: () => import("@/views/production/schedule/list.vue"),
      meta: {
        title: $t("排班管理"),
        showParent: true
      }
    },
    {
      path: "/production/schedule/add",
      name: "productionScheduleAdd",
      component: () => import("@/views/production/schedule/add.vue"),
      meta: {
        title: $t("新增排班"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/schedule/edit/:id",
      name: "productionScheduleEdit",
      component: () => import("@/views/production/schedule/edit.vue"),
      meta: {
        title: $t("编辑排班"),
        showLink: false,
        showParent: true
      }
    },

    // 产品管理 - 在菜单中显示
    {
      path: "/production/product/list",
      name: "productionProductList",
      component: () => import("@/views/product/products/list.vue"),
      meta: {
        title: $t("产品管理"),
        showParent: true
      }
    },
    {
      path: "/production/product/add",
      name: "productionProductAdd",
      component: () => import("@/views/product/products/add.vue"),
      meta: {
        title: $t("新增产品"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/product/edit/:id",
      name: "productionProductEdit",
      component: () => import("@/views/product/products/edit.vue"),
      meta: {
        title: $t("编辑产品"),
        showLink: false,
        showParent: true
      }
    },
    {
      path: "/production/product/stock/:id",
      name: "productionProductStock",
      component: () => import("@/views/product/products/stock.vue"),
      meta: {
        title: $t("产品库存"),
        showLink: false,
        showParent: true
      }
    },

    // 原料管理 - 隐藏页面，只能通过生产计划页面访问
    {
      path: "/production/materials/list",
      name: "productionMaterialList",
      component: () => import("@/views/product/material/list.vue"),
      meta: {
        title: $t("原料管理"),
        showLink: false, // 不在菜单中显示
        showParent: true
      }
    },
    {
      path: "/production/materials/list/:planId",
      name: "productionMaterialListWithPlan",
      component: () => import("@/views/product/material/list.vue"),
      meta: {
        title: $t("生产计划原料"),
        showLink: false,
        showParent: true
      }
    }
  ]
} satisfies RouteConfigsTable; 