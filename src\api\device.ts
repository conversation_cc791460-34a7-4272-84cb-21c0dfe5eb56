import { http } from "@/utils/http";

// 通用结果类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 设备维修上报实体类型
export interface DeviceRepairReport {
  id?: string;
  deviceName: string;
  deviceType: string;
  productLineId: number;
  lineName?: string;
  description: string;
  status: string;
  submitter: string;
  submitTime?: string;
  processInstanceId?: string;
}

// 设备维修上报查询参数
export interface DeviceRepairReportQueryParams {
  current?: number;
  size?: number;
  deviceName?: string;
  productLineId?: number;
  submitter?: string;
}

// 分页结果类型
export interface PageResult<T> {
  records: T[];
  total: number;
  current: number;
  size: number;
}

// 产线类型
export interface ProductionLine {
  id: number | string;
  code: string;
  name: string;
}

// 设备类型
export interface Device {
  id?: number;
  code: string;
  name: string;
  type: string;
  productiveId: number;
  productionLineName?: string; // 关联的产线名称（用于显示）
}

/**
 * 获取设备维修上报列表（分页）
 */
export const getDeviceRepairReportList = (params?: DeviceRepairReportQueryParams) => {
  // 将前端的 current 参数映射为后端的 page 参数
  const backendParams = {
    page: params?.current || 1,
    size: params?.size || 10
  };

  // 如果有设备名称查询条件且不为空，使用专门的查询接口
  if (params?.deviceName && params.deviceName.trim() !== "") {
    console.log('使用设备名称查询接口:', params.deviceName);
    return http.request<Result<PageResult<DeviceRepairReport>>>(
      "get",
      "/device-repair-report/search-by-device",
      {
        params: {
          ...backendParams,
          deviceName: params.deviceName.trim()
        }
      }
    );
  }

  // 否则使用基本的分页接口
  // 注意：当前后端的 /page 接口不支持其他查询条件
  // 如果有其他查询条件，会在控制台显示警告
  if (params?.productLineId || params?.submitter) {
    console.warn('警告：当前后端不支持产线、提交人等查询条件，这些条件将被忽略');
  }

  console.log('使用基本分页接口');
  return http.request<Result<PageResult<DeviceRepairReport>>>(
    "get",
    "/device-repair-report/page",
    { params: backendParams }
  );
};

/**
 * 创建设备维修上报
 */
export const createDeviceRepairReport = (data: DeviceRepairReport) => {
  return http.request<Result<any>>(
    "post",
    "/device-repair-report/add",
    { data }
  );
};

/**
 * 更新设备维修上报
 */
export const updateDeviceRepairReport = (data: DeviceRepairReport) => {
  return http.request<Result<any>>(
    "put",
    "/device-repair-report/update",
    { data }
  );
};

/**
 * 根据ID获取设备维修上报详情
 */
export const getDeviceRepairReportById = (id: string) => {
  return http.request<Result<DeviceRepairReport>>(
    "get",
    `/device-repair-report/get/${id}`
  );
};

/**
 * 删除设备维修上报
 */
export const deleteDeviceRepairReport = (id: string) => {
  return http.request<Result<any>>(
    "delete",
    `/device-repair-report/delete/${id}`
  );
};

/**
 * 根据设备名称分页查询设备维修上报
 */
export const searchDeviceRepairReportByName = (params: {
  deviceName: string;
  page?: number;
  size?: number;
}) => {
  return http.request<Result<PageResult<DeviceRepairReport>>>(
    "get",
    "/device-repair-report/search-by-device",
    { params }
  );
};

// --- 基础数据 API ---

/** 获取产线列表 */
export const getProductionLineList = () => {
  return http.request<{ success: boolean; msg: string; data: ProductionLine[] }>(
    "get",
    "/api/production/line/all"
  );
};

/** 获取设备列表 */
export const getDeviceList = (productiveId?: number) => {
  const params = productiveId ? { productiveId } : {};
  return http.request<{ success: boolean; msg: string; data: Device[] }>(
    "get",
    "/api/device/list",
    { params }
  );
};


