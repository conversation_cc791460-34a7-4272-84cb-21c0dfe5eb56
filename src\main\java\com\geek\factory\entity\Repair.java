package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 设备维修实体类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@TableName("repair")
@ApiModel(value = "Repair", description = "设备维修实体")
public class Repair implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    @ApiModelProperty(value = "维修ID")
    private Integer id;

    @TableField("device_id")
    @ApiModelProperty(value = "设备ID")
    private Integer deviceId;

    @TableField("device_name")
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @TableField("productline_id")
    @ApiModelProperty(value = "产线ID")
    private Integer productlineId;

    @TableField("repair_person")
    @ApiModelProperty(value = "维修人员")
    private String repairPerson;

    @TableField("repair_date")
    @ApiModelProperty(value = "维修日期")
    private String repairDate;

    @TableField("status")
    @ApiModelProperty(value = "维修状态")
    private String status;

    @TableField("repair_content")
    @ApiModelProperty(value = "维修内容")
    private String repairContent;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
}
