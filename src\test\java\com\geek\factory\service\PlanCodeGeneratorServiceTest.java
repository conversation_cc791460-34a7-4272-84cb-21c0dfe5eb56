package com.geek.factory.service;

import com.geek.factory.mapper.ProductionPlanMapper;
import com.geek.factory.mapper.ProductionScheduleMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 计划编号生成服务测试类
 * 测试有序编号生成逻辑
 */
@ExtendWith(MockitoExtension.class)
class PlanCodeGeneratorServiceTest {

    @Mock
    private ProductionPlanMapper productionPlanMapper;

    @Mock
    private ProductionScheduleMapper productionScheduleMapper;

    @InjectMocks
    private PlanCodeGeneratorService planCodeGeneratorService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testGenerateProductionPlanCode_FirstOfDay() {
        // 模拟当天第一个计划
        when(productionPlanMapper.getMaxSequenceForDate(anyString())).thenReturn(null);

        String planCode = planCodeGeneratorService.generateProductionPlanCode();
        String today = LocalDate.now().format(DATE_FORMATTER);
        String expectedPrefix = "PLAN-" + today + "-";

        assertNotNull(planCode);
        assertTrue(planCode.startsWith(expectedPrefix));
        assertTrue(planCode.endsWith("-01"));
        assertEquals(15, planCode.length());
    }

    @Test
    void testGenerateProductionPlanCode_SecondOfDay() {
        // 模拟当天第二个计划
        when(productionPlanMapper.getMaxSequenceForDate(anyString())).thenReturn(1);

        String planCode = planCodeGeneratorService.generateProductionPlanCode();
        String today = LocalDate.now().format(DATE_FORMATTER);
        String expectedPrefix = "PLAN-" + today + "-";

        assertNotNull(planCode);
        assertTrue(planCode.startsWith(expectedPrefix));
        assertTrue(planCode.endsWith("-02"));
    }

    @Test
    void testGenerateScheduleCode_WithExistingPlans() {
        // 模拟已有生产计划和排班记录
        when(productionPlanMapper.getMaxSequenceForDate(anyString())).thenReturn(3);
        when(productionScheduleMapper.getMaxSequenceForDate(anyString())).thenReturn(2);

        String scheduleCode = planCodeGeneratorService.generateScheduleCode();
        String today = LocalDate.now().format(DATE_FORMATTER);
        String expectedPrefix = "PLAN-" + today + "-";

        assertNotNull(scheduleCode);
        assertTrue(scheduleCode.startsWith(expectedPrefix));
        assertTrue(scheduleCode.endsWith("-04")); // 取最大值3+1
    }

    @Test
    void testIsValidPlanCode() {
        String validCode = "PLAN-20250707-01";
        String invalidCode1 = "PLAN-20250707-1"; // 序号不是两位
        String invalidCode2 = "PLAN-2025070-01"; // 日期格式错误
        String invalidCode3 = "PLAN-20250707-AB"; // 序号不是数字

        assertTrue(planCodeGeneratorService.isValidPlanCode(validCode));
        assertFalse(planCodeGeneratorService.isValidPlanCode(invalidCode1));
        assertFalse(planCodeGeneratorService.isValidPlanCode(invalidCode2));
        assertFalse(planCodeGeneratorService.isValidPlanCode(invalidCode3));
        assertFalse(planCodeGeneratorService.isValidPlanCode(null));
    }

    @Test
    void testExtractDateFromCode() {
        String code = "PLAN-20250707-01";
        String extractedDate = planCodeGeneratorService.extractDateFromCode(code);
        assertEquals("20250707", extractedDate);

        String invalidCode = "INVALID-CODE";
        String result = planCodeGeneratorService.extractDateFromCode(invalidCode);
        assertNull(result);
    }

    @Test
    void testExtractSequenceFromCode() {
        String code = "PLAN-20250707-15";
        Integer sequence = planCodeGeneratorService.extractSequenceFromCode(code);
        assertEquals(15, sequence);

        String invalidCode = "PLAN-20250707-AB";
        Integer result = planCodeGeneratorService.extractSequenceFromCode(invalidCode);
        assertNull(result);
    }

    @Test
    void testConcurrentCodeGeneration() throws InterruptedException {
        // 测试并发生成编号的线程安全性
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        AtomicInteger sequence = new AtomicInteger(0);

        // 模拟并发场景
        when(productionPlanMapper.getMaxSequenceForDate(anyString()))
            .thenAnswer(invocation -> sequence.getAndIncrement());

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    String code = planCodeGeneratorService.generateProductionPlanCode();
                    assertNotNull(code);
                    assertTrue(planCodeGeneratorService.isValidPlanCode(code));
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();
    }

    @Test
    void testCodeFormatConsistency() {
        // 测试编号格式的一致性
        when(productionPlanMapper.getMaxSequenceForDate(anyString())).thenReturn(99);

        String planCode = planCodeGeneratorService.generateProductionPlanCode();
        
        // 验证格式
        assertTrue(planCode.matches("^PLAN-\\d{8}-\\d{2}$"));
        
        // 验证长度
        assertEquals(15, planCode.length());
        
        // 验证各部分
        assertEquals("PLAN", planCode.substring(0, 4));
        assertEquals("-", planCode.substring(4, 5));
        assertEquals("-", planCode.substring(13, 14));
        
        // 验证日期部分是今天
        String dateStr = planCode.substring(5, 13);
        assertEquals(LocalDate.now().format(DATE_FORMATTER), dateStr);
        
        // 验证序号部分
        String seqStr = planCode.substring(14);
        assertEquals("100", seqStr); // 99+1=100，但应该是两位数格式
    }
}
