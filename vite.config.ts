import { getPluginsList } from "./build/plugins";
import { include, exclude } from "./build/optimize";
import { type UserConfigExport, type ConfigEnv, loadEnv } from "vite";
import {
  root,
  alias,
  wrapperEnv,
  pathResolve,
  __APP_INFO__
} from "./build/utils";

export default ({ mode }: ConfigEnv): UserConfigExport => {
  const { VITE_CDN, VITE_PORT, VITE_COMPRESSION, VITE_PUBLIC_PATH } =
    wrapperEnv(loadEnv(mode, root));
  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias
    },
    // 服务端渲染
    server: {
      // 端口号
      port: VITE_PORT,
      host: "localhost",
      // 添加 CORS 配置
      cors: {
        origin: true, // 允许所有来源
        credentials: true // 允许携带凭证
      },
      // 本地跨域代理 https://cn.vitejs.dev/config/server-options.html#server-proxy
      proxy: {
        // 通过网关代理API请求
        "/api": {
          target: "http://localhost:80", // 网关端口
          changeOrigin: true, // 允许跨域
          // 不重写路径，保持/api前缀，让网关进行路由
        },
        // WebSocket代理
        "/ws": {
          target: "ws://localhost:80", // 网关端口，WebSocket协议
          ws: true, // 启用WebSocket代理
          changeOrigin: true // 允许跨域
        },
        // 文件服务代理
        "/file": {
          target: "http://localhost:80", // 网关端口
          changeOrigin: true // 允许跨域
        },
        // 保留原有的dev代理（如果需要直连后端调试）
        "/dev": {
          target: "http://localhost:8080",
          changeOrigin: true // 允许跨域
        }
      }
    },
    plugins: getPluginsList(VITE_CDN, VITE_COMPRESSION),
    // https://cn.vitejs.dev/config/dep-optimization-options.html#dep-optimization-options
    optimizeDeps: {
      include,
      exclude
    },
    build: {
      // https://cn.vitejs.dev/guide/build.html#browser-compatibility
      target: "es2015",
      sourcemap: false,
      // 消除打包大小超过500kb警告
      chunkSizeWarningLimit: 4000,
      rollupOptions: {
        input: {
          index: pathResolve("./index.html", import.meta.url)
        },
        // 静态资源分类打包
        output: {
          chunkFileNames: "static/js/[name]-[hash].js",
          entryFileNames: "static/js/[name]-[hash].js",
          assetFileNames: "static/[ext]/[name]-[hash].[ext]"
        }
      }
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__)
    }
  };
};



