package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.Product;
import com.geek.factory.vo.query.ProductQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 产品Mapper接口
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 分页查询产品列表
     * @param page 分页对象
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<Product> selectProductPage(Page<Product> page, @Param("query") ProductQueryVO queryVO);

    /**
     * 根据分类查询产品列表
     * @param category 产品分类
     * @return 产品列表
     */
    List<Product> selectByCategory(@Param("category") String category);

    /**
     * 根据价格区间查询产品
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 产品列表
     */
    List<Product> selectByPriceRange(@Param("minPrice") Double minPrice, @Param("maxPrice") Double maxPrice);

    /**
     * 统计各分类产品数量
     * @return 统计结果
     */
    List<Product> selectCategoryStatistics();
}
