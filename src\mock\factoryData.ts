import type { Product, Material, ProductMaterial, ProductWithMaterials, MaterialWithProducts } from '@/types/factory'

// 产品数据
export const mockProducts: Product[] = [
  {
    id: 1,
    productCode: 'P001',
    name: '智能手机',
    description: '高端智能手机',
    category: '电子产品',
    unit: '台',
    standardPrice: 2999,
    productionCycle: 24,
    qualityStandard: 'ISO 9001',
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 2,
    productCode: 'P002',
    name: '平板电脑',
    description: '轻薄平板电脑',
    category: '电子产品',
    unit: '台',
    standardPrice: 1999,
    productionCycle: 18,
    qualityStandard: 'ISO 9001',
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 3,
    productCode: 'P003',
    name: '智能手表',
    description: '运动智能手表',
    category: '电子产品',
    unit: '台',
    standardPrice: 1299,
    productionCycle: 12,
    qualityStandard: 'ISO 9001',
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 4,
    productCode: 'P004',
    name: '无线耳机',
    description: '降噪无线耳机',
    category: '电子产品',
    unit: '副',
    standardPrice: 599,
    productionCycle: 8,
    qualityStandard: 'ISO 9001',
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  }
]

// 原料数据
export const mockMaterials: Material[] = [
  {
    id: 1,
    materialCode: 'M001',
    name: '锂电池',
    description: '高容量锂电池',
    category: '电子元件',
    unit: '个',
    standardPrice: 50,
    supplier: '电池科技有限公司',
    minStock: 100,
    currentStock: 250,
    storageCondition: '常温干燥',
    shelfLife: 365,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 2,
    materialCode: 'M002',
    name: 'OLED屏幕',
    description: '高清OLED显示屏',
    category: '显示器件',
    unit: '块',
    standardPrice: 200,
    supplier: '显示技术股份公司',
    minStock: 50,
    currentStock: 120,
    storageCondition: '防静电环境',
    shelfLife: 730,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 3,
    materialCode: 'M003',
    name: '处理器芯片',
    description: '高性能处理器',
    category: '芯片',
    unit: '个',
    standardPrice: 300,
    supplier: '芯片制造有限公司',
    minStock: 30,
    currentStock: 80,
    storageCondition: '防静电环境',
    shelfLife: 1095,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 4,
    materialCode: 'M004',
    name: '铝合金外壳',
    description: '轻量化铝合金外壳',
    category: '结构件',
    unit: '个',
    standardPrice: 80,
    supplier: '金属加工厂',
    minStock: 200,
    currentStock: 350,
    storageCondition: '常温',
    shelfLife: null,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 5,
    materialCode: 'M005',
    name: '内存芯片',
    description: '高速内存芯片',
    category: '芯片',
    unit: '个',
    standardPrice: 120,
    supplier: '存储技术公司',
    minStock: 100,
    currentStock: 180,
    storageCondition: '防静电环境',
    shelfLife: 1095,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 6,
    materialCode: 'M006',
    name: '摄像头模组',
    description: '高像素摄像头',
    category: '光学器件',
    unit: '个',
    standardPrice: 150,
    supplier: '光学科技公司',
    minStock: 80,
    currentStock: 160,
    storageCondition: '防尘环境',
    shelfLife: 730,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 7,
    materialCode: 'M007',
    name: '扬声器',
    description: '高保真扬声器',
    category: '音频器件',
    unit: '个',
    standardPrice: 30,
    supplier: '音响设备公司',
    minStock: 150,
    currentStock: 300,
    storageCondition: '常温',
    shelfLife: null,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  },
  {
    id: 8,
    materialCode: 'M008',
    name: '传感器',
    description: '多功能传感器',
    category: '传感器',
    unit: '个',
    standardPrice: 40,
    supplier: '传感器技术公司',
    minStock: 100,
    currentStock: 220,
    storageCondition: '防静电环境',
    shelfLife: 1095,
    status: 'active',
    createdAt: '2024-01-01',
    updatedAt: '2024-01-01'
  }
]

// 产品原料关系数据（配方数据）
export const mockProductMaterials: ProductMaterial[] = [
  // 智能手机的配方
  { id: 1, productId: 1, materialId: 1, quantity: 1, unit: '个', isRequired: true, notes: '主电池', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 2, productId: 1, materialId: 2, quantity: 1, unit: '块', isRequired: true, notes: '主屏幕', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 3, productId: 1, materialId: 3, quantity: 1, unit: '个', isRequired: true, notes: '主处理器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 4, productId: 1, materialId: 4, quantity: 1, unit: '个', isRequired: true, notes: '机身外壳', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 5, productId: 1, materialId: 5, quantity: 2, unit: '个', isRequired: true, notes: '运行内存+存储', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 6, productId: 1, materialId: 6, quantity: 2, unit: '个', isRequired: true, notes: '前后摄像头', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 7, productId: 1, materialId: 7, quantity: 1, unit: '个', isRequired: true, notes: '听筒扬声器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 8, productId: 1, materialId: 8, quantity: 3, unit: '个', isRequired: true, notes: '重力+光线+距离传感器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  
  // 平板电脑的配方
  { id: 9, productId: 2, materialId: 1, quantity: 1, unit: '个', isRequired: true, notes: '主电池', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 10, productId: 2, materialId: 2, quantity: 1, unit: '块', isRequired: true, notes: '主屏幕', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 11, productId: 2, materialId: 3, quantity: 1, unit: '个', isRequired: true, notes: '主处理器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 12, productId: 2, materialId: 4, quantity: 1, unit: '个', isRequired: true, notes: '机身外壳', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 13, productId: 2, materialId: 5, quantity: 1, unit: '个', isRequired: true, notes: '运行内存', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 14, productId: 2, materialId: 6, quantity: 1, unit: '个', isRequired: true, notes: '后置摄像头', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 15, productId: 2, materialId: 7, quantity: 2, unit: '个', isRequired: true, notes: '立体声扬声器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  
  // 智能手表的配方
  { id: 16, productId: 3, materialId: 1, quantity: 1, unit: '个', isRequired: true, notes: '小型电池', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 17, productId: 3, materialId: 2, quantity: 1, unit: '块', isRequired: true, notes: '小屏幕', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 18, productId: 3, materialId: 3, quantity: 1, unit: '个', isRequired: true, notes: '低功耗处理器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 19, productId: 3, materialId: 4, quantity: 1, unit: '个', isRequired: true, notes: '表壳', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 20, productId: 3, materialId: 8, quantity: 5, unit: '个', isRequired: true, notes: '心率+运动+环境传感器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  
  // 无线耳机的配方
  { id: 21, productId: 4, materialId: 1, quantity: 2, unit: '个', isRequired: true, notes: '左右耳机电池', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 22, productId: 4, materialId: 4, quantity: 1, unit: '个', isRequired: true, notes: '充电盒外壳', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 23, productId: 4, materialId: 7, quantity: 2, unit: '个', isRequired: true, notes: '左右耳机扬声器', createdAt: '2024-01-01', updatedAt: '2024-01-01' },
  { id: 24, productId: 4, materialId: 8, quantity: 2, unit: '个', isRequired: true, notes: '触控传感器', createdAt: '2024-01-01', updatedAt: '2024-01-01' }
]
