<template>
  <div class="product-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            v-if="fromPage"
            type="default"
            @click="goBack"
            class="back-btn"
          >
            <component :is="useRenderIcon(ArrowLeft)" />
            返回{{ fromPageName }}
          </el-button>
          <div class="page-title-section">
            <component :is="useRenderIcon(Box)" class="title-icon" style="font-size: 32px;" />
            <div class="title-text">
              <h1 class="page-title">产品管理</h1>
              <p class="page-subtitle">Product Management</p>
            </div>
          </div>
          <el-tag v-if="highlightProduct" type="success" class="highlight-tag">
            <component :is="useRenderIcon(Star)" />
            当前查看: {{ highlightProduct }}
          </el-tag>
        </div>

        <div class="header-actions">
          <el-button type="primary" @click="handleAdd" size="large" class="add-btn">
            <template #icon>
              <component :is="useRenderIcon(Plus)" />
            </template>
            新增产品
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-card class="content-card">
        <!-- 搜索区域 -->
        <div class="search-section">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="产品编码">
              <el-input
                v-model="searchForm.code"
                placeholder="请输入产品编码"
                clearable
                prefix-icon="Barcode"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" class="search-btn">
                <template #icon>
                  <component :is="useRenderIcon(Search)" />
                </template>
                查询
              </el-button>
              <el-button @click="handleReset" class="reset-btn">
                <template #icon>
                  <component :is="useRenderIcon(RefreshLeft)" />
                </template>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格区域 -->
        <div class="table-section">
          <div class="table-header">
            <div class="table-title">
              <component :is="useRenderIcon(Grid)" class="table-icon" />
              <span>产品列表</span>
            </div>
            <div class="table-info">
              <el-tag type="info" size="small">
                共 {{ pagination.total }} 条记录
              </el-tag>
            </div>
          </div>

          <el-table
            :data="tableData"
            style="width: 100%"
            v-loading="loading"
            class="modern-table"
            :header-cell-style="{
              background: '#f8fafc',
              color: '#374151',
              fontWeight: '600',
              borderBottom: '2px solid #e5e7eb'
            }"
            :row-style="{ height: '60px' }"
            stripe
          >
            <el-table-column prop="code" label="产品编码" width="120" align="center">
              <template #default="{ row }">
                <el-tag type="info" size="small" round>{{ row.code || '未设置' }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="name" label="产品名称" min-width="180">
              <template #default="{ row }">
                <div class="product-info">
                  <div class="product-name">{{ row.name }}</div>
                  <div class="product-model">{{ row.model }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="pic" label="图片" width="100" align="center">
              <template #default="{ row }">
                <div class="image-cell">
                  <el-image
                    v-if="row.pic"
                    :src="row.pic"
                    class="product-image"
                    fit="cover"
                    :preview-src-list="[row.pic]"
                    :preview-teleported="true"
                    loading="lazy"
                  >
                    <template #error>
                      <div class="image-error">
                        <component :is="useRenderIcon(Picture)" />
                        <span>加载失败</span>
                      </div>
                    </template>
                  </el-image>
                  <div v-else class="no-image">
                    <component :is="useRenderIcon(Picture)" />
                    <span>无图片</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="completeTime" label="完成时间" width="160" align="center">
              <template #default="{ row }">
                <div v-if="row.completeTime" class="date-info">
                  <el-icon class="date-icon"><Calendar /></el-icon>
                  {{ formatDateTime(row.completeTime) }}
                </div>
                <span v-else class="text-gray-400">未完成</span>
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="formatStatusType(row.status)"
                  size="small"
                  round
                >
                  {{ formatStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="price" label="价格" width="120" align="center">
              <template #default="{ row }">
                <div class="price-cell">
                  <span class="price-symbol">¥</span>
                  <span class="price-value">{{ row.price.toFixed(2) }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip>
              <template #default="{ row }">
                <div class="description-cell">
                  {{ row.description || '暂无描述' }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right" align="center">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleEdit(row)"
                    class="action-btn edit-btn"
                    round
                  >
                    <template #icon>
                      <component :is="useRenderIcon(Edit)" />
                    </template>
                    编辑
                  </el-button>
                  <!-- 入库/查看入库按钮 -->
                  <el-button
                    v-if="!isProductInStock(row.isInStock)"
                    size="small"
                    type="success"
                    @click="handleInStock(row)"
                    class="action-btn stock-btn"
                    :disabled="!isProductCompleted(row.status)"
                    round
                  >
                    <template #icon>
                      <component :is="useRenderIcon(Box)" />
                    </template>
                    入库
                  </el-button>
                  <el-button
                    v-else
                    size="small"
                    type="info"
                    @click="handleViewStock(row)"
                    class="action-btn view-stock-btn"
                    round
                  >
                    <template #icon>
                      <component :is="useRenderIcon(View)" />
                    </template>
                    查看入库
                  </el-button>
                  <el-button
                    size="small"
                    type="warning"
                    @click="handleUpdateImage(row)"
                    class="action-btn image-btn"
                    round
                  >
                    <template #icon>
                      <component :is="useRenderIcon(Picture)" />
                    </template>
                    修改图片
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleDelete(row)"
                    class="action-btn delete-btn"
                    round
                  >
                    <template #icon>
                      <component :is="useRenderIcon(Delete)" />
                    </template>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section">
          <el-pagination
            background
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="modern-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 入库对话框 -->
    <el-dialog
      v-model="stockDialog.visible"
      title="产品入库"
      width="500px"
      :before-close="handleCloseStockDialog"
    >
      <el-form
        ref="stockFormRef"
        :model="stockDialog.form"
        :rules="stockDialog.rules"
        label-width="100px"
      >
        <el-form-item label="产品名称">
          <el-input v-model="stockDialog.form.productName" disabled />
        </el-form-item>
        <el-form-item label="产品编码">
          <el-input v-model="stockDialog.form.productCode" disabled />
        </el-form-item>
        <el-form-item label="入库数量" prop="quantity">
          <el-input-number
            v-model="stockDialog.form.quantity"
            :min="1"
            :max="9999"
            placeholder="请输入入库数量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="stockDialog.form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseStockDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmStock" :loading="stockDialog.loading">
            确认入库
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改图片对话框 -->
    <el-dialog
      v-model="imageDialog.visible"
      title="修改产品图片"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="image-upload-container">
        <!-- 当前图片预览 -->
        <div class="current-image">
          <h4>当前图片</h4>
          <el-image
            :src="imageDialog.currentImage"
            fit="cover"
            style="width: 200px; height: 200px; border-radius: 8px;"
            :preview-src-list="[imageDialog.currentImage]"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
                <span>暂无图片</span>
              </div>
            </template>
          </el-image>
        </div>

        <!-- 新图片上传 -->
        <div class="new-image">
          <h4>上传新图片</h4>
          <el-upload
            ref="uploadRef"
            :action="uploadAction"
            :headers="getUploadHeaders()"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            accept="image/*"
            drag
          >
            <div v-if="!imageDialog.newImage" class="upload-area">
              <el-icon class="upload-icon"><Plus /></el-icon>
              <div class="upload-text">
                <p>点击或拖拽图片到此处上传</p>
                <p class="upload-tip">支持 JPG、PNG、GIF 格式，文件大小不超过 5MB</p>
              </div>
            </div>
            <el-image
              v-else
              :src="imageDialog.newImage"
              fit="cover"
              style="width: 200px; height: 200px; border-radius: 8px;"
            />
          </el-upload>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseImageDialog">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmImageUpdate"
            :disabled="!imageDialog.newImage"
            :loading="imageDialog.loading"
          >
            确认修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import ArrowLeft from "@iconify-icons/ep/arrow-left";
import Box from "@iconify-icons/ep/box";
import Star from "@iconify-icons/ep/star";
import Plus from "@iconify-icons/ep/plus";
import Search from "@iconify-icons/ep/search";
import RefreshLeft from "@iconify-icons/ep/refresh-left";
import Grid from "@iconify-icons/ep/grid";
import Edit from "@iconify-icons/ep/edit";
import Connection from "@iconify-icons/ep/connection";
import Delete from "@iconify-icons/ep/delete";
import Picture from "@iconify-icons/ep/picture";
import UserFilled from "@iconify-icons/ep/user-filled";
import Cpu from "@iconify-icons/ep/cpu";
import Monitor from "@iconify-icons/ep/monitor";
import Lightning from "@iconify-icons/ep/lightning";
import Headphones from "@iconify-icons/ep/microphone";
import Charging from "@iconify-icons/ep/lightning";
import Watch from "@iconify-icons/ep/timer";
import Van from "@iconify-icons/ep/van";
import Calendar from "@iconify-icons/ep/calendar";
import View from "@iconify-icons/ep/view";
import {
  getAllProducts,
  getProductByCode,
  deleteProduct,
  productInStock,
  getProductStock,
  updateProduct,
  type Product,
  type ProductQueryVO,
  type ProductStock
} from '@/api/product'
import { http } from "@/utils/http"
import { getToken, formatToken } from "@/utils/auth"

defineOptions({
  name: 'ProductList'
})

// 路由
const router = useRouter()
const route = useRoute()

// 从路由参数中获取信息
const fromPage = computed(() => route.query.from)
const fromPageName = computed(() => {
  if (route.query.from === 'production-plan') {
    return '生产计划'
  }
  return ''
})
const highlightProduct = computed(() => route.query.productName)
const planId = computed(() => route.query.planId)

// 返回上一页
const goBack = () => {
  if (route.query.from === 'production-plan') {
    router.push('/production/plan/list')
  } else {
    router.go(-1)
  }
}

// 搜索表单
const searchForm = reactive({
  code: '', // 只保留产品编码查询
  current: 1,
  size: 10
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 入库对话框
const stockFormRef = ref()
const stockDialog = reactive({
  visible: false,
  loading: false,
  form: {
    productId: null,
    productName: '',
    productCode: '',
    quantity: 1,
    remark: ''
  },
  rules: {
    quantity: [
      { required: true, message: '请输入入库数量', trigger: 'blur' },
      { type: 'number', min: 1, message: '入库数量必须大于0', trigger: 'blur' }
    ]
  }
})

// 图片上传对话框
const uploadRef = ref()
const imageDialog = reactive({
  visible: false,
  loading: false,
  productId: null,
  productName: '',
  currentImage: '',
  newImage: ''
})

// 上传配置
const uploadAction = '/api/file/upload'

// 动态获取上传请求头（包含 Authorization）
const getUploadHeaders = () => {
  const tokenData = getToken()
  const headers: Record<string, string> = {}

  if (tokenData && tokenData.accessToken) {
    headers['Authorization'] = formatToken(tokenData.accessToken)
    console.log('上传请求头设置成功:', headers)
  } else {
    console.warn('未找到有效的 token，上传可能失败')
  }

  return headers
}

// 表格数据
const tableData = ref<Product[]>([])
const loading = ref(false)

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.code = '' // 只重置产品编码
  pagination.currentPage = 1
  loadData()
}



// 新增
const handleAdd = () => {
  router.push('/production/product/add')
}

// 编辑
const handleEdit = (row: Product) => {
  router.push(`/production/product/edit/${row.id}`)
}






// 产品入库
const handleInStock = (row: any) => {
  console.log('点击入库按钮，产品信息:', {
    id: row.id,
    name: row.name,
    isInStock: row.isInStock,
    stockQuantity: row.stockQuantity,
    status: row.status,
    statusType: typeof row.status
  })

  // 检查产品状态 - 支持字符串'1'和数字1两种情况
  if (!isProductCompleted(row.status)) {
    ElMessage.warning(`产品 ${row.name} 尚未完成，无法入库`)
    return
  }

  // 打开入库对话框
  stockDialog.form.productId = row.id // 保持原始类型
  stockDialog.form.productName = row.name
  stockDialog.form.productCode = row.code
  stockDialog.form.quantity = 1
  stockDialog.form.remark = ''
  stockDialog.visible = true
}

// 关闭入库对话框
const handleCloseStockDialog = () => {
  stockDialog.visible = false
  stockDialog.form.productId = null
  stockDialog.form.productName = ''
  stockDialog.form.productCode = ''
  stockDialog.form.quantity = 1
  stockDialog.form.remark = ''
  stockFormRef.value?.clearValidate()
}

// 确认入库
const handleConfirmStock = async () => {
  try {
    await stockFormRef.value?.validate()
    stockDialog.loading = true

    console.log('开始入库操作，原始数据:', {
      productId: stockDialog.form.productId,
      productIdType: typeof stockDialog.form.productId,
      quantity: stockDialog.form.quantity,
      quantityType: typeof stockDialog.form.quantity
    })

    // 调用入库API - 让API函数内部处理类型转换
    const response = await productInStock({
      productId: stockDialog.form.productId,
      quantity: stockDialog.form.quantity
    })

    console.log('入库API响应:', response)

    ElMessage.success(`产品 ${stockDialog.form.productName} 入库成功`)
    handleCloseStockDialog()

    // 立即更新当前产品的入库状态
    const targetProductId = Number(stockDialog.form.productId)
    console.log('查找产品进行更新:', {
      targetProductId,
      targetProductIdType: typeof targetProductId,
      tableDataLength: tableData.value.length,
      firstProductId: tableData.value[0]?.id,
      firstProductIdType: typeof tableData.value[0]?.id
    })

    const productIndex = tableData.value.findIndex(p => Number(p.id) === targetProductId)
    if (productIndex !== -1) {
      // 直接修改数组中的对象属性，Vue 3会检测到变化
      tableData.value[productIndex].isInStock = 1
      tableData.value[productIndex].stockQuantity = stockDialog.form.quantity

      console.log(`产品${targetProductId}入库状态已更新:`, {
        productIndex,
        isInStock: tableData.value[productIndex].isInStock,
        stockQuantity: tableData.value[productIndex].stockQuantity,
        产品信息: tableData.value[productIndex]
      })

      // 强制触发响应式更新
      nextTick(() => {
        console.log('nextTick后的产品状态:', tableData.value[productIndex])
      })
    } else {
      console.warn('未找到要更新的产品:', {
        查找的productId: targetProductId,
        所有产品ID: tableData.value.map(p => ({ id: p.id, type: typeof p.id }))
      })

      // 如果找不到产品，重新加载数据
      console.log('重新加载数据...')
      await loadData()
    }

    // 如果上面的更新方式不生效，可以重新加载数据（备用方案）
    // await loadData()
  } catch (error) {
    console.error('入库失败详细信息:', {
      error: error,
      message: error?.message,
      response: error?.response,
      status: error?.response?.status,
      data: error?.response?.data
    })

    // 显示更详细的错误信息
    let errorMessage = '入库失败，请重试'
    if (error?.response?.data?.msg) {
      errorMessage = error.response.data.msg
    } else if (error?.message) {
      errorMessage = error.message
    }

    ElMessage.error(errorMessage)
  } finally {
    stockDialog.loading = false
  }
}

// 查看库存
const handleViewStock = (row: any) => {
  router.push(`/production/product/stock/${row.id}`)
}

// 修改图片
const handleUpdateImage = (row: any) => {
  console.log('点击修改图片按钮，产品信息:', {
    id: row.id,
    name: row.name,
    currentImage: row.pic
  })

  imageDialog.productId = row.id
  imageDialog.productName = row.name
  imageDialog.currentImage = row.pic || ''
  imageDialog.newImage = ''
  imageDialog.visible = true
}

// 关闭图片对话框
const handleCloseImageDialog = () => {
  imageDialog.visible = false
  imageDialog.productId = null
  imageDialog.productName = ''
  imageDialog.currentImage = ''
  imageDialog.newImage = ''
  imageDialog.loading = false
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 上传成功回调
const handleUploadSuccess = (response: any) => {
  console.log('图片上传成功:', response)
  if (response.success) {
    imageDialog.newImage = response.data
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.msg || '图片上传失败')
  }
}

// 上传失败回调
const handleUploadError = (error: any) => {
  console.error('图片上传失败:', error)

  // 尝试解析错误信息
  let errorMessage = '图片上传失败，请重试'

  if (error.response) {
    // 服务器返回了错误响应
    try {
      const errorData = JSON.parse(error.response)
      if (errorData.msg) {
        errorMessage = errorData.msg
      }
    } catch (e) {
      // 解析失败，使用默认错误信息
    }
  } else if (error.message) {
    // 网络错误或其他错误
    errorMessage = error.message
  }

  ElMessage.error(errorMessage)
}

// 确认修改图片
const handleConfirmImageUpdate = async () => {
  if (!imageDialog.newImage) {
    ElMessage.warning('请先上传新图片')
    return
  }

  imageDialog.loading = true

  try {
    // 获取当前产品信息
    const productIndex = tableData.value.findIndex(p => p.id === imageDialog.productId)
    if (productIndex === -1) {
      ElMessage.error('产品信息不存在')
      return
    }

    const currentProduct = tableData.value[productIndex]

    // 构造更新数据（只更新图片字段）
    const updateData: Product = {
      id: imageDialog.productId,
      name: currentProduct.name,
      code: currentProduct.code,
      model: currentProduct.model || '',
      category: currentProduct.category || '',
      price: currentProduct.price || 0,
      pic: imageDialog.newImage, // 新图片URL
      completeTime: currentProduct.completeTime,
      status: currentProduct.status,
      description: currentProduct.description
    }

    console.log('准备更新产品图片，数据:', updateData)

    // 调用后端API更新产品
    const response = await updateProduct(updateData)
    console.log('后端API响应:', response)

    if (response.success) {
      // 更新前端显示
      tableData.value[productIndex].pic = imageDialog.newImage
      ElMessage.success(`产品 ${imageDialog.productName} 图片修改成功`)
      console.log(`产品${imageDialog.productId}图片已保存到数据库:`, imageDialog.newImage)
    } else {
      console.error('后端API返回失败:', response)
      ElMessage.error(response.msg || '图片更新失败')
    }
  } catch (error) {
    console.error('更新产品图片失败:', error)
    ElMessage.error('图片更新失败，请重试')
  } finally {
    imageDialog.loading = false
    handleCloseImageDialog()
  }
}

// 删除
const handleDelete = (row: Product) => {
  ElMessageBox.confirm('确定要删除这个产品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteProduct(row.id!)
      if (response.success) {
        ElMessage.success('删除成功')
        loadData() // 重新加载数据
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除产品失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  pagination.currentPage = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  loadData()
}

// 加载产品入库状态
const loadProductStockStatus = async (products: Product[]) => {
  for (const product of products) {
    try {
      const stockResponse = await getProductStock(product.id!)
      if (stockResponse.success && stockResponse.data) {
        // 产品已入库，同时确保状态为已完成
        product.isInStock = 1
        product.stockQuantity = stockResponse.data.quantity
        // 如果产品已入库，状态必须是已完成
        if (product.status !== 1) {
          product.status = 1
        }
      } else {
        // 产品未入库
        product.isInStock = 0
        product.stockQuantity = 0
      }
    } catch (error) {
      // 获取库存信息失败，默认为未入库
      console.log(`获取产品${product.id}库存信息失败:`, error)
      product.isInStock = 0
      product.stockQuantity = 0
    }
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    let response

    // 如果有产品编码查询条件，使用编码查询接口
    if (searchForm.code && searchForm.code.trim()) {
      console.log(`🔍 根据产品编码查询: ${searchForm.code}`)
      response = await getProductByCode(searchForm.code.trim())

      if (response.success && response.data) {
        // 单个产品查询成功，转换为数组格式
        const product = response.data

        // 添加默认图片
        if (!product.pic) {
          product.pic = 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop'
        }

        // 添加描述
        if (!product.description) {
          product.description = `${product.name}的详细描述信息`
        }

        const records = [product]
        await loadProductStockStatus(records)

        tableData.value = records
        pagination.total = records.length
        ElMessage.success(`找到产品编码为 "${searchForm.code}" 的产品`)
      } else {
        // 没有找到匹配的产品
        tableData.value = []
        pagination.total = 0
        ElMessage.warning(`未找到产品编码为 "${searchForm.code}" 的产品`)
      }
    } else {
      // 没有查询条件，获取所有产品
      console.log('📋 获取所有产品列表')
      response = await getAllProducts()

      if (response.success) {
        // 为没有图片的产品添加默认图片和状态
        const records = (response.data || []).map((item: Product, index: number) => {
          if (!item.pic) {
            // 使用电子产品相关的示例图片
            const defaultImages = [
              'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092160607-ee22621dd758?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092918056-0c4c3acd3789?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092921461-eab62e97a780?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092334651-ddf26d9a09d0?w=200&h=200&fit=crop',
              'https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=200&h=200&fit=crop'
            ]

            item.pic = defaultImages[index % defaultImages.length]
          }

          // 为产品添加模拟状态和描述（如果后端没有返回）
          if (item.status === undefined) {
            // 只有已入库的产品才设置为已完成状态，确保逻辑一致性
            item.status = item.isInStock === 1 ? 1 : 0
          }
          if (!item.description) {
            item.description = `${item.name}的详细描述信息`
          }



          return item
        })

        // 获取每个产品的入库状态
        await loadProductStockStatus(records)



        tableData.value = records
        pagination.total = records.length
        ElMessage.success(`成功加载 ${records.length} 个产品`)
      } else {
        ElMessage.error(response.msg || '查询失败')
        tableData.value = []
        pagination.total = 0
      }
    }
  } catch (error) {
    console.error('加载产品数据失败:', error)
    ElMessage.error('加载数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 获取分类图标
const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, any> = {
    '主板类': Cpu,
    '显示器件': Monitor,
    '电源类': Lightning,
    '音频设备': Headphones,
    '连接线': Connection,
    '充电设备': Charging,
    '可穿戴设备': Watch,
    '车载设备': Van
  }
  return iconMap[category] || Box
}

// 获取分类标签类型
const getCategoryTagType = (category: string): "primary" | "success" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "primary" | "success" | "warning" | "info" | "danger"> = {
    '主板类': 'primary',
    '显示器件': 'success',
    '电源类': 'warning',
    '音频设备': 'info',
    '连接线': 'primary',
    '充电设备': 'warning',
    '可穿戴设备': 'success',
    '车载设备': 'info'
  }
  return typeMap[category] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化状态文本
const formatStatusText = (status: string | number) => {
  // 只有数字1或字符串'1'才是已完成，其他都是未完成
  if (status === 1 || status === '1') {
    return '已完成'
  } else {
    return '未完成'
  }
}

// 格式化状态标签类型
const formatStatusType = (status: string | number) => {
  // 只有数字1或字符串'1'才是已完成(success)，其他都是未完成(warning)
  if (status === 1 || status === '1') {
    return 'success'
  } else {
    return 'warning'
  }
}

// 检查产品是否已完成
const isProductCompleted = (status: string | number) => {
  // 支持字符串'1'和数字1两种情况
  return status === 1 || status === '1'
}

// 检查产品是否已入库
const isProductInStock = (isInStock: string | number) => {
  // 支持字符串'1'和数字1两种情况
  return isInStock === 1 || isInStock === '1'
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.product-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  height: 40px;
  padding: 0 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.page-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  color: #667eea;
  font-size: 32px;
}

.title-text {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-weight: 400;
}

.highlight-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.add-btn {
  height: 44px;
  padding: 0 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 24px;
}

.content-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

/* 搜索区域样式 */
.search-section {
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 8px 8px 0 0;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  margin-right: 8px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.reset-btn {
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.reset-btn:hover {
  transform: translateY(-1px);
}

/* 表格区域样式 */
.table-section {
  padding: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.table-icon {
  color: #667eea;
  font-size: 20px;
}

.table-info {
  display: flex;
  gap: 12px;
}

.modern-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__header-wrapper) {
  border-radius: 12px 12px 0 0;
}

:deep(.el-table__body-wrapper) {
  border-radius: 0 0 12px 12px;
}

:deep(.el-table .el-table__cell) {
  border-bottom: 1px solid #f1f5f9;
  padding: 16px 12px;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
  background: #fafbfc;
}

/* 产品名称单元格 */
.product-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-avatar {
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.product-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.product-model {
  font-size: 12px;
  color: #6b7280;
}

/* 价格单元格 */
.price-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-weight: 600;
  color: #059669;
}

.price-symbol {
  font-size: 12px;
}

.price-value {
  font-size: 16px;
}

/* 描述单元格 */
.description-cell {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

/* 图片单元格 */
.image-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.product-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
  font-size: 12px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
  background: #fef0f0;
  padding: 8px;
  border-radius: 4px;
  width: 50px;
  height: 50px;
  justify-content: center;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.action-btn {
  border-radius: 16px;
  font-size: 11px;
  padding: 6px 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 70px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  width: 100%;
  max-width: 85px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.edit-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stock-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stock-btn:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

.stock-btn:disabled:hover {
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.view-stock-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.view-stock-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.image-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.image-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

.delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* 分页区域 */
.pagination-section {
  display: flex;
  justify-content: center;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.modern-pagination {
  border-radius: 8px;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

:deep(.el-pagination .el-pager li:hover) {
  transform: translateY(-1px);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: transparent;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .search-form {
    flex-direction: column;
    gap: 12px;
  }

  .search-section {
    padding: 12px 16px;
  }


}

@media (max-width: 768px) {
  .product-container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .product-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .table-section {
    padding: 16px;
  }

  .pagination-section {
    padding: 16px;
  }
}

/* 图片上传对话框样式 */
.image-upload-container {
  display: flex;
  justify-content: space-between;
  gap: 30px;
}

.image-upload-container .current-image,
.image-upload-container .new-image {
  flex: 1;
  text-align: center;
}

.image-upload-container .current-image h4,
.image-upload-container .new-image h4 {
  margin-bottom: 15px;
  color: #333;
  font-weight: 500;
}

.image-upload-container .image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  background: #f5f7fa;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  color: #909399;
}

.image-upload-container .image-slot .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.image-upload-container .image-slot span {
  font-size: 14px;
}

.image-upload-container .upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s;
}

.image-upload-container .upload-area:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.image-upload-container .upload-icon {
  font-size: 40px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.image-upload-container .upload-text {
  text-align: center;
}

.image-upload-container .upload-text p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.image-upload-container .upload-text p.upload-tip {
  color: #909399;
  font-size: 12px;
}

:deep(.el-upload-dragger) {
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

:deep(.el-upload-dragger:hover) {
  border-color: transparent !important;
}
</style>
