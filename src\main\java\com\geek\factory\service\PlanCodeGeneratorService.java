package com.geek.factory.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.geek.factory.entity.ProductionPlan;
import com.geek.factory.entity.ProductionSchedule;
import com.geek.factory.mapper.ProductionPlanMapper;
import com.geek.factory.mapper.ProductionScheduleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 计划编号生成服务
 * 实现有序的计划编号生成逻辑
 * 格式：PLAN-YYYYMMDD-XX
 * 特点：
 * 1. 编号有序，创建成功才递增，失败不递增
 * 2. 每天重新从01开始计数
 * 3. 线程安全，支持并发创建
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlanCodeGeneratorService {

    private final ProductionPlanMapper productionPlanMapper;
    private final ProductionScheduleMapper productionScheduleMapper;
    
    // 使用锁确保编号生成的线程安全
    private final ReentrantLock lock = new ReentrantLock();
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成生产计划编号
     * 格式：PLAN-YYYYMMDD-XX
     * 
     * @return 生成的计划编号
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateProductionPlanCode() {
        lock.lock();
        try {
            String today = LocalDate.now().format(DATE_FORMATTER);
            String prefix = "PLAN-" + today + "-";
            
            log.info("🔢 开始生成生产计划编号，日期前缀：{}", prefix);
            
            // 查询当天最大的序号
            Integer maxSequence = getMaxSequenceForDate(prefix, "production_plan", "plan_code");
            
            // 生成新的序号（从1开始）
            int newSequence = (maxSequence == null) ? 1 : maxSequence + 1;
            String planCode = prefix + String.format("%02d", newSequence);
            
            log.info("✅ 生成生产计划编号成功：{} (当天第{}个)", planCode, newSequence);
            return planCode;
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取下一个生产计划编号（预览，不实际生成）
     *
     * @return 下一个可用的计划编号
     */
    public String getNextProductionPlanCode() {
        String today = LocalDate.now().format(DATE_FORMATTER);
        String prefix = "PLAN-" + today + "-";

        // 查询当天最大的序号
        Integer maxSequence = getMaxSequenceForDate(prefix, "production_plan", "plan_code");

        // 生成新的序号（从1开始）
        int newSequence = (maxSequence == null) ? 1 : maxSequence + 1;
        return prefix + String.format("%02d", newSequence);
    }

    /**
     * 生成排班编号
     * 格式：PLAN-YYYYMMDD-XX (与生产计划使用相同格式)
     * 
     * @return 生成的排班编号
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateScheduleCode() {
        lock.lock();
        try {
            String today = LocalDate.now().format(DATE_FORMATTER);
            String prefix = "PLAN-" + today + "-";
            
            log.info("🔢 开始生成排班编号，日期前缀：{}", prefix);
            
            // 查询当天最大的序号（包括生产计划和排班）
            Integer maxSequenceFromPlan = getMaxSequenceForDate(prefix, "production_plan", "plan_code");
            Integer maxSequenceFromSchedule = getMaxSequenceForDate(prefix, "production_schedule", "schedule_code");
            
            // 取两个表中的最大序号
            Integer maxSequence = Math.max(
                maxSequenceFromPlan == null ? 0 : maxSequenceFromPlan,
                maxSequenceFromSchedule == null ? 0 : maxSequenceFromSchedule
            );
            
            // 生成新的序号
            int newSequence = maxSequence + 1;
            String scheduleCode = prefix + String.format("%02d", newSequence);
            
            log.info("✅ 生成排班编号成功：{} (当天第{}个)", scheduleCode, newSequence);
            return scheduleCode;
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取指定日期前缀的最大序号
     * 
     * @param prefix 日期前缀 (如: PLAN-20250707-)
     * @param tableName 表名
     * @param columnName 列名
     * @return 最大序号，如果没有记录则返回null
     */
    private Integer getMaxSequenceForDate(String prefix, String tableName, String columnName) {
        try {
            String sql = String.format(
                "SELECT MAX(CAST(SUBSTRING(%s, %d) AS UNSIGNED)) as max_seq " +
                "FROM %s " +
                "WHERE %s LIKE '%s%%' " +
                "AND LENGTH(%s) = %d " +
                "AND SUBSTRING(%s, %d) REGEXP '^[0-9]+$'",
                columnName, prefix.length() + 1,  // 从序号部分开始截取
                tableName,
                columnName, prefix,
                columnName, prefix.length() + 2,  // 完整编号长度
                columnName, prefix.length() + 1   // 序号部分开始位置
            );
            
            log.debug("🔍 查询SQL: {}", sql);
            
            if ("production_plan".equals(tableName)) {
                return productionPlanMapper.getMaxSequenceForDate(prefix);
            } else if ("production_schedule".equals(tableName)) {
                return productionScheduleMapper.getMaxSequenceForDate(prefix);
            }
            
            return null;
        } catch (Exception e) {
            log.error("❌ 查询最大序号失败，表：{}，前缀：{}", tableName, prefix, e);
            return null;
        }
    }

    /**
     * 验证编号格式是否正确
     * 
     * @param code 编号
     * @return 是否符合格式
     */
    public boolean isValidPlanCode(String code) {
        if (code == null || code.length() != 15) {
            return false;
        }
        
        // 格式：PLAN-YYYYMMDD-XX
        String pattern = "^PLAN-\\d{8}-\\d{2}$";
        return code.matches(pattern);
    }

    /**
     * 从编号中提取日期
     * 
     * @param code 编号
     * @return 日期字符串 (YYYYMMDD)
     */
    public String extractDateFromCode(String code) {
        if (!isValidPlanCode(code)) {
            return null;
        }
        return code.substring(5, 13); // 提取YYYYMMDD部分
    }

    /**
     * 从编号中提取序号
     * 
     * @param code 编号
     * @return 序号
     */
    public Integer extractSequenceFromCode(String code) {
        if (!isValidPlanCode(code)) {
            return null;
        }
        try {
            return Integer.parseInt(code.substring(14)); // 提取XX部分
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取下一个可用的生产计划编号（不实际生成）
     * 用于前端预览
     * 
     * @return 下一个可用的计划编号
     */
    public String getNextProductionPlanCode() {
        String today = LocalDate.now().format(DATE_FORMATTER);
        String prefix = "PLAN-" + today + "-";
        
        // 查询当天最大的序号
        Integer maxSequence = getMaxSequenceForDate(prefix, "production_plan", "plan_code");
        
        // 生成新的序号（从1开始）
        int newSequence = (maxSequence == null) ? 1 : maxSequence + 1;
        return prefix + String.format("%02d", newSequence);
    }
}

