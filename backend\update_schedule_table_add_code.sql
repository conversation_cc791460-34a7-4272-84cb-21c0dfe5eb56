-- 为生产排班表添加排班编号字段
-- 实现有序编号生成逻辑

USE factory;

-- 1. 检查当前production_schedule表结构
DESCRIBE production_schedule;

-- 2. 为production_schedule表添加schedule_code字段
ALTER TABLE production_schedule
ADD COLUMN schedule_code VARCHAR(50) NULL COMMENT '排班编号' AFTER id;

-- 3. 为现有数据生成排班编号
-- 按创建时间顺序为现有记录生成编号
SET @row_number = 0;
SET @current_date = '';

UPDATE production_schedule
SET schedule_code = (
    SELECT CONCAT('PLAN-', DATE_FORMAT(create_time, '%Y%m%d'), '-',
                  LPAD(
                      (@row_number := CASE
                          WHEN @current_date = DATE_FORMAT(create_time, '%Y%m%d')
                          THEN @row_number + 1
                          ELSE 1
                      END), 2, '0'
                  ),
                  (@current_date := DATE_FORMAT(create_time, '%Y%m%d'))
    )
    FROM (SELECT @row_number := 0, @current_date := '') AS vars
)
WHERE schedule_code IS NULL
ORDER BY create_time;

-- 4. 创建唯一索引确保编号不重复
ALTER TABLE production_schedule
ADD UNIQUE KEY uk_schedule_code (schedule_code);

-- 5. 验证结果
SELECT
    id,
    schedule_code,
    shift_name,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time
FROM production_schedule
ORDER BY create_time;

-- 6. 显示表结构
DESCRIBE production_schedule;

SELECT '✅ 生产排班表schedule_code字段添加完成！' as status;
SELECT '📋 编号格式：PLAN-YYYYMMDD-XX' as format_info;
SELECT '🔢 编号特点：有序、按日期分组、从01开始计数' as features;
