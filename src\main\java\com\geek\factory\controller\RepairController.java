package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.constant.SuccessConstant;
import com.geek.factory.entity.Repair;
import com.geek.factory.result.Result;
import com.geek.factory.service.RepairService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description 设备维修控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/dev/repair")
@Api(tags = "设备维修管理")
@CrossOrigin
public class RepairController {

    @Autowired
    private RepairService repairService;

    /**
     * 分页查询设备维修列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询设备维修列表")
    public Result getRepairList(@RequestBody Map<String, Object> params) {
        log.info("分页查询维修记录 - 参数: {}", params);
        try {
            // 获取分页参数
            Long current = params.get("current") != null ? Long.valueOf(params.get("current").toString()) : 1L;
            Long size = params.get("size") != null ? Long.valueOf(params.get("size").toString()) : 10L;

            Page<Repair> page = new Page<>(current, size);
            LambdaQueryWrapper<Repair> wrapper = new LambdaQueryWrapper<>();

            // 明确指定要查询的字段，确保包含id字段
            wrapper.select(Repair::getId, Repair::getDeviceId, Repair::getDeviceName,
                          Repair::getRepairPerson, Repair::getRepairDate, Repair::getStatus,
                          Repair::getRepairContent, Repair::getProductlineId);

            // 添加查询条件
            if (params.get("deviceName") != null && !params.get("deviceName").toString().trim().isEmpty()) {
                wrapper.like(Repair::getDeviceName, params.get("deviceName").toString());
            }
            if (params.get("repairPerson") != null && !params.get("repairPerson").toString().trim().isEmpty()) {
                wrapper.like(Repair::getRepairPerson, params.get("repairPerson").toString());
            }
            if (params.get("status") != null && !params.get("status").toString().trim().isEmpty()) {
                wrapper.eq(Repair::getStatus, params.get("status").toString());
            }
            if (params.get("productlineId") != null) {
                wrapper.eq(Repair::getProductlineId, Integer.valueOf(params.get("productlineId").toString()));
            }

            // 按ID降序排列（如果没有createTime字段）
            wrapper.orderByDesc(Repair::getId);

            Page<Repair> result = repairService.page(page, wrapper);
            log.info("分页查询维修记录成功，共{}条记录", result.getTotal());
            return new Result(SuccessConstant.SUCCESS, "查询成功", result);
        } catch (Exception e) {
            log.error("分页查询维修记录失败", e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 根据ID查询设备维修
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询设备维修")
    public Result getRepairById(@PathVariable Integer id) {
        log.info("根据ID查询设备维修，ID：{}", id);
        try {
            Repair repair = repairService.getById(id);
            if (repair != null) {
                log.info("查询设备维修成功");
                return new Result(SuccessConstant.SUCCESS, "查询成功", repair);
            } else {
                log.warn("设备维修记录不存在，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "记录不存在", null);
            }
        } catch (Exception e) {
            log.error("查询设备维修失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 新增设备维修
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增设备维修")
    public Result createRepair(@RequestBody Repair repair) {
        log.info("新增设备维修，参数：{}", repair);
        try {
            boolean success = repairService.save(repair);
            if (success) {
                log.info("新增设备维修成功");
                return new Result(SuccessConstant.SUCCESS, "新增成功", null);
            } else {
                log.warn("新增设备维修失败");
                return new Result(SuccessConstant.FAILED, "新增失败", null);
            }
        } catch (Exception e) {
            log.error("新增设备维修失败", e);
            return new Result(SuccessConstant.FAILED, "新增失败", null);
        }
    }

    /**
     * 更新设备维修
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新设备维修")
    public Result updateRepair(@RequestBody Repair repair) {
        log.info("更新设备维修，参数：{}", repair);
        try {
            boolean success = repairService.updateById(repair);
            if (success) {
                log.info("更新设备维修成功，ID：{}", repair.getId());
                return new Result(SuccessConstant.SUCCESS, "更新成功", null);
            } else {
                log.warn("更新设备维修失败");
                return new Result(SuccessConstant.FAILED, "更新失败", null);
            }
        } catch (Exception e) {
            log.error("更新设备维修失败", e);
            return new Result(SuccessConstant.FAILED, "更新失败", null);
        }
    }

    /**
     * 删除设备维修
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除设备维修")
    public Result deleteRepair(@PathVariable Integer id) {
        log.info("删除设备维修，ID：{}", id);
        try {
            boolean success = repairService.removeById(id);
            if (success) {
                log.info("删除设备维修成功，ID：{}", id);
                return new Result(SuccessConstant.SUCCESS, "删除成功", null);
            } else {
                log.warn("删除设备维修失败，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "删除失败", null);
            }
        } catch (Exception e) {
            log.error("删除设备维修失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "删除失败", null);
        }
    }

    /**
     * 查询请求参数类
     */
    public static class RepairQueryRequest {
        private Long current = 1L;
        private Long size = 10L;
        private String deviceName;
        private String repairPerson;
        private String status;
        private Integer productlineId;

        // Getters and Setters
        public Long getCurrent() { return current; }
        public void setCurrent(Long current) { this.current = current; }
        public Long getSize() { return size; }
        public void setSize(Long size) { this.size = size; }
        public String getDeviceName() { return deviceName; }
        public void setDeviceName(String deviceName) { this.deviceName = deviceName; }
        public String getRepairPerson() { return repairPerson; }
        public void setRepairPerson(String repairPerson) { this.repairPerson = repairPerson; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public Integer getProductlineId() { return productlineId; }
        public void setProductlineId(Integer productlineId) { this.productlineId = productlineId; }
        
        @Override
        public String toString() {
            return "RepairQueryRequest{" +
                    "current=" + current +
                    ", size=" + size +
                    ", deviceName='" + deviceName + '\'' +
                    ", repairPerson='" + repairPerson + '\'' +
                    ", status='" + status + '\'' +
                    ", productlineId=" + productlineId +
                    '}';
        }
    }
}
