package com.geek.factory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.dto.DeviceRepairLogDTO;
import com.geek.factory.entity.DeviceRepairLog;

import java.util.List;

/**
 * @Description 设备维修记录日志服务接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
public interface DeviceRepairLogService extends IService<DeviceRepairLog> {

    /**
     * 获取所有维修记录日志（包含产线名称）
     */
    List<DeviceRepairLogDTO> getAllWithLineName();
}
