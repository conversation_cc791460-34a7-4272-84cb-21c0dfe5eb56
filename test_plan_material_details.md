# 生产计划原料消耗详情功能测试指南

## 🎯 新增功能说明

根据用户要求，新增了**根据生产计划ID查询原料消耗详情**的功能，包括：

### 后端实现
1. **新增API端点**: `GET /api/plan/materials/{planId}`
2. **新增DTO**: `ProductionPlanMaterialDTO`
3. **新增数据库表**: `production_plan_materials`
4. **新增实体类**: `ProductionPlanMaterial`
5. **新增Mapper**: `ProductionPlanMaterialMapper`

### 前端实现
1. **更新API调用**: `getPlanMaterials(planId)`
2. **更新页面**: `material-consume.vue`

## 📊 数据库结构

### 生产计划原料关联表
```sql
CREATE TABLE `production_plan_materials` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '生产计划ID',
  `material_id` bigint NOT NULL COMMENT '原料ID',
  `required_quantity` decimal(10,2) NOT NULL COMMENT '需要数量',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_material_id` (`material_id`)
);
```

## 🔧 后端API详情

### 请求
```
GET /api/plan/materials/{planId}
```

### 响应格式
```json
{
  "success": true,
  "msg": "查询成功",
  "data": [
    {
      "materialId": 1,
      "materialName": "陶瓷电容 10μF 50V",
      "unit": "个",
      "requiredQuantity": 20.00,
      "remark": "高精度电容"
    }
  ]
}
```

## 🧪 测试步骤

### 1. 数据库准备
```bash
# 执行数据库更新脚本
mysql -u root -p factory < backend/update_material_stock.sql
```

### 2. 创建测试数据
1. 启动后端服务
2. 创建一个生产计划（包含原料）
3. 系统会自动在 `production_plan_materials` 表中保存原料关联信息

### 3. 测试API
```bash
# 测试获取生产计划原料详情
curl -X GET "http://localhost:8080/api/plan/materials/1" \
  -H "Content-Type: application/json"
```

### 4. 前端测试
1. 打开生产计划列表页面
2. 点击某个生产计划的"原料消耗"按钮
3. 查看原料消耗详情页面是否正确显示数据

## 🔄 完整工作流程

### 创建生产计划时
1. 用户填写生产计划信息
2. 选择需要的原料和数量
3. 提交创建请求
4. 系统执行以下操作：
   - 保存生产计划基本信息
   - 扣减原料库存
   - **新增**: 保存原料关联信息到 `production_plan_materials` 表

### 查看原料消耗时
1. 用户点击"原料消耗"按钮
2. 前端调用 `getPlanMaterials(planId)` API
3. 后端从 `production_plan_materials` 表查询数据
4. 返回原料消耗详情列表
5. 前端显示图表和表格

## 📝 代码关键点

### 后端关键代码

<augment_code_snippet path="src/main/java/com/geek/factory/controller/ProductionPlanController.java" mode="EXCERPT">
```java
@GetMapping("/materials/{planId}")
@ApiOperation("根据生产计划ID查询原料消耗详情")
public Result getPlanMaterials(@PathVariable Long planId) {
    List<ProductionPlanMaterialDTO> materials = planService.getPlanMaterialDetails(planId);
    return new Result(true, "查询成功", materials);
}
```
</augment_code_snippet>

### 前端关键代码

<augment_code_snippet path="src/api/production.ts" mode="EXCERPT">
```typescript
export const getPlanMaterials = (planId: number) => {
  return http.request<Result<ProductionPlanMaterialDTO[]>>(
    "get",
    `/api/plan/materials/${planId}`
  );
};
```
</augment_code_snippet>

## 🚀 预期结果

### 成功场景
- API返回正确的原料消耗详情数据
- 前端页面显示原料名称、数量、单位等信息
- 图表正确渲染原料消耗分布

### 数据示例
```json
[
  {
    "materialId": 16,
    "materialName": "陶瓷电容 10μF 50V",
    "unit": "个",
    "requiredQuantity": 20.00,
    "remark": "高精度电容"
  },
  {
    "materialId": 17,
    "materialName": "铝电解电容 100μF 25V",
    "unit": "个", 
    "requiredQuantity": 15.00,
    "remark": "电源滤波电容"
  }
]
```

## 🔧 故障排除

### 常见问题
1. **数据库表不存在**: 确保执行了 `update_material_stock.sql`
2. **API返回空数据**: 检查是否有生产计划关联的原料数据
3. **前端显示异常**: 检查API路径是否正确 (`/api/plan/materials/{planId}`)

### 调试建议
1. 检查后端日志中的SQL执行情况
2. 验证数据库中 `production_plan_materials` 表的数据
3. 使用浏览器开发者工具检查API请求和响应
