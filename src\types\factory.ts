// 工厂管理系统的数据类型定义

// 产品表
export interface Product {
  id: number;
  productCode: string; // 产品编码
  name: string; // 产品名称
  description?: string; // 产品描述
  category: string; // 产品类别
  unit: string; // 计量单位
  standardPrice: number; // 标准价格
  productionCycle: number; // 生产周期（小时）
  qualityStandard?: string; // 质量标准
  status: 'active' | 'inactive' | 'discontinued'; // 状态
  createdAt: string;
  updatedAt: string;
}

// 原料表
export interface Material {
  id: number;
  materialCode: string; // 原料编码
  name: string; // 原料名称
  description?: string; // 原料描述
  category: string; // 原料类别
  unit: string; // 计量单位
  standardPrice: number; // 标准价格
  supplier?: string; // 供应商
  minStock: number; // 最小库存
  currentStock: number; // 当前库存
  storageCondition?: string; // 存储条件
  shelfLife?: number; // 保质期（天）
  status: 'active' | 'inactive' | 'shortage'; // 状态
  createdAt: string;
  updatedAt: string;
}

// 产品原料关系表（配方表）
export interface ProductMaterial {
  id: number;
  productId: number; // 产品ID
  materialId: number; // 原料ID
  quantity: number; // 用量
  unit: string; // 用量单位
  isRequired: boolean; // 是否必需
  notes?: string; // 备注
  createdAt: string;
  updatedAt: string;
}

// 扩展的产品信息（包含原料配方）
export interface ProductWithMaterials extends Product {
  materials: Array<{
    material: Material;
    quantity: number;
    unit: string;
    isRequired: boolean;
    notes?: string;
  }>;
  totalMaterialCost: number; // 原料总成本
}

// 扩展的原料信息（包含关联产品）
export interface MaterialWithProducts extends Material {
  products: Array<{
    product: Product;
    quantity: number;
    unit: string;
    isRequired: boolean;
    notes?: string;
  }>;
  usageCount: number; // 被多少个产品使用
}

// 配方管理相关类型
export interface Recipe {
  id: number;
  productId: number;
  productName: string;
  version: string; // 配方版本
  isActive: boolean; // 是否为当前版本
  materials: Array<{
    materialId: number;
    materialName: string;
    quantity: number;
    unit: string;
    isRequired: boolean;
    cost: number; // 该原料的成本
    notes?: string;
  }>;
  totalCost: number; // 配方总成本
  createdAt: string;
  updatedAt: string;
}

// 库存预警类型
export interface StockAlert {
  materialId: number;
  materialName: string;
  currentStock: number;
  minStock: number;
  shortageAmount: number;
  affectedProducts: string[]; // 受影响的产品
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
}

// 生产需求分析类型
export interface ProductionRequirement {
  productId: number;
  productName: string;
  plannedQuantity: number;
  materialRequirements: Array<{
    materialId: number;
    materialName: string;
    requiredQuantity: number;
    availableStock: number;
    shortageQuantity: number;
    unit: string;
  }>;
}
