<template>
  <div class="repair-record-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card shadow="never" class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="设备名称">
            <el-input
              v-model="searchForm.deviceName"
              placeholder="请输入设备名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="维修人员">
            <el-input
              v-model="searchForm.repairer"
              placeholder="请输入维修人员"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <template #icon>
                <component :is="useRenderIcon(Search)" />
              </template>
              搜索
            </el-button>
            <el-button @click="resetSearch">
              <template #icon>
                <component :is="useRenderIcon(Refresh)" />
              </template>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-card shadow="never" class="action-card">
        <el-button type="primary" @click="openAdd">
          <template #icon>
            <component :is="useRenderIcon(Plus)" />
          </template>
          新增记录
        </el-button>
        <el-button type="success" @click="handleExport">
          <template #icon>
            <component :is="useRenderIcon(Download)" />
          </template>
          导出Excel
        </el-button>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card shadow="never" class="table-card">
        <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="记录ID" width="80" />
          <el-table-column prop="reportId" label="报修单ID" width="100" />
          <el-table-column prop="deviceName" label="设备名称" width="120" />
          <el-table-column prop="deviceType" label="设备类型" width="100" />
          <el-table-column prop="lineName" label="产线名称" width="120" />
          <el-table-column prop="repairer" label="维修人员" width="100" />
          <el-table-column prop="repairContent" label="维修内容" min-width="200" show-overflow-tooltip />
          <el-table-column prop="repairTime" label="维修时间" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.repairTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="isCompleted" label="完成状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.isCompleted ? 'success' : 'warning'"
                size="small"
              >
                {{ row.isCompleted ? '已完成' : '进行中' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="info"
                size="small"
                @click="openView(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(View)" />
                </template>
                查看
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="openEdit(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Edit)" />
                </template>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
                link
              >
                <template #icon>
                  <component :is="useRenderIcon(Delete)" />
                </template>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="paginationConfig.current"
            v-model:page-size="paginationConfig.size"
            :total="paginationConfig.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="getList"
            @current-change="getList"
          />
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="报修单ID" prop="reportId">
          <el-input-number
            v-model="formData.reportId"
            placeholder="请输入报修单ID"
            style="width: 100%"
            :min="1"
          />
        </el-form-item>
        <el-form-item label="维修人员" prop="repairer">
          <el-input
            v-model="formData.repairer"
            placeholder="请输入维修人员"
          />
        </el-form-item>
        <el-form-item label="维修内容" prop="repairContent">
          <el-input
            v-model="formData.repairContent"
            type="textarea"
            :rows="4"
            placeholder="请输入维修内容"
          />
        </el-form-item>
        <el-form-item label="完成状态" prop="isCompleted">
          <el-switch
            v-model="formData.isCompleted"
            active-text="已完成"
            inactive-text="进行中"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="维修记录详情"
      width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">
          {{ detailData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="报修单ID">
          {{ detailData.reportId }}
        </el-descriptions-item>
        <el-descriptions-item label="设备名称">
          {{ detailData.deviceName }}
        </el-descriptions-item>
        <el-descriptions-item label="设备类型">
          {{ detailData.deviceType }}
        </el-descriptions-item>
        <el-descriptions-item label="产线名称">
          {{ detailData.lineName }}
        </el-descriptions-item>
        <el-descriptions-item label="维修人员">
          {{ detailData.repairer }}
        </el-descriptions-item>
        <el-descriptions-item label="维修时间">
          {{ formatDateTime(detailData.repairTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="完成状态">
          <el-tag :type="detailData.isCompleted ? 'success' : 'warning'" size="small">
            {{ detailData.isCompleted ? '已完成' : '进行中' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="维修内容" :span="2">
          {{ detailData.repairContent }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import Plus from "@iconify-icons/ep/plus";
import Edit from "@iconify-icons/ep/edit";
import Delete from "@iconify-icons/ep/delete";
import View from "@iconify-icons/ep/view";
import Download from "@iconify-icons/ep/download";

import {
  getRepairRecordList,
  getRepairRecordById,
  createRepairRecord,
  updateRepairRecord,
  deleteRepairRecord,
  exportRepairRecords,
  type DeviceRepairRecordDTO,
  type DeviceRepairRecord
} from "@/api/repair-record";

// 查询参数类型
interface RecordQueryParams {
  deviceName?: string;
  repairer?: string;
}

// 搜索表单
const searchForm = reactive<RecordQueryParams>({
  deviceName: "",
  repairer: ""
});

// 分页配置
const paginationConfig = reactive({
  total: 0,
  size: 10,
  current: 1
});

// 表格数据
const tableData = ref<DeviceRepairRecordDTO[]>([]);
const loading = ref(false);

// 对话框
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 详情对话框
const detailDialogVisible = ref(false);
const detailData = ref<DeviceRepairRecordDTO>({} as DeviceRepairRecordDTO);

// 表单
const formRef = ref<FormInstance>();
const formData = ref<DeviceRepairRecord>({
  reportId: undefined,
  repairer: "",
  repairContent: "",
  isCompleted: false
});

// 表单验证规则
const formRules: FormRules = {
  reportId: [{ required: true, message: "请输入报修单ID", trigger: "blur" }],
  repairer: [{ required: true, message: "请输入维修人员", trigger: "blur" }],
  repairContent: [{ required: true, message: "请输入维修内容", trigger: "blur" }]
};

// 获取维修记录列表
const getRecordList = async () => {
  try {
    const result = await getRepairRecordList();
    return result;
  } catch (error) {
    console.error('获取维修记录列表失败:', error);
    throw error;
  }
};

// 获取列表
const getList = async () => {
  loading.value = true;
  try {
    const result = await getRecordList();
    if (result && result.success) {
      tableData.value = result.data || [];
      paginationConfig.total = result.data?.length || 0;
    } else {
      ElMessage.error("获取数据失败");
    }
  } catch (error) {
    console.error('获取列表失败:', error);
    ElMessage.error("获取数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  paginationConfig.current = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  searchForm.deviceName = "";
  searchForm.repairer = "";
  paginationConfig.current = 1;
  getList();
};

// 打开新增对话框
const openAdd = () => {
  dialogTitle.value = "新增维修记录";
  isEdit.value = false;
  formData.value = {
    reportId: undefined,
    repairer: "",
    repairContent: "",
    isCompleted: false
  };
  dialogVisible.value = true;
};

// 打开编辑对话框
const openEdit = async (row: DeviceRepairRecordDTO) => {
  dialogTitle.value = "编辑维修记录";
  isEdit.value = true;

  try {
    const res = await getRepairRecordById(row.id!);
    if (res && res.success && res.data) {
      formData.value = {
        id: res.data.id,
        reportId: res.data.reportId,
        repairer: res.data.repairer,
        repairContent: res.data.repairContent,
        repairTime: res.data.repairTime,
        isCompleted: res.data.isCompleted
      };
    } else {
      ElMessage.error("获取详情失败");
      return;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
    return;
  }

  dialogVisible.value = true;
};

// 打开详情对话框
const openView = async (row: DeviceRepairRecordDTO) => {
  try {
    const res = await getRepairRecordById(row.id!);
    if (res && res.success && res.data) {
      // 合并列表数据和详情数据，保留联表字段
      detailData.value = {
        ...res.data,
        deviceName: row.deviceName,
        deviceType: row.deviceType,
        lineName: row.lineName
      };
    } else {
      ElMessage.error("获取详情失败");
      return;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error("获取详情失败");
    return;
  }

  detailDialogVisible.value = true;
};

// 删除
const handleDelete = async (row: DeviceRepairRecordDTO) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除维修记录 ${row.id} 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const result = await deleteRepairRecord(row.id!);
    if (result && result.success) {
      ElMessage.success("删除成功");
      getList();
    } else {
      ElMessage.error(result?.msg || "删除失败");
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error("删除失败");
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (isEdit.value) {
      const result = await updateRepairRecord(formData.value);
      if (result && result.success) {
        ElMessage.success("更新成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "更新失败");
      }
    } else {
      // 新增时设置维修时间为当前时间（ISO格式）
      const submitData = {
        ...formData.value,
        repairTime: new Date().toISOString().slice(0, 19)
      };

      const result = await createRepairRecord(submitData);
      if (result && result.success) {
        ElMessage.success("新增成功");
        closeDialog();
        getList();
      } else {
        ElMessage.error(result?.msg || "新增失败");
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error("提交失败");
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
};

// 导出Excel
const handleExport = async () => {
  try {
    ElMessage.info("正在导出，请稍候...");

    const blob = await exportRepairRecords();

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 设置文件名（包含当前日期）
    const now = new Date();
    const dateStr = now.getFullYear() +
      String(now.getMonth() + 1).padStart(2, '0') +
      String(now.getDate()).padStart(2, '0');
    link.download = `维修记录_${dateStr}.xlsx`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success("导出成功");
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error("导出失败");
  }
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  try {
    const date = new Date(dateTime);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '-';
  }
};

// 生命周期
onMounted(() => {
  getList();
});
</script>

<style scoped>
.repair-record-container {
  padding: 20px;
}

.search-section,
.action-section,
.table-section {
  margin-bottom: 20px;
}

.search-card,
.action-card,
.table-card {
  border-radius: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .repair-record-container {
    padding: 10px;
  }
  
  .el-dialog {
    width: 90% !important;
  }
}
</style>
