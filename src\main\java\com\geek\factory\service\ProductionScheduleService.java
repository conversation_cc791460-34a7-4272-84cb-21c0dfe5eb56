package com.geek.factory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.dto.ProductionScheduleDTO;
import com.geek.factory.entity.ProductionSchedule;

import java.util.List;

/**
 * 生产排班服务接口
 */
public interface ProductionScheduleService extends IService<ProductionSchedule> {

    /**
     * 获取所有排班信息（包含关联的产线名称和生产计划编号）
     */
    List<ProductionScheduleDTO> getAllWithLineName();

    /**
     * 查询排班列表（含产线名称和计划编码）
     */
    List<ProductionScheduleDTO> getScheduleWithNames(ProductionScheduleDTO dto);

    /**
     * 根据生产计划ID获取计划编码
     */
    String getPlanCodeById(Long productionPlanId);

    /**
     * 根据产线ID获取产线名称
     */
    String getProductLineNameById(Long productLineId);

    /**
     * 根据ID获取排班详情
     */
    ProductionSchedule getById(Long id);

    /**
     * 创建排班
     */
    void add(ProductionSchedule schedule);

    /**
     * 更新排班
     */
    void update(ProductionSchedule schedule);

    /**
     * 删除排班
     */
    void deleteById(Long id);

    /**
     * 根据班次查询排班
     */
    List<ProductionScheduleDTO> getByShift(String shift);
}
