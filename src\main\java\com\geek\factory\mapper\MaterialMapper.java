package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.entity.Material;
import com.geek.factory.vo.MaterialQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 原料Mapper接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Mapper
public interface MaterialMapper extends BaseMapper<Material> {

    /**
     * 分页查询原料列表
     * @param page 分页对象
     * @param query 查询条件
     * @return 原料分页列表
     */
    @Select("<script>" +
            "SELECT id, name, type, unit, price, pic, create_time, update_time " +
            "FROM materials WHERE 1=1 " +
            "<if test='query.name != null and query.name != \"\"'>" +
            "AND name LIKE CONCAT('%', #{query.name}, '%') " +
            "</if>" +
            "<if test='query.type != null and query.type != \"\"'>" +
            "AND type LIKE CONCAT('%', #{query.type}, '%') " +
            "</if>" +
            "<if test='query.unit != null and query.unit != \"\"'>" +
            "AND unit = #{query.unit} " +
            "</if>" +
            "<if test='query.minPrice != null'>" +
            "AND price >= #{query.minPrice} " +
            "</if>" +
            "<if test='query.maxPrice != null'>" +
            "AND price <= #{query.maxPrice} " +
            "</if>" +
            "ORDER BY create_time DESC, id DESC" +
            "</script>")
    IPage<Material> selectMaterialPage(Page<Material> page, @Param("query") MaterialQueryVO query);

    /**
     * 根据类型查询原料列表
     * @param type 原料类型
     * @return 原料列表
     */
    @Select("SELECT * FROM materials WHERE type = #{type} ORDER BY create_time DESC")
    List<Material> selectByType(@Param("type") String type);

    /**
     * 根据价格区间查询原料
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 原料列表
     */
    @Select("SELECT * FROM materials WHERE price BETWEEN #{minPrice} AND #{maxPrice} ORDER BY price ASC")
    List<Material> selectByPriceRange(@Param("minPrice") java.math.BigDecimal minPrice,
                                    @Param("maxPrice") java.math.BigDecimal maxPrice);

    /**
     * 查询原料类型统计
     * @return 类型统计列表
     */
    @Select("SELECT type, COUNT(*) as count FROM materials GROUP BY type ORDER BY count DESC")
    List<java.util.Map<String, Object>> selectTypeStatistics();

    /**
     * 根据单位查询原料列表
     * @param unit 计量单位
     * @return 原料列表
     */
    @Select("SELECT * FROM materials WHERE unit = #{unit} ORDER BY create_time DESC")
    List<Material> selectByUnit(@Param("unit") String unit);
}
