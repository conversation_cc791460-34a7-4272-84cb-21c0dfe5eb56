-- 工厂管理系统数据库初始化脚本

-- 创建产线表
CREATE TABLE IF NOT EXISTS `production_line` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '产线ID',
  `code` varchar(50) NOT NULL COMMENT '产线编码',
  `name` varchar(100) NOT NULL COMMENT '产线名称',
  `description` varchar(255) DEFAULT NULL COMMENT '产线描述',
  `status` int DEFAULT '1' COMMENT '产线状态：0-停用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产线表';

-- 创建设备表
CREATE TABLE IF NOT EXISTS `device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `code` varchar(50) NOT NULL COMMENT '设备编码',
  `name` varchar(100) NOT NULL COMMENT '设备名称',
  `type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `productive_id` int DEFAULT NULL COMMENT '所属产线ID',
  `status` int DEFAULT '1' COMMENT '设备状态：0-停用，1-启用',
  `description` varchar(255) DEFAULT NULL COMMENT '设备描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_productive_id` (`productive_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备表';

-- 创建设备维修表
CREATE TABLE IF NOT EXISTS `repair` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '维修ID',
  `device_id` int DEFAULT NULL COMMENT '设备ID',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `productline_id` int DEFAULT NULL COMMENT '产线ID',
  `repair_person` varchar(50) DEFAULT NULL COMMENT '维修人员',
  `repair_date` varchar(20) DEFAULT NULL COMMENT '维修日期',
  `status` varchar(20) DEFAULT NULL COMMENT '维修状态',
  `repair_content` text COMMENT '维修内容',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_productline_id` (`productline_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备维修表';

-- 创建维修记录表
CREATE TABLE IF NOT EXISTS `record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `device_id` int DEFAULT NULL COMMENT '设备ID',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `productline_id` int DEFAULT NULL COMMENT '产线ID',
  `record_date` varchar(20) DEFAULT NULL COMMENT '记录日期',
  `record_content` text COMMENT '记录内容',
  `record_result` text COMMENT '记录结果',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_productline_id` (`productline_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维修记录表';

-- 插入测试数据
-- 产线测试数据
INSERT IGNORE INTO `production_line` (`id`, `code`, `name`, `description`, `status`) VALUES
(1, 'LINE001', '产线A', '主要生产线A', 1),
(2, 'LINE002', '产线B', '主要生产线B', 1),
(3, 'LINE003', '产线C', '主要生产线C', 1);

-- 设备测试数据
INSERT IGNORE INTO `device` (`id`, `code`, `name`, `type`, `productive_id`, `status`, `description`) VALUES
(1, 'DEV001', '空压机', '压缩设备', 1, 1, '主要空压机设备'),
(2, 'DEV002', '注塑机', '成型设备', 1, 1, '注塑成型机'),
(3, 'DEV003', '包装机', '包装设备', 2, 1, '自动包装机'),
(4, 'DEV004', '检测仪', '检测设备', 2, 1, '质量检测仪'),
(5, 'DEV005', '传送带', '输送设备', 3, 1, '自动传送带');

-- 设备维修测试数据
INSERT IGNORE INTO `repair` (`id`, `device_id`, `device_name`, `productline_id`, `repair_person`, `repair_date`, `status`, `repair_content`) VALUES
(1, 1, '空压机', 1, '张三', '2024-05-01', '已完成', '更换滤芯'),
(2, 2, '注塑机', 1, '李四', '2024-05-02', '进行中', '检修模具'),
(3, 3, '包装机', 2, '王五', '2024-05-03', '已完成', '调整包装参数');

-- 维修记录测试数据
INSERT IGNORE INTO `record` (`id`, `device_id`, `device_name`, `productline_id`, `record_date`, `record_content`, `record_result`) VALUES
(1, 1, '空压机', 1, '2024-05-01', '更换滤芯', '正常运行'),
(2, 2, '注塑机', 1, '2024-05-02', '检修模具', '待测试'),
(3, 3, '包装机', 2, '2024-05-03', '调整包装参数', '正常运行');

-- 创建设备维修记录日志表
CREATE TABLE IF NOT EXISTS `device_repair_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `repair_id` bigint DEFAULT NULL COMMENT '关联的维修记录ID',
  `product_line_id` bigint DEFAULT NULL COMMENT '产线ID',
  `remark` text COMMENT '备注',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作员',
  `status` varchar(20) DEFAULT NULL COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_repair_id` (`repair_id`),
  KEY `idx_product_line_id` (`product_line_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备维修记录日志表';

-- 设备维修记录日志测试数据
INSERT IGNORE INTO `device_repair_log` (`id`, `repair_id`, `product_line_id`, `remark`, `operator`, `status`) VALUES
(1, 1, 1, '更换滤芯操作记录', '张工', '已完成'),
(2, 2, 1, '检修模具操作记录', '李技师', '处理中'),
(3, 3, 2, '调整包装参数操作记录', '王工程师', '已完成');
