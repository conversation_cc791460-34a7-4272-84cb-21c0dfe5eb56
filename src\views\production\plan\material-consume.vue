<template>
  <div class="material-consume-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-page-header @back="goBack" :title="pageTitle">
        <template #content>
          <div class="header-content">
            <h1 class="page-title">
              <el-icon class="title-icon"><TrendCharts /></el-icon>
              原料消耗分析大屏
            </h1>
            <p class="page-subtitle">实时监控生产计划原料消耗情况</p>
          </div>
        </template>
      </el-page-header>
    </div>

    <!-- 数字化大屏 - 顶部数据概览 -->
    <div class="dashboard-overview">
      <div class="overview-cards">
        <div class="overview-card primary">
          <div class="card-icon">
            <el-icon><Box /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">原料种类</div>
            <div class="card-value">{{ materialConsumes.length }}</div>
            <div class="card-subtitle">种</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-up"><TrendCharts /></el-icon>
          </div>
        </div>

        <div class="overview-card success">
          <div class="card-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">总消耗量</div>
            <div class="card-value">{{ totalConsumed }}</div>
            <div class="card-subtitle">单位</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-up"><TrendCharts /></el-icon>
          </div>
        </div>

        <div class="overview-card warning">
          <div class="card-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">计划数量</div>
            <div class="card-value">{{ planInfo?.planQuantity || 0 }}</div>
            <div class="card-subtitle">件</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-up"><TrendCharts /></el-icon>
          </div>
        </div>

        <div class="overview-card danger">
          <div class="card-icon">
            <el-icon><Goods /></el-icon>
          </div>
          <div class="card-content">
            <div class="card-title">消耗率</div>
            <div class="card-value">{{ consumeRate }}%</div>
            <div class="card-subtitle">效率</div>
          </div>
          <div class="card-trend">
            <el-icon class="trend-up"><TrendCharts /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和数据展示区域 -->
    <div class="dashboard-content">
      <!-- 左侧饼图区域 -->
      <div class="chart-section">
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">
              <el-icon><TrendCharts /></el-icon>
              原料消耗分布
            </h3>
            <div class="chart-controls">
              <el-button-group>
                <el-button :type="chartType === 'pie' ? 'primary' : ''" @click="switchChartType('pie')">饼图</el-button>
                <el-button :type="chartType === 'doughnut' ? 'primary' : ''" @click="switchChartType('doughnut')">环形图</el-button>
                <el-button :type="chartType === 'bar' ? 'primary' : ''" @click="switchChartType('bar')">柱状图</el-button>
              </el-button-group>
            </div>
          </div>
          <div class="chart-container">
            <div ref="chartRef" class="chart-canvas" v-loading="loading"></div>
          </div>
        </div>
      </div>

      <!-- 右侧数据列表区域 -->
      <div class="data-section">
        <div class="data-card">
          <div class="data-header">
            <h3 class="data-title">
              <el-icon><Box /></el-icon>
              原料消耗明细
            </h3>
            <div class="data-summary">
              <span class="summary-item">
                <span class="summary-label">总计:</span>
                <span class="summary-value">{{ totalConsumed }} 单位</span>
              </span>
            </div>
          </div>
          <div class="data-list">
            <div
              v-for="(item, index) in materialConsumes"
              :key="item.materialId"
              class="data-item"
              :class="`data-item-${index % 4}`"
            >
              <div class="item-icon">
                <el-icon><Box /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-name">{{ item.materialName }}</div>
                <div class="item-code">{{ item.materialCode }}</div>
              </div>
              <div class="item-stats">
                <div class="item-value">{{ item.totalConsumed }}</div>
                <div class="item-unit">{{ item.unit }}</div>
                <div class="item-percentage">{{ getConsumePercentage(item.totalConsumed) }}%</div>
              </div>
              <div class="item-progress">
                <div
                  class="progress-bar"
                  :style="{ width: getConsumePercentage(item.totalConsumed) + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <el-empty
            v-if="!loading && materialConsumes.length === 0"
            description="暂无原料消耗数据"
            class="empty-state"
          />
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Document, Box, TrendCharts, Goods } from "@element-plus/icons-vue";
import { getPlanMaterials, getPlanMaterialsByCode } from "@/api/production";
import * as echarts from "echarts";

// 路由
const route = useRoute();
const router = useRouter();

// 获取路由参数中的计划ID和计划编码
const planId = Number(route.params.id) || Number(route.query.planId) || null;
const planCode = route.params.planCode || route.query.planCode || null;

console.log('🔍 路由参数:', { planId, planCode, params: route.params, query: route.query });

// 响应式数据
const loading = ref(false);
const planInfo = ref<any>(null);
const materialConsumes = ref<any[]>([]);
const chartRef = ref<HTMLElement>();
const chartType = ref('pie'); // 图表类型：pie, doughnut, bar
let chartInstance: echarts.ECharts | null = null;

// 计算属性
const pageTitle = computed(() => {
  return planInfo.value ? `${planInfo.value.planName} - 原料消耗` : "原料消耗详情";
});

const totalConsumed = computed(() => {
  return materialConsumes.value.reduce((sum: number, item: any) => sum + (item.totalConsumed || 0), 0);
});

// 消耗率计算
const consumeRate = computed(() => {
  if (!planInfo.value?.planQuantity || totalConsumed.value === 0) return 0;
  return Math.round((totalConsumed.value / (planInfo.value.planQuantity * 10)) * 100);
});

// 方法
const goBack = () => {
  // 返回到生产计划列表页面
  router.push('/production/plan/list');
};

const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "info",
    1: "success", 
    2: "warning",
    3: "danger"
  };
  return statusMap[status] || "info";
};

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "未开始",
    1: "进行中",
    2: "已完成", 
    3: "已暂停"
  };
  return statusMap[status] || "未知";
};

const formatDate = (date: string) => {
  if (!date) return "-";
  return new Date(date).toLocaleString();
};

const getConsumePercentage = (consumed: number) => {
  if (totalConsumed.value === 0) return 0;
  return Math.round((consumed / totalConsumed.value) * 100);
};

const getProgressColor = (consumed: number) => {
  const percentage = getConsumePercentage(consumed);
  if (percentage > 50) return "#f56c6c";
  if (percentage > 20) return "#e6a23c";
  return "#67c23a";
};

// 切换图表类型
const switchChartType = (type: string) => {
  chartType.value = type;
  nextTick(() => {
    initChart();
  });
};

// 显示模拟数据
const showMockData = (currentPlanId: number) => {
  console.log(`🎭 显示模拟数据，计划ID: ${currentPlanId}`);

  const mockResponse = {
    data: {
      planId: currentPlanId,
      planCode: `PLAN-${currentPlanId.toString().padStart(6, '0')}`,
      planName: `生产计划${currentPlanId}`,
      productName: "智能手机",
      planQuantity: 100,
      status: 1,
      createTime: new Date().toISOString(),
      materialConsumes: [
        {
          materialId: 1,
          materialCode: "M001",
          materialName: "铝合金板",
          totalConsumed: 200,
          unit: "块"
        },
        {
          materialId: 4,
          materialCode: "M004",
          materialName: "锂离子电池",
          totalConsumed: 100,
          unit: "个"
        },
        {
          materialId: 5,
          materialCode: "M005",
          materialName: "PCB电路板",
          totalConsumed: 300,
          unit: "块"
        },
        {
          materialId: 2,
          materialCode: "M002",
          materialName: "ABS塑料颗粒",
          totalConsumed: 150,
          unit: "kg"
        }
      ]
    }
  };

  if (mockResponse.data) {
    planInfo.value = {
      planId: mockResponse.data.planId,
      planCode: mockResponse.data.planCode,
      planName: mockResponse.data.planName,
      productName: mockResponse.data.productName,
      planQuantity: mockResponse.data.planQuantity,
      status: mockResponse.data.status || 1,
      createTime: mockResponse.data.createTime || new Date().toISOString()
    };
    materialConsumes.value = mockResponse.data.materialConsumes || [];

    // 初始化图表
    nextTick(() => {
      initChart();
    });
  }
};

// 获取数据
const getMaterialConsumeData = async () => {
  loading.value = true;

  try {
    let response;

    // 优先使用计划编码查询
    if (planCode) {
      console.log(`🔍 使用计划编码查询原料消耗数据: ${planCode}`);
      response = await getPlanMaterialsByCode(planCode);
    }
    // 如果没有计划编码，使用计划ID查询
    else if (planId) {
      console.log(`🔍 使用计划ID查询原料消耗数据: ${planId}`);
      response = await getPlanMaterials(planId);
    }
    // 如果都没有，使用测试数据
    else {
      console.log(`🔍 没有计划编码和ID，使用测试计划编码查询`);
      response = await getPlanMaterialsByCode('PLAN-000003'); // 使用测试计划编码
    }

    console.log('📊 API响应数据:', response);
    console.log('📊 API响应状态:', response?.success);
    console.log('📊 API响应数据长度:', response?.data?.length);

    // 详细检查API响应
    console.log('🔍 详细检查API响应:');
    console.log('  - response:', response);
    console.log('  - response.success:', response?.success);
    console.log('  - response.data:', response?.data);
    console.log('  - response.data.length:', response?.data?.length);
    console.log('  - typeof response.data:', typeof response?.data);

    if (response && response.success && response.data && Array.isArray(response.data) && response.data.length > 0) {
      console.log(`✅ 获取到 ${response.data.length} 条原料消耗记录`);

      // 设置计划基本信息
      const currentPlanCode = planCode || `PLAN-${(planId || 1).toString().padStart(6, '0')}`;
      planInfo.value = {
        planId: planId || 1,
        planCode: currentPlanCode,
        planName: `生产计划 ${currentPlanCode}`,
        productName: "智能手机主板",
        planQuantity: 100,
        status: 1,
        createTime: new Date().toISOString()
      };

      // 转换API数据格式以匹配前端显示
      materialConsumes.value = response.data.map((item: any) => ({
        materialId: item.materialId,
        materialCode: `M${String(item.materialId).padStart(3, '0')}`,
        materialName: item.materialName,
        totalConsumed: item.requiredQuantity,
        unit: item.unit,
        remark: item.remark
      }));

      console.log('🎯 转换后的原料消耗数据:', materialConsumes.value);

      // 初始化图表
      await nextTick();
      initChart();

      loading.value = false;
      ElMessage.success(`成功加载 ${response.data.length} 条原料消耗记录`);
      return;
    } else {
      const displayId = planCode || planId || '未知';
      console.warn(`⚠️ 生产计划 ${displayId} 暂无原料消耗记录，API响应:`, response);
      ElMessage.warning(`生产计划 ${displayId} 暂无原料消耗记录，显示模拟数据`);

      // 显示模拟数据
      showMockData(planId || 1);
    }
  } catch (error) {
    const displayId = planCode || planId || '未知';
    console.error(`❌ 获取生产计划 ${displayId} 原料消耗数据失败:`, error);
    console.error('❌ 错误详情:', {
      message: error?.message,
      status: error?.response?.status,
      data: error?.response?.data,
      config: error?.config
    });
    ElMessage.error(`获取生产计划 ${displayId} 数据失败: ${error?.message || '未知错误'}，显示模拟数据`);

    // 即使出错也显示模拟数据
    showMockData(planId || 1);
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value || materialConsumes.value.length === 0) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartRef.value);

  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];

  let option: any = {
    title: {
      text: "原料消耗分布",
      left: "center",
      textStyle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff'
      }
    },
    color: colors
  };

  if (chartType.value === 'pie') {
    option.legend = {
      orient: "vertical",
      left: "left",
      data: materialConsumes.value.map((item: any) => item.materialName)
    };
    option.series = [{
      name: "原料消耗",
      type: "pie",
      radius: "60%",
      center: ['60%', '50%'],
      data: materialConsumes.value.map((item: any) => ({
        name: item.materialName,
        value: item.totalConsumed
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      },
      label: {
        show: true,
        formatter: '{b}: {d}%'
      }
    }];
  } else if (chartType.value === 'doughnut') {
    option.legend = {
      orient: "vertical",
      left: "left",
      data: materialConsumes.value.map((item: any) => item.materialName)
    };
    option.series = [{
      name: "原料消耗",
      type: "pie",
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: materialConsumes.value.map((item: any) => ({
        name: item.materialName,
        value: item.totalConsumed
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      },
      label: {
        show: true,
        formatter: '{b}: {d}%'
      }
    }];
  } else if (chartType.value === 'bar') {
    option.xAxis = {
      type: 'category',
      data: materialConsumes.value.map((item: any) => item.materialName),
      axisLabel: {
        rotate: 45
      }
    };
    option.yAxis = {
      type: 'value',
      name: '消耗量'
    };
    option.series = [{
      name: "原料消耗",
      type: "bar",
      data: materialConsumes.value.map((item: any) => item.totalConsumed),
      itemStyle: {
        borderRadius: [4, 4, 0, 0]
      },
      label: {
        show: true,
        position: 'top'
      }
    }];
    option.grid = {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    };
  }

  chartInstance.setOption(option);

  // 响应式调整
  window.addEventListener('resize', () => {
    chartInstance?.resize();
  });
};

// 生命周期
onMounted(() => {
  getMaterialConsumeData();
});

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', () => {
    chartInstance?.resize();
  });
});
</script>

<style scoped>
.material-consume-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin: 0;
}

.title-icon {
  font-size: 32px;
  color: #ffd700;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* 数字化大屏概览卡片 */
.dashboard-overview {
  margin-bottom: 30px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.overview-card.primary::before {
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.overview-card.success::before {
  background: linear-gradient(90deg, #56ab2f, #a8e6cf);
}

.overview-card.warning::before {
  background: linear-gradient(90deg, #f093fb, #f5576c);
}

.overview-card.danger::before {
  background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.card-icon {
  font-size: 32px;
  margin-bottom: 16px;
  color: #667eea;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.card-value {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  line-height: 1;
}

.card-subtitle {
  font-size: 12px;
  color: #999;
}

.card-trend {
  position: absolute;
  top: 20px;
  right: 20px;
  opacity: 0.3;
}

.trend-up {
  color: #52c41a;
  font-size: 20px;
}

/* 图表和数据展示区域 */
.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  height: 600px;
}

.chart-section, .data-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.chart-card, .data-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header, .data-header {
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.chart-title, .data-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

/* 数据列表样式 */
.data-summary {
  display: flex;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 14px;
  color: #666;
}

.summary-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.data-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.data-item {
  display: flex;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.data-item:hover {
  transform: translateX(5px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.data-item-0 { border-left-color: #667eea; }
.data-item-1 { border-left-color: #56ab2f; }
.data-item-2 { border-left-color: #f093fb; }
.data-item-3 { border-left-color: #4facfe; }

.item-icon {
  font-size: 24px;
  color: #667eea;
  margin-right: 16px;
}

.item-content {
  flex: 1;
  margin-right: 16px;
}

.item-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.item-code {
  font-size: 12px;
  color: #999;
}

.item-stats {
  text-align: right;
  margin-right: 16px;
}

.item-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.item-unit {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.item-percentage {
  font-size: 12px;
  color: #667eea;
  font-weight: bold;
}

.item-progress {
  width: 60px;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.empty-state {
  margin-top: 60px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    height: auto;
  }

  .chart-section {
    height: 500px;
  }

  .data-section {
    height: 600px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .page-title {
    font-size: 24px;
  }

  .card-value {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .material-consume-container {
    padding: 15px;
  }
}
</style>

<style scoped>
.el-card {
  margin-bottom: 16px;
}

.el-statistic {
  text-align: center;
}

.el-progress {
  margin-top: 8px;
}
</style> 