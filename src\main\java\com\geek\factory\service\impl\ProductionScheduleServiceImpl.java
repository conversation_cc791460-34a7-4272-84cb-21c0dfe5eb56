package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.dto.ProductionScheduleDTO;
import com.geek.factory.entity.ProductionSchedule;
import com.geek.factory.mapper.ProductionScheduleMapper;
import com.geek.factory.mapper.ProductionPlanMapper;
import com.geek.factory.mapper.ProductionLineMapper;
import com.geek.factory.service.PlanCodeGeneratorService;
import com.geek.factory.service.ProductionScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产排班服务实现类
 */
@Service
public class ProductionScheduleServiceImpl extends ServiceImpl<ProductionScheduleMapper, ProductionSchedule> implements ProductionScheduleService {

    @Autowired
    private ProductionScheduleMapper scheduleMapper;

    @Autowired
    private ProductionPlanMapper productionPlanMapper;

    @Autowired
    private ProductionLineMapper productionLineMapper;

    @Autowired
    private PlanCodeGeneratorService planCodeGeneratorService;

    @Override
    public List<ProductionScheduleDTO> getAllWithLineName() {
        return scheduleMapper.getAllWithLineName();
    }

    @Override
    public List<ProductionScheduleDTO> getScheduleWithNames(ProductionScheduleDTO dto) {
        return scheduleMapper.getScheduleWithNames(dto);
    }

    @Override
    public String getPlanCodeById(Long productionPlanId) {
        if (productionPlanId == null) {
            return null;
        }
        // 通过生产计划ID查询计划编码
        return productionPlanMapper.getPlanCodeById(productionPlanId);
    }

    @Override
    public String getProductLineNameById(Long productLineId) {
        if (productLineId == null) {
            return null;
        }
        // 通过产线ID查询产线名称
        return productionLineMapper.getNameById(productLineId);
    }

    @Override
    public ProductionSchedule getById(Long id) {
        return scheduleMapper.selectById(id);
    }

    @Override
    public void add(ProductionSchedule schedule) {
        // 设置创建时间
        schedule.setCreateTime(LocalDateTime.now());
        schedule.setUpdateTime(LocalDateTime.now());

        // 自动生成生产计划编号（用于排班关联）
        if (schedule.getScheduleCode() == null || schedule.getScheduleCode().isEmpty()) {
            schedule.setScheduleCode(planCodeGeneratorService.generateProductionPlanCode());
        }

        scheduleMapper.insert(schedule);
    }

    @Override
    public void update(ProductionSchedule schedule) {
        // 设置更新时间
        schedule.setUpdateTime(LocalDateTime.now());
        scheduleMapper.updateById(schedule);
    }

    @Override
    public void deleteById(Long id) {
        scheduleMapper.deleteById(id);
    }

    @Override
    public List<ProductionScheduleDTO> getByShift(String shift) {
        if (shift == null || shift.trim().isEmpty()) {
            return new java.util.ArrayList<>();
        }
        return scheduleMapper.getByShift(shift);
    }
}
