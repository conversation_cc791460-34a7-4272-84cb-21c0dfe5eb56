-- 验证和修复生产计划原料消耗功能的SQL脚本

-- 1. 检查表是否存在
SELECT
    TABLE_NAME,
    TABLE_COMMENT
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'factory'
    AND TABLE_NAME = 'production_plan_material';

-- 2. 检查现有生产计划
SELECT 
    id, 
    plan_code, 
    product_name, 
    status,
    create_time
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 10;

-- 3. 检查现有原料
SELECT 
    id, 
    code, 
    name, 
    unit, 
    quantity 
FROM materials 
WHERE quantity > 0
LIMIT 10;

-- 4. 检查现有的生产计划原料关联数据
SELECT COUNT(*) as total_records FROM production_plan_material;

-- 5. 为特定生产计划ID添加测试数据（替换为实际的生产计划ID）
-- 注意：请将下面的ID替换为实际存在的生产计划ID

-- 首先删除可能存在的测试数据
DELETE FROM production_plan_material WHERE plan_id IN (
    SELECT id FROM production_plan ORDER BY create_time DESC LIMIT 3
);

-- 获取最新的3个生产计划ID
SET @plan_id_1 = (SELECT id FROM production_plan ORDER BY create_time DESC LIMIT 1 OFFSET 0);
SET @plan_id_2 = (SELECT id FROM production_plan ORDER BY create_time DESC LIMIT 1 OFFSET 1);
SET @plan_id_3 = (SELECT id FROM production_plan ORDER BY create_time DESC LIMIT 1 OFFSET 2);

-- 为第一个生产计划添加原料消耗数据
INSERT INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan_id_1, m.id, 
    CASE 
        WHEN m.unit = '个' THEN ROUND(RAND() * 100 + 10, 0)
        WHEN m.unit = 'kg' THEN ROUND(RAND() * 50 + 5, 2)
        WHEN m.unit = '片' THEN ROUND(RAND() * 200 + 20, 0)
        WHEN m.unit = 'm' THEN ROUND(RAND() * 100 + 10, 2)
        ELSE ROUND(RAND() * 50 + 5, 2)
    END,
    m.unit,
    CONCAT('用于生产计划 ', @plan_id_1),
    NOW()
FROM materials m 
WHERE m.quantity > 0 
LIMIT 5;

-- 为第二个生产计划添加原料消耗数据
INSERT INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan_id_2, m.id, 
    CASE 
        WHEN m.unit = '个' THEN ROUND(RAND() * 80 + 8, 0)
        WHEN m.unit = 'kg' THEN ROUND(RAND() * 40 + 4, 2)
        WHEN m.unit = '片' THEN ROUND(RAND() * 150 + 15, 0)
        WHEN m.unit = 'm' THEN ROUND(RAND() * 80 + 8, 2)
        ELSE ROUND(RAND() * 40 + 4, 2)
    END,
    m.unit,
    CONCAT('用于生产计划 ', @plan_id_2),
    NOW()
FROM materials m 
WHERE m.quantity > 0 
LIMIT 4;

-- 为第三个生产计划添加原料消耗数据
INSERT INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT @plan_id_3, m.id, 
    CASE 
        WHEN m.unit = '个' THEN ROUND(RAND() * 60 + 6, 0)
        WHEN m.unit = 'kg' THEN ROUND(RAND() * 30 + 3, 2)
        WHEN m.unit = '片' THEN ROUND(RAND() * 120 + 12, 0)
        WHEN m.unit = 'm' THEN ROUND(RAND() * 60 + 6, 2)
        ELSE ROUND(RAND() * 30 + 3, 2)
    END,
    m.unit,
    CONCAT('用于生产计划 ', @plan_id_3),
    NOW()
FROM materials m 
WHERE m.quantity > 0 
LIMIT 6;

-- 6. 验证插入结果
SELECT
    ppm.id,
    ppm.plan_id,
    pp.plan_code,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark,
    ppm.create_time
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
ORDER BY ppm.plan_id, ppm.material_id;

-- 7. 测试API查询语句（模拟后端查询）
SELECT
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = @plan_id_1;

-- 8. 显示可用于测试的生产计划ID
SELECT 
    CONCAT('可用于测试的生产计划ID: ', id, ' (计划编号: ', plan_code, ')') as test_info
FROM production_plan 
WHERE id IN (@plan_id_1, @plan_id_2, @plan_id_3);
