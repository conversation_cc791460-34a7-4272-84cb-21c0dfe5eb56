import { http } from "@/utils/http";

// 通用结果类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 维修记录DTO类型（匹配后端DeviceRepairRecordDTO）
export interface DeviceRepairRecordDTO {
  id?: number;
  reportId?: number;
  repairer?: string;
  repairContent?: string;
  repairTime?: string;
  isCompleted?: boolean;
  // 联表展示字段
  deviceName?: string;      // 来源于 device_repair_report
  deviceType?: string;      // 来源于 device_repair_report
  lineName?: string;        // 来源于 product_line
}

// 维修记录实体类型
export interface DeviceRepairRecord {
  id?: number;
  reportId?: number;
  repairer?: string;
  repairContent?: string;
  repairTime?: string;
  isCompleted?: boolean;
}

// 维修提交DTO类型
export interface RepairSubmitDTO {
  reportId: number;
  repairer: string;
  repairContent: string;
}

// --- 维修记录 API ---

/** 获取维修记录列表（含设备与产线信息） */
export const getRepairRecordList = () => {
  return http.request<Result<DeviceRepairRecordDTO[]>>(
    "get",
    "/api/repair/record/list"
  );
};

/** 根据ID获取维修记录详情 */
export const getRepairRecordById = (id: number) => {
  return http.request<Result<DeviceRepairRecord>>(
    "get",
    `/api/repair/record/${id}`
  );
};

/** 新增维修记录 */
export const createRepairRecord = (data: DeviceRepairRecord) => {
  return http.request<Result>(
    "post",
    "/api/repair/record/add",
    { data }
  );
};

/** 更新维修记录 */
export const updateRepairRecord = (data: DeviceRepairRecord) => {
  return http.request<Result>(
    "put",
    "/api/repair/record/update",
    { data }
  );
};

/** 删除维修记录 */
export const deleteRepairRecord = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/repair/record/delete/${id}`
  );
};

/** 完成维修 */
export const completeRepair = (data: RepairSubmitDTO) => {
  return http.request<Result>(
    "post",
    "/api/repair/record/complete",
    { data }
  );
};

/** 导出维修记录到Excel */
export const exportRepairRecords = () => {
  return http.request<Blob>(
    "get",
    "/api/repair/record/export",
    {},
    {
      responseType: "blob"
    }
  );
};
