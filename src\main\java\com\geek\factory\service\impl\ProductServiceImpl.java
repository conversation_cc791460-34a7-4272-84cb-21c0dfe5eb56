package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Product;
import com.geek.factory.mapper.ProductMapper;
import com.geek.factory.service.ProductService;
import com.geek.factory.vo.ProductQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 产品服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public IPage<Product> getProductPage(ProductQueryVO queryVO) {
        log.debug("分页查询产品列表，查询条件：{}", queryVO);

        // 验证分页参数
        queryVO.validatePageParams();

        Page<Product> page = new Page<>(queryVO.getCurrent(), queryVO.getSize());
        return baseMapper.selectProductPage(page, queryVO);
    }

    @Override
    public Product getProductById(Integer id) {
        log.debug("根据ID查询产品详情，ID：{}", id);

        if (id == null || id <= 0) {
            log.warn("产品ID无效：{}", id);
            return null;
        }

        return baseMapper.selectById(id);
    }

    @Override
    public boolean addProduct(Product product) {
        log.debug("新增产品，产品信息：{}", product);

        // 验证产品数据
        if (!validateProduct(product)) {
            log.warn("产品数据验证失败：{}", product);
            return false;
        }

        return baseMapper.insert(product) > 0;
    }

    @Override
    public boolean updateProduct(Product product) {
        log.debug("修改产品，产品信息：{}", product);

        // 验证产品数据
        if (!validateProduct(product) || product.getId() == null) {
            log.warn("产品数据验证失败：{}", product);
            return false;
        }

        return baseMapper.updateById(product) > 0;
    }

    @Override
    public boolean deleteProduct(Integer id) {
        log.debug("删除产品，ID：{}", id);

        if (id == null || id <= 0) {
            log.warn("产品ID无效：{}", id);
            return false;
        }

        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteProductBatch(List<Integer> ids) {
        log.debug("批量删除产品，IDs：{}", ids);

        if (ids == null || ids.isEmpty()) {
            log.warn("产品ID列表为空");
            return false;
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Product> getAllProducts() {
        log.debug("获取所有产品列表");
        return baseMapper.selectList(null);
    }

    @Override
    public List<Product> getProductsByCategory(String category) {
        log.debug("根据分类查询产品列表，分类：{}", category);

        if (!StringUtils.hasText(category)) {
            log.warn("产品分类为空");
            return List.of();
        }

        return baseMapper.selectByCategory(category);
    }

    @Override
    public List<Product> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        log.debug("根据价格区间查询产品，价格区间：{} - {}", minPrice, maxPrice);

        if (minPrice == null || maxPrice == null || minPrice.compareTo(maxPrice) > 0) {
            log.warn("价格区间参数无效：{} - {}", minPrice, maxPrice);
            return List.of();
        }

        return baseMapper.selectByPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<Map<String, Object>> getProductCategoryStatistics() {
        log.debug("获取产品分类统计");
        return baseMapper.selectCategoryStatistics();
    }

    @Override
    public boolean validateProduct(Product product) {
        if (product == null) {
            return false;
        }

        // 验证必填字段
        if (!StringUtils.hasText(product.getName())) {
            log.warn("产品名称不能为空");
            return false;
        }

        if (!StringUtils.hasText(product.getModel())) {
            log.warn("产品型号不能为空");
            return false;
        }

        if (!StringUtils.hasText(product.getCategory())) {
            log.warn("产品分类不能为空");
            return false;
        }

        if (product.getPrice() == null || product.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("产品价格必须大于0");
            return false;
        }

        return true;
    }
}
