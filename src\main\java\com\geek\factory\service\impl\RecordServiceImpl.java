package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Record;
import com.geek.factory.mapper.RecordMapper;
import com.geek.factory.service.RecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @Description 维修记录服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class RecordServiceImpl extends ServiceImpl<RecordMapper, Record> implements RecordService {
}
