-- 快速修复：为特定生产计划ID添加原料消耗测试数据
-- 这个脚本专门针对日志中显示的生产计划ID: 1941332966093598700

-- 1. 检查表是否存在
SELECT COUNT(*) as table_exists 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'factory' 
    AND TABLE_NAME = 'production_plan_material';

-- 2. 检查生产计划是否存在
SELECT id, plan_code, product_name, status 
FROM production_plan 
WHERE id = 1941332966093598700;

-- 3. 检查可用的原料
SELECT id, code, name, unit, quantity 
FROM materials 
WHERE quantity > 0 
LIMIT 10;

-- 4. 删除可能存在的旧测试数据
DELETE FROM production_plan_material 
WHERE plan_id = 1941332966093598700;

-- 5. 为特定生产计划ID插入测试数据
INSERT INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time) VALUES
(1941332966093598700, 1, 50.00, '片', '硅晶圆用于主板制造', NOW()),
(1941332966093598700, 2, 25.50, 'kg', '铜箔用于电路连接', NOW()),
(1941332966093598700, 3, 15.20, 'kg', '环氧树脂用于绝缘', NOW()),
(1941332966093598700, 7, 200.00, '个', '电容器用于电路', NOW()),
(1941332966093598700, 6, 150.00, '个', '电阻器用于电路', NOW());

-- 如果上面的原料ID不存在，使用动态插入
INSERT IGNORE INTO production_plan_material (plan_id, material_id, required_quantity, unit, remark, create_time)
SELECT 1941332966093598700, m.id, 
    CASE 
        WHEN m.unit = '个' THEN ROUND(RAND() * 100 + 10, 0)
        WHEN m.unit = 'kg' THEN ROUND(RAND() * 50 + 5, 2)
        WHEN m.unit = '片' THEN ROUND(RAND() * 200 + 20, 0)
        WHEN m.unit = 'm' THEN ROUND(RAND() * 100 + 10, 2)
        ELSE ROUND(RAND() * 50 + 5, 2)
    END,
    m.unit,
    CONCAT('用于生产计划 1941332966093598700 - ', m.name),
    NOW()
FROM materials m 
WHERE m.quantity > 0 
LIMIT 5;

-- 6. 验证插入结果
SELECT 
    ppm.id,
    ppm.plan_id,
    ppm.material_id,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 1941332966093598700;

-- 7. 测试API查询（模拟后端SQL）
SELECT 
    ppm.material_id as materialId,
    m.name as materialName,
    ppm.unit,
    ppm.required_quantity as requiredQuantity,
    ppm.remark
FROM production_plan_material ppm
LEFT JOIN materials m ON ppm.material_id = m.id
WHERE ppm.plan_id = 1941332966093598700;

-- 8. 显示总记录数
SELECT COUNT(*) as total_records 
FROM production_plan_material 
WHERE plan_id = 1941332966093598700;

-- 9. 显示成功消息
SELECT '✅ 测试数据插入完成！现在可以测试API了' as status;
