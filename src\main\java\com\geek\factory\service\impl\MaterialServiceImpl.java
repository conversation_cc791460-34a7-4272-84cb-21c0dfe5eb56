package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.entity.Material;
import com.geek.factory.mapper.MaterialMapper;
import com.geek.factory.service.MaterialService;
import com.geek.factory.vo.MaterialQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Description 原料服务实现类
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    @Override
    public IPage<Material> getMaterialPage(MaterialQueryVO queryVO) {
        log.debug("分页查询原料列表，查询条件：{}", queryVO);

        // 验证分页参数
        queryVO.validatePageParams();

        Page<Material> page = new Page<>(queryVO.getCurrent(), queryVO.getSize());
        return baseMapper.selectMaterialPage(page, queryVO);
    }

    @Override
    public Material getMaterialById(Integer id) {
        log.debug("根据ID查询原料详情，ID：{}", id);

        if (id == null || id <= 0) {
            log.warn("原料ID无效：{}", id);
            return null;
        }

        return baseMapper.selectById(id);
    }

    @Override
    public boolean addMaterial(Material material) {
        log.debug("新增原料，原料信息：{}", material);

        // 验证原料数据
        if (!validateMaterial(material)) {
            log.warn("原料数据验证失败：{}", material);
            return false;
        }

        return baseMapper.insert(material) > 0;
    }

    @Override
    public boolean updateMaterial(Material material) {
        log.debug("修改原料，原料信息：{}", material);

        // 验证原料数据
        if (!validateMaterial(material) || material.getId() == null) {
            log.warn("原料数据验证失败：{}", material);
            return false;
        }

        return baseMapper.updateById(material) > 0;
    }

    @Override
    public boolean deleteMaterial(Integer id) {
        log.debug("删除原料，ID：{}", id);

        if (id == null || id <= 0) {
            log.warn("原料ID无效：{}", id);
            return false;
        }

        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteMaterialBatch(List<Integer> ids) {
        log.debug("批量删除原料，IDs：{}", ids);

        if (ids == null || ids.isEmpty()) {
            log.warn("原料ID列表为空");
            return false;
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Material> getAllMaterials() {
        log.debug("获取所有原料列表");
        return baseMapper.selectList(null);
    }

    @Override
    public List<Material> getMaterialsByType(String type) {
        log.debug("根据类型查询原料列表，类型：{}", type);

        if (!StringUtils.hasText(type)) {
            log.warn("原料类型为空");
            return List.of();
        }

        return baseMapper.selectByType(type);
    }

    @Override
    public List<Material> getMaterialsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        log.debug("根据价格区间查询原料，价格区间：{} - {}", minPrice, maxPrice);

        if (minPrice == null || maxPrice == null || minPrice.compareTo(maxPrice) > 0) {
            log.warn("价格区间参数无效：{} - {}", minPrice, maxPrice);
            return List.of();
        }

        return baseMapper.selectByPriceRange(minPrice, maxPrice);
    }

    @Override
    public List<Map<String, Object>> getMaterialTypeStatistics() {
        log.debug("获取原料类型统计");
        return baseMapper.selectTypeStatistics();
    }

    @Override
    public List<Material> getMaterialsByUnit(String unit) {
        log.debug("根据单位查询原料列表，单位：{}", unit);

        if (!StringUtils.hasText(unit)) {
            log.warn("计量单位为空");
            return List.of();
        }

        return baseMapper.selectByUnit(unit);
    }

    @Override
    public boolean validateMaterial(Material material) {
        if (material == null) {
            return false;
        }

        // 验证必填字段
        if (!StringUtils.hasText(material.getName())) {
            log.warn("原料名称不能为空");
            return false;
        }

        if (!StringUtils.hasText(material.getType())) {
            log.warn("原料类型不能为空");
            return false;
        }

        if (!StringUtils.hasText(material.getUnit())) {
            log.warn("计量单位不能为空");
            return false;
        }

        if (material.getPrice() == null || material.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("原料价格必须大于0");
            return false;
        }

        return true;
    }
}
