package com.geek.factory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geek.factory.dto.CreateProductionPlanDTO;
import com.geek.factory.dto.MaterialUsageDTO;
import com.geek.factory.dto.ProductionPlanMaterialDTO;
import com.geek.factory.dto.UpdateProductionPlanDTO;
import com.geek.factory.entity.Material;
import com.geek.factory.entity.ProductionPlan;
import com.geek.factory.entity.ProductionPlanMaterial;
import com.geek.factory.mapper.ProductionPlanMapper;
import com.geek.factory.mapper.ProductionPlanMaterialMapper;
import com.geek.factory.service.MaterialService;
import com.geek.factory.service.PlanCodeGeneratorService;
import com.geek.factory.service.ProductionPlanService;
import com.geek.factory.vo.ProductionPlanVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生产计划服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductionPlanServiceImpl extends ServiceImpl<ProductionPlanMapper, ProductionPlan>
    implements ProductionPlanService {

    private final ProductionPlanMapper productionPlanMapper;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private ProductionPlanMaterialMapper productionPlanMaterialMapper;

    @Autowired
    private PlanCodeGeneratorService planCodeGeneratorService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPlan(CreateProductionPlanDTO dto) {
        log.info("开始创建生产计划，计划编号：{}", dto.getCode());

        ProductionPlan plan = new ProductionPlan();
        BeanUtils.copyProperties(dto, plan);

        // 设置创建时间
        plan.setCreateTime(LocalDateTime.now());

        // 生成有序的计划编号（如果DTO中没有提供）
        if (plan.getPlanCode() == null || plan.getPlanCode().isEmpty()) {
            plan.setPlanCode(planCodeGeneratorService.generateProductionPlanCode());
        }

        // 设置默认状态
        if (plan.getStatus() == null || plan.getStatus().isEmpty()) {
            plan.setStatus("未开始");
        }

        // 设置默认入库状态
        if (plan.getIsInStock() == null) {
            plan.setIsInStock(0); // 默认未入库
        }

        // 保存生产计划
        this.save(plan);
        log.info("生产计划保存成功，计划ID：{}", plan.getId());

        // 扣减原料库存并保存原料关联信息
        if (dto.getMaterials() != null && !dto.getMaterials().isEmpty()) {
            log.info("🔍 开始处理原料信息，原料数量：{}", dto.getMaterials().size());
            log.info("📋 原料详情：{}", dto.getMaterials());

            deductMaterialStock(dto.getMaterials());
            log.info("✅ 原料库存扣减完成");

            // 保存生产计划原料关联信息
            savePlanMaterials(plan.getId(), dto.getMaterials());
            log.info("✅ 生产计划原料关联信息保存完成，计划ID：{}", plan.getId());
        } else {
            log.warn("⚠️ 生产计划没有关联原料，跳过库存扣减和原料关联保存");
            log.warn("📊 DTO原料信息：materials = {}", dto.getMaterials());
        }

        log.info("生产计划创建完成，计划编号：{}", plan.getPlanCode());
    }

    /**
     * 删除生产计划的原料关联信息
     *
     * @param planId 生产计划ID
     */
    private void deletePlanMaterials(Long planId) {
        try {
            LambdaQueryWrapper<ProductionPlanMaterial> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductionPlanMaterial::getPlanId, planId);
            productionPlanMaterialMapper.delete(queryWrapper);
            log.info("删除生产计划原料关联信息成功，计划ID：{}", planId);
        } catch (Exception e) {
            log.error("删除生产计划原料关联信息失败，计划ID：{}", planId, e);
            throw new RuntimeException("删除原料关联信息失败: " + e.getMessage(), e);
        }
    }


    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlan(ProductionPlan plan) {
        // 设置更新时间
        plan.setUpdateTime(LocalDateTime.now());
        this.updateById(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlan(UpdateProductionPlanDTO dto) {
        log.info("开始更新生产计划，计划ID：{}", dto.getId());

        // 更新生产计划基本信息
        ProductionPlan plan = new ProductionPlan();
        BeanUtils.copyProperties(dto, plan);
        plan.setUpdateTime(LocalDateTime.now());

        boolean updateSuccess = this.updateById(plan);
        if (!updateSuccess) {
            throw new RuntimeException("更新生产计划失败");
        }
        log.info("生产计划基本信息更新成功，计划ID：{}", dto.getId());

        // 处理原料更新
        if (dto.getMaterial() != null && !dto.getMaterial().isEmpty()) {
            log.info("开始更新生产计划原料信息，原料数量：{}", dto.getMaterial().size());

            // 1. 先删除原有的原料关联信息
            deletePlanMaterials(dto.getId());
            log.info("删除原有原料关联信息完成");

            // 2. 重新保存新的原料关联信息
            savePlanMaterials(dto.getId(), dto.getMaterial());
            log.info("保存新的原料关联信息完成");
        } else {
            log.info("没有原料信息需要更新");
        }

        log.info("生产计划更新完成，计划ID：{}", dto.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlan(Long id) {
        log.info("开始删除生产计划，计划ID：{}", id);

        // 先删除关联的原料数据
        deletePlanMaterials(id);
        log.info("删除生产计划原料关联信息完成，计划ID：{}", id);

        // 再删除生产计划主记录
        boolean deleteSuccess = this.removeById(id);
        if (!deleteSuccess) {
            throw new RuntimeException("删除生产计划失败，计划ID：" + id);
        }

        log.info("生产计划删除完成，计划ID：{}", id);
    }
    
    @Override
    public Page<ProductionPlanVO> getProductionPlanWithNames(Integer current, Integer size, Map<String, Object> params) {
        Page<ProductionPlanVO> page = new Page<>(current, size);
        // 确保 params 不为 null，避免 SQL 解析错误
        if (params == null) {
            params = new HashMap<>();
        }
        return productionPlanMapper.selectProductionPlanWithNames(page, params);
    }

    @Override
    public ProductionPlanVO getProductionPlanVOById(Long id) {
        return productionPlanMapper.selectProductionPlanVOById(id);
    }
    

    /**
     * 扣减原料库存
     */
    private void deductMaterialStock(List<MaterialUsageDTO> materials) {
        log.info("开始扣减原料库存，原料数量：{}", materials.size());

        try {
            // 先验证库存是否充足
            for (MaterialUsageDTO item : materials) {
                Material dbMaterial = materialService.getById(item.getMaterialId());
                if (dbMaterial == null) {
                    log.error("原料不存在，ID：{}", item.getMaterialId());
                    throw new RuntimeException("原料不存在: ID=" + item.getMaterialId());
                }
                if (dbMaterial.getQuantity().compareTo(item.getRequiredQuantity()) < 0) {
                    log.error("原料库存不足，原料：{}，当前库存：{}，需要：{}",
                            dbMaterial.getName(), dbMaterial.getQuantity(), item.getRequiredQuantity());
                    throw new RuntimeException("原料 " + dbMaterial.getName() + " 库存不足，无法创建生产计划");
                }
            }

            // 执行库存扣减
            for (MaterialUsageDTO item : materials) {
                Material dbMaterial = materialService.getById(item.getMaterialId());
                dbMaterial.setQuantity(dbMaterial.getQuantity().subtract(item.getRequiredQuantity()));
                boolean updateSuccess = materialService.updateById(dbMaterial);
                if (!updateSuccess) {
                    log.error("更新原料库存失败，原料ID：{}", item.getMaterialId());
                    throw new RuntimeException("更新原料库存失败");
                }
                log.info("成功扣减原料库存，原料：{}，扣减数量：{}，剩余库存：{}",
                        dbMaterial.getName(), item.getRequiredQuantity(), dbMaterial.getQuantity());
            }

            log.info("所有原料库存扣减完成");
        } catch (Exception e) {
            log.error("库存扣减失败", e);
            throw new RuntimeException("库存扣减失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存生产计划原料关联信息
     */
    private void savePlanMaterials(Long planId, List<MaterialUsageDTO> materials) {
        log.info("开始保存生产计划原料关联信息，计划ID：{}，原料数量：{}", planId, materials.size());

        try {
            for (MaterialUsageDTO item : materials) {
                ProductionPlanMaterial planMaterial = new ProductionPlanMaterial();
                planMaterial.setPlanId(planId);
                planMaterial.setMaterialId(item.getMaterialId());
                planMaterial.setRequiredQuantity(item.getRequiredQuantity());
                planMaterial.setUnit(item.getUnit());
                planMaterial.setRemark(item.getRemark());
                planMaterial.setCreateTime(LocalDateTime.now());

                productionPlanMaterialMapper.insert(planMaterial);
                log.debug("保存原料关联信息成功，原料ID：{}，数量：{}", item.getMaterialId(), item.getRequiredQuantity());
            }
            log.info("生产计划原料关联信息保存完成");
        } catch (Exception e) {
            log.error("保存生产计划原料关联信息失败", e);
            throw new RuntimeException("保存原料关联信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ProductionPlanMaterialDTO> getPlanMaterialDetails(Long planId) {
        log.info("查询生产计划原料消耗详情，计划ID：{}", planId);

        try {
            List<ProductionPlanMaterialDTO> materials = productionPlanMaterialMapper.selectPlanMaterialDetails(planId);
            log.info("查询生产计划原料消耗详情成功，计划ID：{}，原料数量：{}", planId, materials.size());
            return materials;
        } catch (Exception e) {
            log.error("查询生产计划原料消耗详情失败，计划ID：{}", planId, e);
            throw new RuntimeException("查询原料消耗详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ProductionPlanMaterialDTO> getPlanMaterialDetailsByCode(String planCode) {
        log.info("查询生产计划原料消耗详情，计划编码：{}", planCode);

        try {
            List<ProductionPlanMaterialDTO> materials = productionPlanMaterialMapper.selectPlanMaterialDetailsByCode(planCode);
            log.info("查询生产计划原料消耗详情成功，计划编码：{}，原料数量：{}", planCode, materials.size());
            return materials;
        } catch (Exception e) {
            log.error("查询生产计划原料消耗详情失败，计划编码：{}", planCode, e);
            throw new RuntimeException("查询原料消耗详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int fixExistingPlanCodes() {
        log.info("开始修复现有生产计划的编号");

        // 查询所有没有计划编号的生产计划
        List<ProductionPlan> plansWithoutCode = this.lambdaQuery()
                .isNull(ProductionPlan::getPlanCode)
                .or()
                .eq(ProductionPlan::getPlanCode, "")
                .orderByAsc(ProductionPlan::getCreateTime)
                .list();

        if (plansWithoutCode.isEmpty()) {
            log.info("没有需要修复的生产计划");
            return 0;
        }

        log.info("找到 {} 条需要修复的生产计划", plansWithoutCode.size());

        int fixedCount = 0;
        for (ProductionPlan plan : plansWithoutCode) {
            try {
                // 为每个计划生成编号
                String planCode = planCodeGeneratorService.generateProductionPlanCode();
                plan.setPlanCode(planCode);
                this.updateById(plan);
                fixedCount++;
                log.info("修复计划ID: {}, 新编号: {}", plan.getId(), planCode);
            } catch (Exception e) {
                log.error("修复计划ID: {} 失败: {}", plan.getId(), e.getMessage());
            }
        }

        log.info("生产计划编号修复完成，共修复 {} 条记录", fixedCount);
        return fixedCount;
    }
}
