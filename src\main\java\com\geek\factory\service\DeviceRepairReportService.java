package com.geek.factory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.dto.DeviceRepairReportDTO;
import com.geek.factory.dto.DeviceRepairReportQueryDTO;
import com.geek.factory.entity.DeviceRepairReport;

import java.util.List;

/**
 * 设备维修上报服务接口
 */
public interface DeviceRepairReportService extends IService<DeviceRepairReport> {

    /**
     * 分页查询设备维修上报记录
     */
    IPage<DeviceRepairReportDTO> getPageList(DeviceRepairReportQueryDTO queryDTO);

    /**
     * 创建维修上报记录
     */
    boolean createReport(DeviceRepairReport report);

    /**
     * 更新维修上报记录
     */
    boolean updateReport(DeviceRepairReport report);

    /**
     * 根据ID获取维修上报详情
     */
    DeviceRepairReportDTO getReportDetail(Long id);

    /**
     * 根据产线ID查询维修上报记录
     */
    List<DeviceRepairReportDTO> getReportsByProductLineId(Long productLineId);

    /**
     * 根据状态查询维修上报记录
     */
    List<DeviceRepairReportDTO> getReportsByStatus(String status);

    /**
     * 根据设备名称查询维修上报记录
     */
    List<DeviceRepairReportDTO> getReportsByDeviceName(String deviceName);

    /**
     * 根据提交人查询维修上报记录
     */
    List<DeviceRepairReportDTO> getReportsBySubmitter(String submitter);

    /**
     * 统计各状态的维修上报数量
     */
    List<Object> getStatusStatistics();

    /**
     * 删除维修上报记录
     */
    boolean deleteReport(Long id);

    /**
     * 批量删除维修上报记录
     */
    boolean deleteReports(List<Long> ids);

    /**
     * 更新维修上报状态
     */
    boolean updateReportStatus(Long id, String status);

    /**
     * 获取所有维修上报记录（不分页）
     */
    List<DeviceRepairReportDTO> getAllReports();

    /**
     * 分页查询设备维修报修单（带产线名称）
     */
    IPage<DeviceRepairReportDTO> getPagedReports(int page, int size);

    /**
     * 根据ID查询某一条报修单（带产线名称）
     */
    DeviceRepairReportDTO getReportByIdWithLineName(Long id);
}
