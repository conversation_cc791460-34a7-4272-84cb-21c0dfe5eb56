-- 修复生产计划表结构和ID生成问题
-- 解决雪花算法ID问题，改为使用AUTO_INCREMENT

USE factory;

-- 1. 检查当前production_plan表结构
DESCRIBE production_plan;

-- 2. 检查当前表的AUTO_INCREMENT设置
SHOW CREATE TABLE production_plan;

-- 3. 如果表不存在，创建production_plan表
CREATE TABLE IF NOT EXISTS `production_plan` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_code` varchar(50) NOT NULL COMMENT '计划编号',
  `product_id` bigint DEFAULT NULL COMMENT '产品ID',
  `product_line_id` bigint DEFAULT NULL COMMENT '产线ID',
  `quantity` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '计划数量',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `complete_time` datetime DEFAULT NULL COMMENT '计划完成时间',
  `status` varchar(20) DEFAULT '未开始' COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_in_stock` int DEFAULT '0' COMMENT '是否入库 (0-未入库, 1-已入库)',
  `priority` int DEFAULT '2' COMMENT '优先级 (1-高, 2-中, 3-低)',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_plan_code` (`plan_code`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_product_line_id` (`product_line_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产计划表';

-- 4. 如果表已存在但ID不是AUTO_INCREMENT，需要修改
-- 注意：这个操作会重置AUTO_INCREMENT值，请谨慎使用
-- ALTER TABLE production_plan MODIFY COLUMN id bigint NOT NULL AUTO_INCREMENT;

-- 5. 重置AUTO_INCREMENT起始值（可选，如果需要从1开始）
-- ALTER TABLE production_plan AUTO_INCREMENT = 1;

-- 6. 检查修复后的表结构
SHOW CREATE TABLE production_plan;

-- 7. 显示当前表中的数据统计
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id,
    AVG(id) as avg_id
FROM production_plan;

-- 8. 显示最近的几条记录
SELECT 
    id, 
    plan_code, 
    status, 
    create_time 
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 10;

-- 9. 如果需要清理雪花算法生成的大ID数据（谨慎操作！）
-- 建议先备份数据
-- CREATE TABLE production_plan_backup AS SELECT * FROM production_plan;

-- 然后可以选择性删除或更新数据
-- DELETE FROM production_plan WHERE id > 1000000000000000000;

SELECT '✅ 生产计划表结构检查和修复完成！' as status;
