package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.geek.factory.constant.MessageConstant;
import com.geek.factory.entity.Material;
import com.geek.factory.result.Result;
import com.geek.factory.service.MaterialService;
import com.geek.factory.vo.query.MaterialQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 原料管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/material")
@Api(tags = "原料管理")
@CrossOrigin
public class MaterialController {

    @Autowired
    private MaterialService materialService;

    @ApiOperation("分页查询原料列表")
    @PostMapping("/page")
    public Result getMaterialPage(@RequestBody MaterialQueryVO queryVO) {
        log.info("分页查询原料列表，查询条件：{}", queryVO);
        try {
            IPage<Material> page = materialService.selectMaterialPage(queryVO);
            return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, page);
        } catch (Exception e) {
            log.error("分页查询原料列表失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("根据ID查询原料详情")
    @GetMapping("/{id}")
    public Result getMaterialById(@PathVariable Integer id) {
        log.info("根据ID查询原料详情，ID：{}", id);
        try {
            Material material = materialService.getById(id);
            if (material != null) {
                return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, material);
            } else {
                return new Result(false, MessageConstant.MATERIAL_NOT_FOUND, null);
            }
        } catch (Exception e) {
            log.error("根据ID查询原料详情失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("新增原料")
    @PostMapping("/add")
    public Result addMaterial(@RequestBody Material material) {
        log.info("新增原料，原料信息：{}", material);
        try {
            boolean success = materialService.addMaterial(material);
            if (success) {
                return new Result(true, MessageConstant.ADD_MATERIAL_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.ADD_MATERIAL_FAILED, null);
            }
        } catch (Exception e) {
            log.error("新增原料失败", e);
            return new Result(false, MessageConstant.ADD_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("更新原料")
    @PostMapping("/update")
    public Result updateMaterial(@RequestBody Material material) {
        log.info("更新原料，原料信息：{}", material);
        try {
            boolean success = materialService.updateMaterial(material);
            if (success) {
                return new Result(true, MessageConstant.UPDATE_MATERIAL_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.UPDATE_MATERIAL_FAILED, null);
            }
        } catch (Exception e) {
            log.error("更新原料失败", e);
            return new Result(false, MessageConstant.UPDATE_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("删除原料")
    @DeleteMapping("/{id}")
    public Result deleteMaterial(@PathVariable Integer id) {
        log.info("删除原料，ID：{}", id);
        try {
            boolean success = materialService.deleteMaterial(id);
            if (success) {
                return new Result(true, MessageConstant.DELETE_MATERIAL_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.DELETE_MATERIAL_FAILED, null);
            }
        } catch (Exception e) {
            log.error("删除原料失败", e);
            return new Result(false, MessageConstant.DELETE_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("批量删除原料")
    @DeleteMapping("/batch")
    public Result deleteMaterialBatch(@RequestBody List<Integer> ids) {
        log.info("批量删除原料，IDs：{}", ids);
        try {
            boolean success = materialService.deleteMaterialBatch(ids);
            if (success) {
                return new Result(true, MessageConstant.DELETE_MATERIAL_SUCCESS, null);
            } else {
                return new Result(false, MessageConstant.DELETE_MATERIAL_FAILED, null);
            }
        } catch (Exception e) {
            log.error("批量删除原料失败", e);
            return new Result(false, MessageConstant.DELETE_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("根据类型查询原料列表")
    @GetMapping("/type/{type}")
    public Result getMaterialsByType(@PathVariable String type) {
        log.info("根据类型查询原料列表，类型：{}", type);
        try {
            List<Material> materials = materialService.getMaterialsByType(type);
            return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, materials);
        } catch (Exception e) {
            log.error("根据类型查询原料列表失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("根据价格区间查询原料")
    @GetMapping("/price")
    public Result getMaterialsByPriceRange(@RequestParam Double minPrice, @RequestParam Double maxPrice) {
        log.info("根据价格区间查询原料，价格区间：{} - {}", minPrice, maxPrice);
        try {
            List<Material> materials = materialService.getMaterialsByPriceRange(minPrice, maxPrice);
            return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, materials);
        } catch (Exception e) {
            log.error("根据价格区间查询原料失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }

    @ApiOperation("获取原料类型统计")
    @GetMapping("/statistics/type")
    public Result getMaterialTypeStatistics() {
        log.info("获取原料类型统计");
        try {
            List<Material> statistics = materialService.getMaterialTypeStatistics();
            return new Result(true, MessageConstant.QUERY_MATERIAL_SUCCESS, statistics);
        } catch (Exception e) {
            log.error("获取原料类型统计失败", e);
            return new Result(false, MessageConstant.QUERY_MATERIAL_FAILED, null);
        }
    }
}
