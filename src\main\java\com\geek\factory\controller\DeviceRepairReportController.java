package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.constant.SuccessConstant;
import com.geek.factory.dto.DeviceRepairReportDTO;
import com.geek.factory.dto.DeviceRepairReportQueryDTO;
import com.geek.factory.entity.DeviceRepairReport;
import com.geek.factory.result.Result;
import com.geek.factory.service.DeviceRepairReportService;
import com.geek.factory.service.impl.DeviceRepairReportServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 设备维修上报控制器
 */
@RestController
@RequestMapping("/api/device-repair-report")
@Api(tags = "设备维修上报管理")
public class DeviceRepairReportController {

    @Autowired
    private DeviceRepairReportServiceImpl reportService;

    @PostMapping("/add")
    @ApiOperation("新增维修上报")
    public Result add(@RequestBody DeviceRepairReport report) {
        try {
            boolean success = reportService.save(report);
            return new Result(success ? SuccessConstant.SUCCESS : SuccessConstant.FAIL,
                    success ? "上报成功" : "上报失败");
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "上报失败：" + e.getMessage());
        }
    }

    @PutMapping("/update")
    @ApiOperation("更新维修上报")
    public Result update(@RequestBody DeviceRepairReport report) {
        try {
            boolean success = reportService.updateById(report);
            return new Result(success ? SuccessConstant.SUCCESS : SuccessConstant.FAIL,
                    success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "更新失败：" + e.getMessage());
        }
    }

    @GetMapping("/list")
    @ApiOperation("分页查询维修上报列表")
    public Result list(DeviceRepairReportQueryDTO queryDTO) {
        try {
            IPage<DeviceRepairReportDTO> result = reportService.getPageList(queryDTO);
            return new Result(SuccessConstant.SUCCESS, "查询成功", result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/page")
    @ApiOperation("分页查询维修上报列表（前端专用）")
    public Result page(DeviceRepairReportQueryDTO queryDTO) {
        try {
            IPage<DeviceRepairReportDTO> result = reportService.getPageList(queryDTO);
            return new Result(SuccessConstant.SUCCESS, "查询成功", result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/all")
    @ApiOperation("获取所有维修上报记录")
    public Result all() {
        try {
            List<DeviceRepairReport> list = reportService.list();
            return new Result(SuccessConstant.SUCCESS, "查询成功", list);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "查询失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除维修上报")
    public Result delete(@PathVariable Long id) {
        try {
            boolean success = reportService.removeById(id);
            return new Result(success ? SuccessConstant.SUCCESS : SuccessConstant.FAIL,
                    success ? "删除成功" : "删除失败");
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "删除失败：" + e.getMessage());
        }
    }

    @GetMapping("/get/{id}")
    @ApiOperation("根据ID获取维修上报详情")
    public Result get(@PathVariable Long id) {
        try {
            DeviceRepairReport report = reportService.getMapper().selectDetailById(id);
            return report != null ?
                    new Result(SuccessConstant.SUCCESS, "查询成功", report) :
                    new Result(SuccessConstant.FAIL, "记录不存在");
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "查询失败：" + e.getMessage());
        }
    }

    @PutMapping("/status/{id}")
    @ApiOperation("更新维修上报状态")
    public Result updateStatus(@PathVariable Long id, @RequestParam String status) {
        try {
            DeviceRepairReport report = new DeviceRepairReport();
            report.setId(id);
            report.setStatus(status);
            boolean success = reportService.updateById(report);
            return new Result(success ? SuccessConstant.SUCCESS : SuccessConstant.FAIL,
                    success ? "状态更新成功" : "状态更新失败");
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "状态更新失败：" + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    @ApiOperation("获取维修上报统计信息")
    public Result statistics() {
        try {
            List<Object> stats = reportService.getMapper().countByStatus();
            return new Result(SuccessConstant.SUCCESS, "查询成功", stats);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(SuccessConstant.FAIL, "查询失败：" + e.getMessage());
        }
    }
}
