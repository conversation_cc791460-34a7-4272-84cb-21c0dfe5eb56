<template>
  <div class="production-plan-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="page-title-section">
            <el-icon class="title-icon" size="28"><DocumentAdd /></el-icon>
            <div class="title-text">
              <h1 class="page-title">新增生产计划</h1>
              <p class="page-subtitle">Create New Production Plan</p>
            </div>
          </div>
        </div>

        <div class="header-actions">
          <el-button
            size="large"
            @click="handleCancel"
            class="cancel-btn"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回列表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单卡片 -->
    <div class="form-section">
      <div class="form-card">
        <div class="form-header">
          <div class="form-title">
            <el-icon class="form-icon"><Edit /></el-icon>
            <span>基本信息</span>
          </div>
          <div class="form-subtitle">请填写生产计划的详细信息</div>
        </div>

        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
          class="modern-form"
          label-position="top"
        >
          <!-- 基础信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Document /></el-icon>
              <span>基础信息</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="6">
                <el-form-item label="计划编号" prop="code">
                  <el-input
                    v-model="formData.code"
                    placeholder="正在生成计划编号..."
                    readonly
                    prefix-icon="DocumentCopy"
                    size="large"
                    style="background-color: #f5f7fa;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="产线ID">
                  <el-input
                    v-model="formData.productLineId"
                    placeholder="选择产线后自动填充"
                    readonly
                    size="large"
                    prefix-icon="Key"
                    style="background-color: #f5f7fa;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="产线选择" prop="productLineId">
                  <el-select
                    v-model="formData.productLineId"
                    placeholder="请选择产线"
                    style="width: 100%"
                    size="large"
                    filterable
                    @change="handleProductLineChange"
                  >
                    <el-option
                      v-for="line in productionLineOptions"
                      :key="line.id"
                      :label="`${line.name} (${line.code})`"
                      :value="line.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="产线名称">
                  <el-input
                    v-model="formData.productLineName"
                    placeholder="选择产线后自动填充"
                    readonly
                    size="large"
                    prefix-icon="Factory"
                    style="background-color: #f5f7fa;"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 产品信息区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Box /></el-icon>
              <span>产品信息</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="6">
                <el-form-item label="产品ID">
                  <el-input
                    v-model="formData.productId"
                    placeholder="选择产品后自动填充"
                    readonly
                    size="large"
                    prefix-icon="Key"
                    style="background-color: #f5f7fa;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="产品选择" prop="productId">
                  <el-select
                    v-model="formData.productId"
                    placeholder="请选择产品"
                    style="width: 100%"
                    size="large"
                    filterable
                    @change="handleProductChange"
                  >
                    <el-option
                      v-for="product in productOptions"
                      :key="product.id"
                      :label="`${product.name} (ID: ${product.id})`"
                      :value="product.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="产品名称">
                  <el-input
                    v-model="formData.productName"
                    placeholder="选择产品后自动填充"
                    readonly
                    size="large"
                    prefix-icon="Box"
                    style="background-color: #f5f7fa;"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="6">
                <el-form-item label="生产数量" prop="quantity">
                  <el-input-number
                    v-model="formData.quantity"
                    :min="1"
                    placeholder="请输入生产数量"
                    style="width: 100%"
                    size="large"
                    controls-position="right"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 时间安排区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Calendar /></el-icon>
              <span>时间安排</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="计划开始时间" prop="planStartTime">
                  <el-date-picker
                    v-model="formData.planStartTime"
                    type="datetime"
                    placeholder="选择计划开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                    size="large"
                    prefix-icon="Calendar"
                    @change="handleStartTimeChange"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="计划结束时间" prop="completeTime">
                  <el-date-picker
                    v-model="formData.completeTime"
                    type="datetime"
                    placeholder="选择计划结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                    size="large"
                    prefix-icon="Calendar"
                    :disabled-date="disabledEndDate"
                    :disabled-hours="disabledEndHours"
                    :disabled-minutes="disabledEndMinutes"
                    :disabled-seconds="disabledEndSeconds"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 状态设置区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Setting /></el-icon>
              <span>状态设置</span>
            </div>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="计划状态" prop="status">
                  <el-select
                    v-model="formData.status"
                    placeholder="请选择计划状态"
                    style="width: 100%"
                    size="large"
                  >
                    <el-option label="未开始" :value="0">
                      <div class="status-option">
                        <el-tag type="info" size="small">未开始</el-tag>
                        <span class="option-desc">计划尚未开始执行</span>
                      </div>
                    </el-option>
                    <el-option label="进行中" :value="1">
                      <div class="status-option">
                        <el-tag type="success" size="small">进行中</el-tag>
                        <span class="option-desc">计划正在执行中</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 原料配置区域 -->
          <div class="form-section-group">
            <div class="section-title">
              <el-icon class="section-icon"><Box /></el-icon>
              <span>原料配置</span>
            </div>

            <div class="materials-section">
              <div class="materials-header">
                <el-button
                  type="primary"
                  :icon="Plus"
                  @click="showMaterialDialog = true"
                  size="large"
                >
                  添加原料
                </el-button>
              </div>

              <el-table
                :data="formData.material"
                border
                size="small"
                style="width: 100%; margin-top: 16px"
                v-if="formData.material.length > 0"
                class="materials-table"
              >
                <el-table-column label="原料名称" min-width="120" show-overflow-tooltip>
                  <template #default="{ row }">
                    {{ getMaterialNameById(row.materialId) }}
                  </template>
                </el-table-column>
                <el-table-column prop="requiredQuantity" label="数量" width="100">
                  <template #default="{ row, $index }">
                    <el-input-number
                      v-model="row.requiredQuantity"
                      :min="0"
                      :precision="2"
                      size="small"
                      :controls="false"
                      style="width: 100%"
                      @change="updateMaterialQuantity($index, $event)"
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="unit" label="单位" width="60" />
                <el-table-column label="操作" width="70" fixed="right">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Delete"
                      @click="removeMaterial($index)"
                      circle
                    />
                  </template>
                </el-table-column>
              </el-table>

              <el-empty
                v-if="formData.material.length === 0"
                description="暂无原料配置，请点击添加原料按钮"
                :image-size="100"
              />
            </div>
          </div>



          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="loading"
              size="large"
              class="submit-btn"
            >
              <el-icon v-if="!loading"><Check /></el-icon>
              {{ loading ? '保存中...' : '保存计划' }}
            </el-button>
            <el-button
              @click="handleCancel"
              size="large"
              class="cancel-btn"
            >
              <el-icon><Close /></el-icon>
              取消
            </el-button>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 原料选择对话框 -->
    <el-dialog
      v-model="showMaterialDialog"
      title="选择原料"
      width="900px"
      :close-on-click-modal="false"
    >
      <div class="material-dialog-content">
        <el-table
          :data="availableMaterials"
          border
          style="width: 100%"
          @selection-change="handleMaterialSelection"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="原料名称" />
          <el-table-column prop="type" label="类型" />
          <el-table-column prop="unit" label="单位" />
          <el-table-column prop="price" label="单价" />
          <el-table-column label="库存状态" width="120">
            <template #default="{ row }">
              <el-tag
                :type="getStockStatusType(row.quantity)"
                size="small"
              >
                {{ row.quantity || 0 }} {{ row.unit }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="需求数量" width="150">
            <template #default="{ row }">
              <el-input-number
                v-model="row.tempQuantity"
                :min="0"
                :precision="2"
                size="small"
                style="width: 100%"
                placeholder="请输入数量"
                @change="updateTempQuantity(row)"
              />
            </template>
          </el-table-column>

        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMaterialDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmMaterialSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  DocumentAdd,
  ArrowLeft,
  Edit,
  Document,
  Box,
  Calendar,
  Setting,
  Check,
  Close,
  Plus,
  Delete
} from "@element-plus/icons-vue";
import { createProductionPlan, getNextPlanCode, getProductionPlanList, type CreateProductionPlanDTO, type MaterialUsageForPlan } from "@/api/production";
import { getAllMaterials, validateMaterialStock, type Material, type MaterialWithTemp } from "@/api/material";
import { getAllProducts, type Product } from "@/api/product";
import { getProductionLineList, type ProductionLine } from "@/api/device";

const router = useRouter();
const formRef = ref();
const loading = ref(false);

// 产品选择相关
const productOptions = ref<Product[]>([]);

// 产线选择相关
const productionLineOptions = ref<ProductionLine[]>([]);

// 原料相关状态
const showMaterialDialog = ref(false);
const availableMaterials = ref<MaterialWithTemp[]>([]);
const selectedMaterials = ref<MaterialWithTemp[]>([]);

// 表单数据（匹配后端字段结构）
const formData = reactive<CreateProductionPlanDTO>({
  productId: 0,
  productName: "",
  code: "", // 后端自动生成有序编号
  productLineId: 0,
  productLineName: "",
  quantity: 1,
  planStartTime: "",
  completeTime: "",  // 改为completeTime
  status: 0, // 默认为未开始
  material: []
});

// 编号由后端自动生成，确保有序性
// 格式：PLAN-YYYYMMDD-XX (XX为当天的顺序编号，从01开始)

// 处理产品选择变化
const handleProductChange = (productId: number) => {
  const selectedProduct = productOptions.value.find(p => p.id === productId);
  if (selectedProduct) {
    formData.productId = selectedProduct.id;
    formData.productName = selectedProduct.name;
  }
};

// 处理产线选择变化
const handleProductLineChange = (productLineId: number) => {
  const selectedLine = productionLineOptions.value.find(line => line.id === productLineId);
  if (selectedLine) {
    formData.productLineId = selectedLine.id;
    formData.productLineName = selectedLine.name;
  }
};

// 根据materialId获取原料名称
const getMaterialNameById = (materialId: number): string => {
  const material = availableMaterials.value.find(m => m.id === materialId);
  return material ? material.name : `原料ID: ${materialId}`;
};

// 表单验证规则（匹配新的字段结构）
const rules = {
  productId: [
    { required: true, message: "请选择产品", trigger: "change" }
  ],
  productLineId: [
    { required: true, message: "请选择产线", trigger: "change" }
  ],
  quantity: [
    { required: true, message: "请输入生产数量", trigger: "blur" }
  ],
  planStartTime: [
    { required: true, message: "请选择计划开始时间", trigger: "blur" }
  ],
  completeTime: [  // 改为completeTime
    { required: true, message: "请选择计划结束时间", trigger: "blur" },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value && formData.planStartTime) {
          if (new Date(value) <= new Date(formData.planStartTime)) {
            callback(new Error("完成时间必须晚于开始时间"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  status: [
    { required: true, message: "请选择计划状态", trigger: "change" }
  ]
};

// 验证原料库存
const validateStock = async () => {
  if (!formData.material || formData.material.length === 0) {
    ElMessage.warning('请先添加原料');
    return false;
  }

  try {
    const res = await validateMaterialStock(formData.material);
    if (!res.success) {
      ElMessage.error(res.msg || '库存验证失败');
      return false;
    }
    return true;
  } catch (error) {
    console.error('库存验证失败:', error);
    ElMessage.error('库存验证失败，请稍后重试');
    return false;
  }
};

// 格式化时间为后端期望的格式 (yyyy-MM-dd HH:mm:ss)
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return "";

  // 如果是 ISO 格式 (包含T)，转换为空格格式
  if (dateTimeStr.includes('T')) {
    return dateTimeStr.replace('T', ' ');
  }

  // 如果已经是空格格式，直接返回
  if (dateTimeStr.includes(' ')) {
    return dateTimeStr;
  }

  return dateTimeStr;
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 验证库存
    const stockValid = await validateStock();
    if (!stockValid) return;

    loading.value = true;

    // 创建提交数据，确保时间格式正确
    const submitData = {
      ...formData,
      planStartTime: formatDateTime(formData.planStartTime),
      completeTime: formatDateTime(formData.completeTime)
    };

    console.log("🚀 提交数据详情:", submitData);
    console.log("📊 状态值:", submitData.status, "类型:", typeof submitData.status);

    await createProductionPlan(submitData);
    ElMessage.success("创建成功");
    router.push("/production/plan/list");
  } catch (error) {
    console.error("创建失败:", error);
    ElMessage.error("创建失败");
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  router.back();
};

// 原料相关方法
const loadMaterials = async () => {
  try {
    const response = await getAllMaterials();
    if (response.success) {
      // 后端返回的是分页对象，需要取records字段
      let materialList = [];
      if (response.data && response.data.records) {
        // 分页数据格式
        materialList = response.data.records;
      } else if (Array.isArray(response.data)) {
        // 直接数组格式
        materialList = response.data;
      } else {
        materialList = [];
      }

      // 为每个原料添加临时字段用于在对话框中输入数量
      availableMaterials.value = materialList.map((material: any) => ({
        ...material,
        tempQuantity: 1 // 默认数量为1
      }));
      console.log('加载原料列表成功:', availableMaterials.value);
    } else {
      console.error('加载原料列表失败:', response.msg);
      ElMessage.error(response.msg || '加载原料列表失败');
    }
  } catch (error) {
    console.error('加载原料列表失败:', error);
    ElMessage.error('加载原料列表失败');
  }
};

const handleMaterialSelection = (selection: MaterialWithTemp[]) => {
  selectedMaterials.value = selection;
  console.log('选中的原料:', selection.length, selection);
};

// 更新临时数量
const updateTempQuantity = (row: any) => {
  // 确保数量不小于0
  if (row.tempQuantity < 0) {
    row.tempQuantity = 0;
  }
};



const confirmMaterialSelection = async () => {
  // 验证选中的原料是否都有有效的数量
  const invalidMaterials = selectedMaterials.value.filter(material =>
    !material.tempQuantity || material.tempQuantity <= 0
  );

  if (invalidMaterials.length > 0) {
    ElMessage.warning('请为所有选中的原料设置有效的数量');
    return;
  }

  // 准备库存验证数据
  const materialsToValidate = selectedMaterials.value.map(material => ({
    materialId: Number(material.id!), // 确保是数字类型
    materialName: material.name,
    requiredQuantity: Number(material.tempQuantity || 1), // 确保是数字类型
    unit: material.unit
  }));

  // 验证库存
  try {
    console.log('发送库存验证请求:', materialsToValidate);
    const stockRes = await validateMaterialStock(materialsToValidate);
    console.log('库存验证响应:', stockRes);

    if (!stockRes || !stockRes.success) {
      const errorMsg = stockRes?.msg || '库存验证失败';
      console.warn('库存验证未通过:', errorMsg);
      ElMessage.error(`库存验证失败：${errorMsg}`);
      return;
    }
    console.log('库存验证通过');
  } catch (error: any) {
    console.error('库存验证请求失败:', error);

    // 检查具体的错误类型
    if (error?.response) {
      const status = error.response.status;
      const data = error.response.data;
      console.error('HTTP错误 - 状态码:', status);
      console.error('HTTP错误 - 响应数据:', data);

      if (status === 404) {
        ElMessage.error('库存验证接口不存在，请联系管理员');
      } else if (status === 500) {
        ElMessage.error('服务器内部错误，请稍后重试');
      } else {
        ElMessage.error(`库存验证失败：${data?.msg || data?.message || '服务器错误'}`);
      }
    } else if (error?.request) {
      console.error('网络请求失败:', error.request);
      ElMessage.error('网络连接失败，请检查网络连接');
    } else {
      console.error('请求配置错误:', error.message);
      ElMessage.error(`请求错误：${error.message}`);
    }
    return;
  }

  // 记录成功添加的原料数量
  let addedCount = 0;
  let updatedCount = 0;

  selectedMaterials.value.forEach(material => {
    // 检查是否已经添加过该原料
    const exists = formData.material.find(m => m.materialId === material.id);
    if (!exists) {
      formData.material.push({
        materialId: material.id!,
        requiredQuantity: material.tempQuantity || 1,
        unit: material.unit
      });
      addedCount++;
    } else {
      // 如果已存在，更新数量
      exists.requiredQuantity = material.tempQuantity || 1;
      updatedCount++;
    }
  });

  // 显示成功消息
  if (addedCount > 0 && updatedCount > 0) {
    ElMessage.success(`库存验证通过！成功添加 ${addedCount} 个原料，更新 ${updatedCount} 个原料`);
  } else if (addedCount > 0) {
    ElMessage.success(`库存验证通过！成功添加 ${addedCount} 个原料`);
  } else if (updatedCount > 0) {
    ElMessage.success(`库存验证通过！成功更新 ${updatedCount} 个原料`);
  } else {
    ElMessage.info('未选择任何原料');
  }

  // 关闭对话框并清空选择
  showMaterialDialog.value = false;
  selectedMaterials.value = [];
};

const updateMaterialQuantity = (index: number, value: number) => {
  formData.material[index].requiredQuantity = value;
};

const removeMaterial = (index: number) => {
  formData.material.splice(index, 1);
};

// 获取库存状态类型
const getStockStatusType = (quantity: number) => {
  if (!quantity || quantity <= 0) {
    return 'danger';   // 无库存 - 红色
  } else if (quantity <= 10) {
    return 'warning';  // 库存不足 - 橙色
  } else {
    return 'success';  // 库存充足 - 绿色
  }
};

// 获取产品列表
const loadProducts = async () => {
  try {
    const res = await getAllProducts();
    if (res.success) {
      productOptions.value = res.data || [];
    } else {
      console.error('获取产品列表失败:', res.msg);
      ElMessage.error('获取产品列表失败');
    }
  } catch (error) {
    console.error('获取产品列表异常:', error);
    ElMessage.error('获取产品列表失败');
  }
};

// 获取产线列表
const loadProductionLines = async () => {
  try {
    const res = await getProductionLineList();
    if (res.success) {
      productionLineOptions.value = res.data || [];
    } else {
      console.error('获取产线列表失败:', res.msg);
      ElMessage.error('获取产线列表失败');
    }
  } catch (error) {
    console.error('获取产线列表异常:', error);
    ElMessage.error('获取产线列表失败');
  }
};

// 时间验证逻辑
const handleStartTimeChange = (value: string) => {
  if (value && formData.completeTime) {  // 改为completeTime
    // 如果完成时间早于开始时间，清空完成时间
    if (new Date(formData.completeTime) <= new Date(value)) {
      formData.completeTime = "";  // 改为completeTime
      ElMessage.warning("完成时间必须晚于开始时间，已清空完成时间，请重新选择");
    }
  }
};

// 禁用完成日期（不能早于开始日期）
const disabledEndDate = (time: Date) => {
  if (!formData.planStartTime) return false;
  const startDate = new Date(formData.planStartTime);
  return time.getTime() < startDate.getTime() - 24 * 60 * 60 * 1000; // 允许同一天
};

// 禁用完成时间的小时
const disabledEndHours = () => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天，禁用早于开始时间的小时
  if (startDate.toDateString() === endDate.toDateString()) {
    const disabledHours = [];
    for (let i = 0; i < startDate.getHours(); i++) {
      disabledHours.push(i);
    }
    return disabledHours;
  }
  return [];
};

// 禁用完成时间的分钟
const disabledEndMinutes = (hour: number) => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天且同一小时，禁用早于开始时间的分钟
  if (startDate.toDateString() === endDate.toDateString() && hour === startDate.getHours()) {
    const disabledMinutes = [];
    for (let i = 0; i < startDate.getMinutes(); i++) {
      disabledMinutes.push(i);
    }
    return disabledMinutes;
  }
  return [];
};

// 禁用完成时间的秒
const disabledEndSeconds = (hour: number, minute: number) => {
  if (!formData.planStartTime || !formData.completeTime) return [];  // 改为completeTime

  const startDate = new Date(formData.planStartTime);
  const endDate = new Date(formData.completeTime);  // 改为completeTime

  // 如果是同一天、同一小时、同一分钟，禁用早于或等于开始时间的秒
  if (startDate.toDateString() === endDate.toDateString() &&
      hour === startDate.getHours() &&
      minute === startDate.getMinutes()) {
    const disabledSeconds = [];
    for (let i = 0; i <= startDate.getSeconds(); i++) {
      disabledSeconds.push(i);
    }
    return disabledSeconds;
  }
  return [];
};

// 获取计划编号
const loadPlanCode = async () => {
  console.log("🔢 开始获取计划编号...");
  try {
    // 调用后端API获取下一个可用的计划编号
    console.log("📡 调用 getNextPlanCode API...");
    const response = await getNextPlanCode();
    console.log("📩 API响应:", response);

    if (response && response.data) {
      formData.code = response.data;
      console.log("✅ 获取到计划编号:", response.data);
      console.log("📝 formData.code 已设置为:", formData.code);
    } else {
      console.warn("⚠️ API响应数据为空:", response);
      throw new Error("API响应数据为空");
    }
  } catch (error) {
    console.error("❌ 获取计划编号失败:", error);
    console.error("错误详情:", {
      message: error?.message,
      response: error?.response,
      status: error?.response?.status
    });

    // 使用前端生成递增编号的备用方案
    console.log("🔄 使用前端生成递增编号...");
    await generateFrontendPlanCode();
  }
};

// 前端生成递增计划编号的备用方案
const generateFrontendPlanCode = async () => {
  try {
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, "");
    const prefix = `PLAN-${today}-`;

    // 获取现有的生产计划列表来确定下一个序号
    console.log("📋 获取现有计划列表以确定序号...");
    const planListResponse = await getProductionPlanList({ current: 1, size: 999 });

    if (planListResponse && planListResponse.data && planListResponse.data.records) {
      const existingPlans = planListResponse.data.records;
      console.log("📊 现有计划数量:", existingPlans.length);

      // 找出今天的计划编号
      const todayPlans = existingPlans.filter(plan =>
        plan.planCode && plan.planCode.startsWith(prefix)
      );

      console.log("📅 今天的计划:", todayPlans.map(p => p.planCode));

      // 找出最大序号
      let maxSequence = 0;
      todayPlans.forEach(plan => {
        const match = plan.planCode.match(/PLAN-\d{8}-(\d{2})$/);
        if (match) {
          const sequence = parseInt(match[1], 10);
          if (sequence > maxSequence) {
            maxSequence = sequence;
          }
        }
      });

      // 生成下一个序号
      const nextSequence = maxSequence + 1;
      const planCode = prefix + String(nextSequence).padStart(2, '0');

      formData.code = planCode;
      console.log("✅ 前端生成计划编号成功:", planCode);

    } else {
      // 如果无法获取计划列表，使用默认编号
      const defaultCode = prefix + "01";
      formData.code = defaultCode;
      console.log("🔄 使用默认编号:", defaultCode);
    }
  } catch (error) {
    console.error("❌ 前端生成编号也失败:", error);
    // 最后的备用方案
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, "");
    const fallbackCode = `PLAN-${today}-01`;
    formData.code = fallbackCode;
    console.log("🆘 使用最终备用编号:", fallbackCode);
  }
};

// 页面加载时获取数据
onMounted(async () => {
  // 优先获取计划编号，让用户立即看到
  await loadPlanCode();

  // 并行加载其他数据
  loadMaterials();
  loadProducts();
  loadProductionLines();
});
</script>

<style scoped>
.production-plan-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
}

.page-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  color: #667eea;
  font-size: 32px;
}

.title-text {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.cancel-btn {
  height: 44px;
  padding: 0 20px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 24px;
}

.form-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.form-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-icon {
  color: #667eea;
  font-size: 22px;
}

.form-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.modern-form {
  padding: 32px;
}

/* 表单分组样式 */
.form-section-group {
  margin-bottom: 32px;
  padding: 24px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.section-icon {
  color: #667eea;
  font-size: 18px;
}

/* 表单项样式 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:hover) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

:deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}



.option-desc {
  font-size: 12px;
  color: #6b7280;
}

/* 操作按钮样式 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  margin-top: 32px;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 0 32px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.cancel-btn {
  border-radius: 8px;
  padding: 0 32px;
  height: 48px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .production-plan-container {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .modern-form {
    padding: 20px;
  }

  .form-section-group {
    padding: 16px;
    margin-bottom: 20px;
  }

  .form-actions {
    flex-direction: column;
  }

  .submit-btn, .cancel-btn {
    width: 100%;
  }
}

/* 原料配置区域样式 */
.materials-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  max-height: 400px; /* 限制最大高度 */
  overflow-y: auto; /* 超出时显示滚动条 */
}

/* 原料表格样式优化 */
.materials-section .el-table {
  font-size: 12px; /* 减小字体大小 */
}

.materials-section .el-table th,
.materials-section .el-table td {
  padding: 8px 4px; /* 减小单元格内边距 */
}

.materials-section .el-input-number {
  width: 100% !important;
}

.materials-section .el-input {
  font-size: 12px;
}

.materials-section .el-button--small {
  padding: 4px 8px;
  font-size: 11px;
}

.materials-header {
  margin-bottom: 16px;
}

.material-dialog-content {
  max-height: 400px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 状态选择器选项样式 */
.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.option-desc {
  color: #666;
  font-size: 12px;
}
</style>
