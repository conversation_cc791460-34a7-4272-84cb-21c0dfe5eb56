package com.geek.factory.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description 设备维修记录日志DTO
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@ApiModel(value = "DeviceRepairLogDTO", description = "设备维修记录日志DTO")
public class DeviceRepairLogDTO {

    @ApiModelProperty(value = "记录ID")
    private Long id;

    @ApiModelProperty(value = "关联的维修记录ID")
    private Long repairId;

    @ApiModelProperty(value = "产线ID")
    private Long productLineId;

    @ApiModelProperty(value = "产线名称")
    private String productLineName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作员")
    private String operator;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
}
