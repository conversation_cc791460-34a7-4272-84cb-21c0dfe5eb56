package com.geek.factory.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.entity.Material;
import com.geek.factory.vo.query.MaterialQueryVO;

import java.util.List;

/**
 * @Description 原料服务接口
 */
public interface MaterialService extends IService<Material> {

    /**
     * 分页查询原料列表
     * @param queryVO 查询条件
     * @return 分页结果
     */
    IPage<Material> selectMaterialPage(MaterialQueryVO queryVO);

    /**
     * 新增原料
     * @param material 原料信息
     * @return 是否成功
     */
    boolean addMaterial(Material material);

    /**
     * 更新原料
     * @param material 原料信息
     * @return 是否成功
     */
    boolean updateMaterial(Material material);

    /**
     * 删除原料
     * @param id 原料ID
     * @return 是否成功
     */
    boolean deleteMaterial(Integer id);

    /**
     * 批量删除原料
     * @param ids 原料ID列表
     * @return 是否成功
     */
    boolean deleteMaterialBatch(List<Integer> ids);

    /**
     * 根据类型查询原料列表
     * @param type 原料类型
     * @return 原料列表
     */
    List<Material> getMaterialsByType(String type);

    /**
     * 根据价格区间查询原料
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return 原料列表
     */
    List<Material> getMaterialsByPriceRange(Double minPrice, Double maxPrice);

    /**
     * 获取原料类型统计
     * @return 统计结果
     */
    List<Material> getMaterialTypeStatistics();
}
