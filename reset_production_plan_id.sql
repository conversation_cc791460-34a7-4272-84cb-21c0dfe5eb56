-- 重置生产计划表ID从15开始递增
-- 注意：这个操作会影响现有数据，请谨慎执行

USE factory;

-- 1. 检查当前表结构和数据
DESCRIBE production_plan;

-- 2. 查看当前数据统计
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 3. 显示最近的几条记录
SELECT 
    id, 
    plan_code, 
    status, 
    create_time 
FROM production_plan 
ORDER BY create_time DESC 
LIMIT 10;

-- 4. 首先确保表结构正确（ID字段是AUTO_INCREMENT）
ALTER TABLE production_plan MODIFY COLUMN id bigint NOT NULL AUTO_INCREMENT;

-- 5. 方案A：如果要保留现有数据，只重置AUTO_INCREMENT起始值
-- 这会让新创建的记录从15开始，但现有记录保持不变
ALTER TABLE production_plan AUTO_INCREMENT = 15;

-- 6. 方案B：如果要完全重置（删除所有数据，从15开始）
-- 警告：这会删除所有现有数据！
-- TRUNCATE TABLE production_plan;
-- ALTER TABLE production_plan AUTO_INCREMENT = 15;

-- 7. 方案C：重新映射现有数据的ID（复杂但保留数据）
-- 创建临时表
-- CREATE TABLE production_plan_temp LIKE production_plan;
-- ALTER TABLE production_plan_temp AUTO_INCREMENT = 15;

-- 插入数据（会自动分配新的ID）
-- INSERT INTO production_plan_temp (plan_code, product_id, product_line_id, quantity, plan_start_time, complete_time, status, create_time, update_time, is_in_stock, priority, remarks)
-- SELECT plan_code, product_id, product_line_id, quantity, plan_start_time, complete_time, status, create_time, update_time, is_in_stock, priority, remarks
-- FROM production_plan
-- ORDER BY create_time;

-- 替换原表
-- DROP TABLE production_plan;
-- RENAME TABLE production_plan_temp TO production_plan;

-- 8. 验证结果
SELECT 
    COUNT(*) as total_records,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM production_plan;

-- 9. 显示AUTO_INCREMENT设置
SHOW CREATE TABLE production_plan;

SELECT '✅ 生产计划表ID重置完成！下一个ID将从15开始' as status;
