package com.geek.factory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geek.factory.entity.ProductionLine;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @Description 产线数据访问层
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Mapper
public interface ProductionLineMapper extends BaseMapper<ProductionLine> {

    /**
     * 根据产线ID获取产线名称
     *
     * @param productLineId 产线ID
     * @return 产线名称
     */
    @Select("SELECT name FROM production_line WHERE id = #{productLineId}")
    String getNameById(@Param("productLineId") Long productLineId);
}
