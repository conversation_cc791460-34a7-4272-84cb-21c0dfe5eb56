import { http } from "@/utils/http";

// 原料类型定义（匹配后端Material实体）
export interface Material {
  id?: number;         // Long -> number (前端处理)
  code: string;        // 原料编号
  name: string;        // 原料名称（含规格）
  type: string;        // 原料类型
  unit: string;        // 计量单位
  price: number;       // 原料价格 (Double -> number)
  quantity?: number;   // 库存数量 (BigDecimal -> number)
  pic?: string;        // 图片链接（前端扩展字段）
  createTime?: string; // 创建时间（前端扩展字段）
  updateTime?: string; // 更新时间（前端扩展字段）
}

// 扩展的原料类型（用于原料选择对话框）
export interface MaterialWithTemp extends Material {
  tempQuantity?: number; // 临时数量字段
  tempRemark?: string;   // 临时备注字段
}

// 原料查询参数
export interface MaterialQueryVO {
  name?: string;
  type?: string;
  unit?: string;
  minPrice?: number;
  maxPrice?: number;
  current?: number;
  size?: number;
}

// 原料使用量类型定义（对应后端MaterialUsageDTO）
export interface MaterialUsageDTO {
  materialId: number;
  materialName?: string;
  requiredQuantity: number;
  unit?: string;
  remark?: string;
}

// 统一结果集类型
export interface Result<T = any> {
  success: boolean;
  msg: string;
  data: T;
}

// 分页结果类型
export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
}

/**
 * 分页查询原料列表（匹配后端 MaterialController）
 */
export const getMaterialPage = (params: MaterialQueryVO) => {
  // 转换参数格式以匹配后端期望的Map<String, Object>
  const requestParams = {
    current: params.current || 1,
    size: params.size || 10,
    keyword: params.name || params.type || undefined
  };

  return http.request<Result<PageResult<Material>>>("post", "/material/list", {
    data: requestParams
  });
};

/**
 * 获取所有原料列表（不分页）- 使用分页接口获取大量数据
 */
export const getAllMaterials = () => {
  const requestParams = {
    current: 1,
    size: 1000  // 获取大量数据
  };

  return http.request<Result<PageResult<Material>>>("post", "/api/material/list", {
    data: requestParams
  });
};

/**
 * 根据ID查询原料详情
 */
export const getMaterialById = (id: number) => {
  return http.request<Result<Material>>("get", `/api/materialx/${id}`);
};

/**
 * 新增原料
 */
export const addMaterial = (data: Material) => {
  return http.request<Result>("post", "/api/materialx/create", { data });
};

/**
 * 更新原料
 */
export const updateMaterial = (data: Material) => {
  return http.request<Result>("put", "/api/materialx/update", { data });
};

/**
 * 删除原料
 */
export const deleteMaterial = (id: number) => {
  return http.request<Result>("delete", `/api/materialx/delete/${id}`);
};

/**
 * 批量删除原料
 */
export const deleteMaterialBatch = (ids: number[]) => {
  return http.request<Result>("delete", "/api/materialx/delete/batch", { data: ids });
};

/**
 * 验证原料库存是否足够
 */
export const validateMaterialStock = (materials: MaterialUsageDTO[]) => {
  return http.request<Result>("post", "/api/material/stock", {
    data: materials
  });
};
