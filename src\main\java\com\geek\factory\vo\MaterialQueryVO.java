package com.geek.factory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 原料查询VO
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Data
@ApiModel(value = "MaterialQueryVO", description = "原料查询参数")
public class MaterialQueryVO {

    @ApiModelProperty(value = "原料名称", example = "硅晶圆")
    private String name;

    @ApiModelProperty(value = "原料类型", example = "半导体材料")
    private String type;

    @ApiModelProperty(value = "计量单位", example = "片")
    private String unit;

    @ApiModelProperty(value = "最小价格", example = "50.00")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最大价格", example = "500.00")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer current = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer size = 10;

    /**
     * 验证分页参数
     */
    public void validatePageParams() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100;
        }
    }
}
