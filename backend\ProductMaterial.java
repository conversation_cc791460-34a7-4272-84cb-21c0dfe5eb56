package com.geek.factory.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description 产品原料关联实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product_materials")
@ApiModel(value = "ProductMaterial对象", description = "产品原料关联管理")
public class ProductMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    private Integer productId;

    @ApiModelProperty(value = "原料ID")
    @TableField("material_id")
    private Integer materialId;

    @ApiModelProperty(value = "用量")
    @TableField("quantity")
    private BigDecimal quantity;

    @ApiModelProperty(value = "单位")
    @TableField("unit")
    private String unit;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
