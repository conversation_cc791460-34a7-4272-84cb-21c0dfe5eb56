# 🏭 生产计划原料消耗功能使用指南

## 🚨 问题解决
✅ **表名问题已修复**：后端使用的表名是 `production_plan_material`（单数），已更新所有代码和脚本。

根据日志显示，查询生产计划ID `1941332966093598700` 的原料消耗返回了0条记录，这是因为数据库中没有对应的原料关联数据。

## 🔧 快速修复步骤

### 1. 执行快速修复脚本（推荐）
```bash
# 连接到MySQL数据库
mysql -u root -p factory

# 执行快速修复脚本（专门针对日志中的生产计划ID）
source backend/quick_fix.sql;
```

### 2. 或者执行完整修复脚本
```bash
# 执行完整的验证和修复脚本
source backend/verify_and_fix.sql;
```

### 3. 验证数据是否插入成功
```sql
-- 查看插入的测试数据
SELECT COUNT(*) FROM production_plan_material;

-- 查看具体数据
SELECT 
    ppm.plan_id,
    pp.plan_code,
    m.name as material_name,
    ppm.required_quantity,
    ppm.unit
FROM production_plan_material ppm
LEFT JOIN production_plan pp ON ppm.plan_id = pp.id
LEFT JOIN materials m ON ppm.material_id = m.id
LIMIT 10;
```

### 4. 测试API
```bash
# 获取一个有数据的生产计划ID
PLAN_ID=$(mysql -u root -p factory -se "SELECT plan_id FROM production_plan_material LIMIT 1")

# 测试API调用
curl -X GET "http://localhost:8080/api/plan/materials/$PLAN_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 API响应示例

### 成功响应
```json
{
  "success": true,
  "msg": "查询成功",
  "data": [
    {
      "materialId": 16,
      "materialName": "陶瓷电容 10μF 50V",
      "unit": "个",
      "requiredQuantity": 20.00,
      "remark": "用于生产计划 1941332966093598700"
    },
    {
      "materialId": 17,
      "materialName": "铝电解电容 100μF 25V",
      "unit": "个",
      "requiredQuantity": 15.00,
      "remark": "用于生产计划 1941332966093598700"
    }
  ]
}
```

### 空数据响应
```json
{
  "success": true,
  "msg": "查询成功",
  "data": []
}
```

## 🎯 前端页面测试

1. **访问原料消耗页面**
   - URL: `http://localhost:3000/production/plan/material-consume/[PLAN_ID]`
   - 替换 `[PLAN_ID]` 为实际的生产计划ID

2. **预期结果**
   - 如果有数据：显示真实的原料消耗信息和图表
   - 如果无数据：显示警告消息并使用模拟数据

## 🔄 完整工作流程

### 创建生产计划时自动保存原料关联
当用户创建生产计划并选择原料时，系统会：

1. **保存生产计划基本信息**
2. **扣减原料库存**
3. **保存原料关联信息** ← 新增功能
   ```java
   // 在 ProductionPlanServiceImpl.createPlan() 中
   savePlanMaterials(plan.getId(), dto.getMaterials());
   ```

### 查询原料消耗详情
用户点击"原料消耗"按钮时：

1. **前端调用API**: `getPlanMaterials(planId)`
2. **后端查询数据库**: 
   ```sql
   SELECT ppm.material_id, m.name, ppm.unit, ppm.required_quantity, ppm.remark
   FROM production_plan_material ppm
   LEFT JOIN materials m ON ppm.material_id = m.id
   WHERE ppm.plan_id = ?
   ```
3. **返回结果**: 原料消耗详情列表

## 🐛 故障排除

### 问题1: API返回空数据
**原因**: 数据库中没有原料关联数据
**解决**: 执行 `backend/verify_and_fix.sql` 脚本

### 问题2: 表不存在错误
**原因**: 没有创建 `production_plan_material` 表
**解决**: 执行 `backend/database.sql` 或 `backend/update_material_stock.sql`

### 问题3: 外键约束错误
**原因**: 引用的生产计划ID或原料ID不存在
**解决**: 确保使用存在的ID，或者删除外键约束

## 📝 开发说明

### 后端关键文件
- `ProductionPlanController.java` - API端点
- `ProductionPlanServiceImpl.java` - 业务逻辑
- `ProductionPlanMaterialMapper.java` - 数据访问
- `ProductionPlanMaterial.java` - 实体类

### 前端关键文件
- `src/api/production.ts` - API调用
- `src/views/production/plan/material-consume.vue` - 页面组件

### 数据库表结构
```sql
CREATE TABLE `production_plan_material` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `plan_id` bigint NOT NULL,
  `material_id` bigint NOT NULL,
  `required_quantity` decimal(10,2) NOT NULL,
  `unit` varchar(20) NOT NULL,
  `remark` varchar(500) DEFAULT NULL,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

## ✅ 验证清单

- [ ] 数据库表 `production_plan_material` 已创建
- [ ] 测试数据已插入
- [ ] API `/api/plan/materials/{planId}` 返回正确数据
- [ ] 前端页面显示真实数据而非模拟数据
- [ ] 创建新生产计划时自动保存原料关联信息
