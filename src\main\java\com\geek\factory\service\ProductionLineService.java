package com.geek.factory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geek.factory.entity.ProductionLine;

import java.util.List;

/**
 * @Description 产线服务接口
 * <AUTHOR>
 * @Date 2024-01-01
 */
public interface ProductionLineService extends IService<ProductionLine> {

    /**
     * 获取所有产线列表
     * @return 产线列表
     */
    List<ProductionLine> getAllProductionLines();

    /**
     * 根据ID获取产线信息
     * @param id 产线ID
     * @return 产线信息
     */
    ProductionLine getProductionLineById(Long id);
}
