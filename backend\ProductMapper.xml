<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geek.factory.mapper.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geek.factory.entity.Product">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="model" property="model" />
        <result column="category" property="category" />
        <result column="price" property="price" />
        <result column="description" property="description" />
        <result column="pic" property="pic" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, model, category, price, description, pic
    </sql>

    <!-- 分页查询产品列表 -->
    <select id="selectProductPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM products
        <where>
            <if test="query.name != null and query.name != ''">
                AND name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.model != null and query.model != ''">
                AND model LIKE CONCAT('%', #{query.model}, '%')
            </if>
            <if test="query.category != null and query.category != ''">
                AND category = #{query.category}
            </if>
            <if test="query.minPrice != null">
                AND price >= #{query.minPrice}
            </if>
            <if test="query.maxPrice != null">
                AND price &lt;= #{query.maxPrice}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据分类查询产品列表 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM products
        WHERE category = #{category}
        ORDER BY id DESC
    </select>

    <!-- 根据价格区间查询产品 -->
    <select id="selectByPriceRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM products
        WHERE price BETWEEN #{minPrice} AND #{maxPrice}
        ORDER BY price ASC
    </select>

    <!-- 统计各分类产品数量 -->
    <select id="selectCategoryStatistics" resultType="java.util.Map">
        SELECT 
            category,
            COUNT(*) as count,
            AVG(price) as avgPrice,
            MIN(price) as minPrice,
            MAX(price) as maxPrice
        FROM products
        GROUP BY category
        ORDER BY count DESC
    </select>

</mapper>
