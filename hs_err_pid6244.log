#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=6244, tid=16744
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\267fbbbe572ec5a0c4c37b729457c0c3\redhat.java\ss_ws --pipe=\\.\pipe\lsp-dfbf1ffc9396c46b216c56729a101e23-sock

Host: 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1591)
Time: Tue Jun 24 18:03:06 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.1591) elapsed time: 0.241363 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000025ea5dfb500):  JavaThread "Unknown thread" [_thread_in_vm, id=16744, stack(0x000000a89b400000,0x000000a89b500000) (1024K)]

Stack: [0x000000a89b400000,0x000000a89b500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xbfba7]
V  [jvm.dll+0x70b72d]
V  [jvm.dll+0x70bdec]
V  [jvm.dll+0x6dcc68]
V  [jvm.dll+0x871dbc]
V  [jvm.dll+0x3bc47c]
V  [jvm.dll+0x85a848]
V  [jvm.dll+0x45080e]
V  [jvm.dll+0x452451]
C  [jli.dll+0x5278]
C  [ucrtbase.dll+0x14ea0]
C  [KERNEL32.DLL+0x2dbe7]
C  [ntdll.dll+0x85a6c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000025ea8224a00, length=1, elements={
0x0000025ea5dfb500
}

Java Threads: ( => current thread )
=>0x0000025ea5dfb500 JavaThread "Unknown thread"             [_thread_in_vm, id=16744, stack(0x000000a89b400000,0x000000a89b500000) (1024K)]
Total: 1

Other Threads:
  0x0000025ea826cf90 WatcherThread "VM Periodic Task Thread"        [id=28076, stack(0x000000a89b600000,0x000000a89b700000) (1024K)]
  0x0000025ea821c880 WorkerThread "GC Thread#0"                     [id=4248, stack(0x000000a89b500000,0x000000a89b600000) (1024K)]
Total: 2

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000025ebd000000-0x0000025ebdba0000-0x0000025ebdba0000), size 12189696, SharedBaseAddress: 0x0000025ebd000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000025ebe000000-0x0000025efe000000, reserved size: 1073741824
Narrow klass base: 0x0000025ebd000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 16 total, 16 available
 Memory: 16151M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 13

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000eab00000,0x00000000eab80020,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 0K, committed 0K, reserved 1048576K
  class space    used 0K, committed 0K, reserved 1048576K

Card table byte_map: [0x0000025ea7ba0000,0x0000025ea7db0000] _byte_map_base: 0x0000025ea75a0000

Marking Bits: (ParMarkBitMap*) 0x00007ff8f3d031f0
 Begin Bits: [0x0000025eba3f0000, 0x0000025ebb3f0000)
 End Bits:   [0x0000025ebb3f0000, 0x0000025ebc3f0000)

Polling page: 0x0000025ea60f0000

Metaspace:

Usage:
  Non-class:      0 bytes used.
      Class:      0 bytes used.
       Both:      0 bytes used.

Virtual space:
  Non-class space:        0 bytes reserved,       0 bytes (  ?%) committed,  0 nodes.
      Class space:        1.00 GB reserved,       0 bytes (  0%) committed,  1 nodes.
             Both:        1.00 GB reserved,       0 bytes (  0%) committed. 

Chunk freelists:
   Non-Class:  0 bytes
       Class:  16.00 MB
        Both:  16.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 0.
num_arena_deaths: 0.
num_vsnodes_births: 1.
num_vsnodes_deaths: 0.
num_space_committed: 0.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1.
num_chunk_merges: 0.
num_chunk_splits: 1.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=0Kb max_used=0Kb free=119168Kb
 bounds [0x0000025eb2f90000, 0x0000025eb3200000, 0x0000025eba3f0000]
CodeHeap 'profiled nmethods': size=119104Kb used=0Kb max_used=0Kb free=119104Kb
 bounds [0x0000025eab3f0000, 0x0000025eab660000, 0x0000025eb2840000]
CodeHeap 'non-nmethods': size=7488Kb used=199Kb max_used=348Kb free=7288Kb
 bounds [0x0000025eb2840000, 0x0000025eb2ab0000, 0x0000025eb2f90000]
 total_blobs=70 nmethods=0 adapters=48
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.036 Loaded shared library c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (1 events):
Event: 0.073 Thread 0x0000025ea5dfb500 Thread added: 0x0000025ea5dfb500


Dynamic libraries:
0x00007ff6ce740000 - 0x00007ff6ce74e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff9f6b20000 - 0x00007ff9f6d83000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff9f5ca0000 - 0x00007ff9f5d67000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff9f4530000 - 0x00007ff9f48d1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff9f3f30000 - 0x00007ff9f407b000 	C:\Windows\System32\ucrtbase.dll
0x00007ff9ced90000 - 0x00007ff9ceda8000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff9c8f20000 - 0x00007ff9c8f3e000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff9f6910000 - 0x00007ff9f6ad3000 	C:\Windows\System32\USER32.dll
0x00007ff9f4230000 - 0x00007ff9f4257000 	C:\Windows\System32\win32u.dll
0x00007ff9f59f0000 - 0x00007ff9f5a1a000 	C:\Windows\System32\GDI32.dll
0x00007ff9f4080000 - 0x00007ff9f41a1000 	C:\Windows\System32\gdi32full.dll
0x00007ff9f4480000 - 0x00007ff9f4523000 	C:\Windows\System32\msvcp_win.dll
0x00007ff9f3350000 - 0x00007ff9f35e0000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1591_none_3e0fac18e32dc903\COMCTL32.dll
0x00007ff9f4a80000 - 0x00007ff9f4b29000 	C:\Windows\System32\msvcrt.dll
0x00007ff9f68c0000 - 0x00007ff9f68ef000 	C:\Windows\System32\IMM32.DLL
0x00007ff9e7c30000 - 0x00007ff9e7c3c000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff93aa60000 - 0x00007ff93aaed000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff8f3050000 - 0x00007ff8f3de0000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff9f4b40000 - 0x00007ff9f4bf2000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff9f5490000 - 0x00007ff9f5537000 	C:\Windows\System32\sechost.dll
0x00007ff9f5540000 - 0x00007ff9f5656000 	C:\Windows\System32\RPCRT4.dll
0x00007ff9f5d70000 - 0x00007ff9f5de4000 	C:\Windows\System32\WS2_32.dll
0x00007ff9f3dd0000 - 0x00007ff9f3e1e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff9e9ec0000 - 0x00007ff9e9ecb000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff9df950000 - 0x00007ff9df986000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff9f3db0000 - 0x00007ff9f3dc4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff9f2400000 - 0x00007ff9f241a000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff9e5e20000 - 0x00007ff9e5e2a000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff9f3700000 - 0x00007ff9f3941000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff9f5e60000 - 0x00007ff9f61dc000 	C:\Windows\System32\combase.dll
0x00007ff9f61e0000 - 0x00007ff9f62b6000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff9f35f0000 - 0x00007ff9f3629000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff9f43e0000 - 0x00007ff9f4479000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff9e5a30000 - 0x00007ff9e5a3f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff9c1540000 - 0x00007ff9c155f000 	c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1591_none_3e0fac18e32dc903;c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\267fbbbe572ec5a0c4c37b729457c0c3\redhat.java\ss_ws --pipe=\\.\pipe\lsp-dfbf1ffc9396c46b216c56729a101e23-sock
java_class_path (initial): c:\Users\<USER>\.cursor\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk1.8
CLASSPATH=,;
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\jdk1.8\bin;D:\2502\Git\Git\cmd;D:\nvm110;C:\nvm4w\nodej\nodejs;D:\nvm\v20.9.0;D:\nvm\v20.9.0\node_cache;D:\nvm\v20.9.0\node_global;;D:\UltraEdit;C:\Program Files\IDM Computer Solutions\UltraCompare;C:\Program Files\Bandizip\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\VS Code\bin;D:\nvm110;C:\nvm4w\nodej\nodejs;D:\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 141 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1591)
OS uptime: 1 days 9:05 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 141 stepping 1 microcode 0x32, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 16 processors :
  Max Mhz: 2304, Current Mhz: 2304, Mhz Limit: 2304

Memory: 4k page, system-wide physical 16151M (1816M free)
TotalPageFile size 41599M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 26M, peak: 26M
current process commit charge ("private bytes"): 193M, peak: 194M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
