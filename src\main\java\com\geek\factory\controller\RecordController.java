package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geek.factory.constant.SuccessConstant;
import com.geek.factory.entity.Record;
import com.geek.factory.result.Result;
import com.geek.factory.service.RecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description 维修记录控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/dev/record")
@Api(tags = "设备维修记录单管理")
@CrossOrigin
public class RecordController {

    @Autowired
    private RecordService recordService;

    /**
     * 分页查询维修记录列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "分页查询维修记录列表")
    public Result getRecordList(@RequestBody Map<String, Object> params) {
        log.info("分页查询维修记录 - 参数: {}", params);
        try {
            // 获取分页参数
            Long current = params.get("current") != null ? Long.valueOf(params.get("current").toString()) : 1L;
            Long size = params.get("size") != null ? Long.valueOf(params.get("size").toString()) : 10L;

            Page<Record> page = new Page<>(current, size);
            LambdaQueryWrapper<Record> wrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (params.get("deviceName") != null && !params.get("deviceName").toString().trim().isEmpty()) {
                wrapper.like(Record::getDeviceName, params.get("deviceName").toString());
            }
            if (params.get("productlineId") != null) {
                wrapper.eq(Record::getProductlineId, Integer.valueOf(params.get("productlineId").toString()));
            }

            // 按ID降序排列
            wrapper.orderByDesc(Record::getId);

            Page<Record> result = recordService.page(page, wrapper);
            log.info("分页查询维修记录成功，共{}条记录", result.getTotal());
            return new Result(SuccessConstant.SUCCESS, "查询成功", result);
        } catch (Exception e) {
            log.error("分页查询维修记录失败", e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 根据ID查询维修记录
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID查询维修记录")
    public Result getRecordById(@PathVariable Integer id) {
        log.info("根据ID查询维修记录，ID：{}", id);
        try {
            Record record = recordService.getById(id);
            if (record != null) {
                log.info("查询维修记录成功");
                return new Result(SuccessConstant.SUCCESS, "查询成功", record);
            } else {
                log.warn("维修记录不存在，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "记录不存在", null);
            }
        } catch (Exception e) {
            log.error("查询维修记录失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 新增维修记录
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增维修记录")
    public Result createRecord(@RequestBody Record record) {
        log.info("新增维修记录，参数：{}", record);
        try {
            boolean success = recordService.save(record);
            if (success) {
                log.info("新增维修记录成功");
                return new Result(SuccessConstant.SUCCESS, "新增成功", null);
            } else {
                log.warn("新增维修记录失败");
                return new Result(SuccessConstant.FAILED, "新增失败", null);
            }
        } catch (Exception e) {
            log.error("新增维修记录失败", e);
            return new Result(SuccessConstant.FAILED, "新增失败", null);
        }
    }

    /**
     * 更新维修记录
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新维修记录")
    public Result updateRecord(@RequestBody Record record) {
        log.info("更新维修记录，参数：{}", record);
        try {
            boolean success = recordService.updateById(record);
            if (success) {
                log.info("更新维修记录成功，ID：{}", record.getId());
                return new Result(SuccessConstant.SUCCESS, "更新成功", null);
            } else {
                log.warn("更新维修记录失败");
                return new Result(SuccessConstant.FAILED, "更新失败", null);
            }
        } catch (Exception e) {
            log.error("更新维修记录失败", e);
            return new Result(SuccessConstant.FAILED, "更新失败", null);
        }
    }

    /**
     * 删除维修记录
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除维修记录")
    public Result deleteRecord(@PathVariable Integer id) {
        log.info("删除维修记录，ID：{}", id);
        try {
            boolean success = recordService.removeById(id);
            if (success) {
                log.info("删除维修记录成功，ID：{}", id);
                return new Result(SuccessConstant.SUCCESS, "删除成功", null);
            } else {
                log.warn("删除维修记录失败，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "删除失败", null);
            }
        } catch (Exception e) {
            log.error("删除维修记录失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "删除失败", null);
        }
    }
}
