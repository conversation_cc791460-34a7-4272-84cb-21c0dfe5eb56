package com.geek.factory.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.geek.factory.constant.SuccessConstant;
import com.geek.factory.entity.Device;
import com.geek.factory.result.Result;
import com.geek.factory.service.DeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description 设备管理控制器
 * <AUTHOR>
 * @Date 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/device")
@Api(tags = "设备管理")
@CrossOrigin
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    /**
     * 获取设备列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取设备列表", notes = "可根据产线ID过滤设备")
    public Result getDeviceList(
            @ApiParam(value = "产线ID，可选") @RequestParam(required = false) Integer productiveId) {
        log.info("获取设备列表，产线ID：{}", productiveId);
        try {
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            if (productiveId != null) {
                wrapper.eq(Device::getProductiveId, productiveId);
            }
            wrapper.orderByDesc(Device::getId);
            
            List<Device> devices = deviceService.list(wrapper);
            log.info("获取设备列表成功，共{}条记录", devices.size());
            return new Result(SuccessConstant.SUCCESS, "查询成功", devices);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 根据ID获取设备信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取设备信息")
    public Result getDeviceById(@PathVariable Integer id) {
        log.info("根据ID获取设备信息，ID：{}", id);
        try {
            Device device = deviceService.getById(id);
            if (device != null) {
                log.info("获取设备信息成功，设备名称：{}", device.getName());
                return new Result(SuccessConstant.SUCCESS, "查询成功", device);
            } else {
                log.warn("设备不存在，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "设备不存在", null);
            }
        } catch (Exception e) {
            log.error("获取设备信息失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "查询失败", null);
        }
    }

    /**
     * 新增设备
     */
    @PostMapping
    @ApiOperation(value = "新增设备")
    public Result addDevice(@RequestBody Device device) {
        log.info("新增设备，参数：{}", device);
        try {
            boolean success = deviceService.save(device);
            if (success) {
                log.info("新增设备成功，设备名称：{}", device.getName());
                return new Result(SuccessConstant.SUCCESS, "新增成功", null);
            } else {
                log.warn("新增设备失败");
                return new Result(SuccessConstant.FAILED, "新增失败", null);
            }
        } catch (Exception e) {
            log.error("新增设备失败", e);
            return new Result(SuccessConstant.FAILED, "新增失败", null);
        }
    }

    /**
     * 修改设备
     */
    @PutMapping
    @ApiOperation(value = "修改设备")
    public Result updateDevice(@RequestBody Device device) {
        log.info("修改设备，参数：{}", device);
        try {
            boolean success = deviceService.updateById(device);
            if (success) {
                log.info("修改设备成功，设备ID：{}", device.getId());
                return new Result(SuccessConstant.SUCCESS, "修改成功", null);
            } else {
                log.warn("修改设备失败");
                return new Result(SuccessConstant.FAILED, "修改失败", null);
            }
        } catch (Exception e) {
            log.error("修改设备失败", e);
            return new Result(SuccessConstant.FAILED, "修改失败", null);
        }
    }

    /**
     * 根据ID删除设备
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据ID删除设备")
    public Result deleteDevice(@PathVariable Integer id) {
        log.info("删除设备，ID：{}", id);
        try {
            boolean success = deviceService.removeById(id);
            if (success) {
                log.info("删除设备成功，ID：{}", id);
                return new Result(SuccessConstant.SUCCESS, "删除成功", null);
            } else {
                log.warn("删除设备失败，ID：{}", id);
                return new Result(SuccessConstant.FAILED, "删除失败", null);
            }
        } catch (Exception e) {
            log.error("删除设备失败，ID：{}", id, e);
            return new Result(SuccessConstant.FAILED, "删除失败", null);
        }
    }
}
